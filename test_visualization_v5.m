%% 测试v5版本可视化功能
clear all; clc; close all;

fprintf('=== 测试v5版本可视化功能 ===\n');

% 模拟数据
fprintf('1. 准备测试数据...\n');

% 模拟决策变量
X_test = rand(1, 403);
X_test(1:2) = [10, 15]; % 储能节点
X_test(3:4) = [1000, 800]; % 储能容量 (kWh)
X_test(5:6) = [4, 5]; % 小时比

% 模拟ESS_params
ESS_params.Ce = 480;
ESS_params.Cp = 240;
ESS_params.E_max = 5000;
ESS_params.P_max = 1200;

% 模拟Pareto解集和前沿
n_solutions = 8;
ps_test = rand(n_solutions, 403);
ps_test(:,1:2) = randi([2, 33], n_solutions, 2); % 储能节点
ps_test(:,3:4) = 500 + rand(n_solutions, 2) * 2000; % 储能容量
ps_test(:,5:6) = 2 + rand(n_solutions, 2) * 6; % 小时比

% 模拟目标函数值 (模拟真实的成本范围)
f1_base = 600000 + rand(n_solutions, 1) * 300000; % 储能全寿命周期成本 60-90万元
f2_base = 570000 + rand(n_solutions, 1) * 50000;  % 系统运行成本 57-62万元
pf_test = [f1_base, f2_base];

% 归一化
f1_min = min(pf_test(:,1)); f1_max = max(pf_test(:,1));
f2_min = min(pf_test(:,2)); f2_max = max(pf_test(:,2));
pf_norm_test = [(pf_test(:,1) - f1_min) / (f1_max - f1_min), ...
                (pf_test(:,2) - f2_min) / (f2_max - f2_min)];

% 选择最优解
distances = sqrt(pf_norm_test(:,1).^2 + pf_norm_test(:,2).^2);
[~, best_idx] = min(distances);
best_solution = ps_test(best_idx, :);

fprintf('   测试数据准备完成！\n');
fprintf('   Pareto解数量: %d\n', n_solutions);
fprintf('   最优解索引: %d\n', best_idx);

% 2. 测试目标函数和cost_details
fprintf('\n2. 测试目标函数计算...\n');
try
    [f1, f2, cost_details] = ESS_objective_v5(best_solution);
    fprintf('   ✓ 目标函数计算成功\n');
    fprintf('   储能全寿命周期成本: %.2f 万元\n', f1/10000);
    fprintf('   系统运行成本: %.2f 万元\n', f2/10000);
    
    if isfield(cost_details, 'curtail_cost_rate')
        fprintf('   弃风弃光成本费率: %.1f 元/kWh\n', cost_details.curtail_cost_rate);
    end
    
catch ME
    fprintf('   ✗ 目标函数计算失败: %s\n', ME.message);
    return;
end

% 3. 测试调度结果和储能充放电可视化
fprintf('\n3. 测试调度结果可视化...\n');
try
    plot_dispatch_results_v5(best_solution, cost_details, ESS_params);
    fprintf('   ✓ 调度结果可视化成功！\n');
    fprintf('   已生成两个独立图形：\n');
    fprintf('     - 图1: 储能风光协调调度结果 (储能放电在上层)\n');
    fprintf('     - 图2: 储能充放电功率柱形图\n');
    
catch ME
    fprintf('   ✗ 调度结果可视化失败: %s\n', ME.message);
    fprintf('   错误详情: %s\n', ME.getReport);
end

% 4. 测试Pareto前沿可视化
fprintf('\n4. 测试Pareto前沿可视化...\n');
try
    plot_pareto_results_v5(ps_test, pf_test, pf_norm_test, best_solution, best_idx);
    fprintf('   ✓ Pareto前沿可视化成功！\n');
    fprintf('   已生成三个图形：\n');
    fprintf('     - 图1: Pareto解集散点图\n');
    fprintf('     - 图2: 归一化Pareto前沿图\n');
    fprintf('     - 图3: 对比分析图\n');
    
catch ME
    fprintf('   ✗ Pareto前沿可视化失败: %s\n', ME.message);
    fprintf('   错误详情: %s\n', ME.getReport);
end

fprintf('\n=== 可视化功能测试完成 ===\n');
fprintf('如果所有测试都通过，说明可视化功能修改成功！\n');
fprintf('请检查生成的图形窗口，确认：\n');
fprintf('1. 调度结果图中储能放电柱形在购电/风光柱形上方\n');
fprintf('2. 储能充放电功率使用柱形图显示\n');
fprintf('3. Pareto解集图类似CMOPSO-MSI vs MOPSO样式\n');
fprintf('4. Pareto前沿图类似True vs Approximate样式\n'); 