% function: 计算网损
function [ loss ] = Loss( n,U,W,cita,branch);

    branch(:,3) = branch(:,3)+1j*branch(:,4);
    branch(:,4) = [];
    Y=zeros(n); %新建节点导纳矩阵
    y=zeros(n); %网络中的真实导纳
    %计算y(i,j)
    for i=1:size(branch,1) %与交流线联结的真实导纳
        ii=branch(i,1); jj=branch(i,2);
        y(ii,jj)=1/branch(i,3);
        y(jj,ii)=y(ii,jj);
    end
    %计算y(i,i),接地
    % for i=1:size(branch,1) %与交流线联结的对地导纳
    %     ii=branch(i,1); jj=branch(i,2);
    %     y(ii,ii)=y(ii,ii);
    %     y(jj,jj)=y(jj,jj);
    % end
    
    %由y计算Y
    ysum=sum(y,2); %每一行求和
    for i=1:n
        for j=1:n
            if i==j
                Y(i,j)=ysum(i);
            else
                Y(i,j)=-y(i,j);
            end
        end
    end
    
    
    G=real(Y); %电导矩阵
    B=imag(Y); %电纳矩阵
    
    % 创建一个33x33的关联矩阵T，描述节点之间是否存在联系
    T = zeros(33);
    % 根据支路数据设置U矩阵中的连接情况
    for i = 1:size(branch, 1)
        from_node = branch(i, 1);
        to_node = branch(i, 2);
        T(from_node, to_node) = 1;
        T(to_node, from_node) = 1;
        T(i,i)=1;
    end
    
    % 创建一个33x33的接线矩阵D，描述节点之间采用DC连接还是AC连接
    D = zeros(33);
    % 根据矩阵a中的连接方式设置B矩阵中的元素
    for i = 1:size(branch, 1)
        from_node = branch(i, 1);
        to_node = branch(i, 2);
        connection_type = branch(i, 4);
        D(from_node, to_node) = connection_type;
        D(to_node, from_node) = connection_type;
    end
    
    %生成系统中VSC调制矩阵M
    M=ones(33);
    L=find(branch(:, 5) ~= 0);
    for i = 1:size(L)
        from_node = branch(L(i), 1);
        to_node = branch(L(i), 2);
        M_type = branch(L(i), 5);
        M(from_node, to_node) = M_type;
        M(to_node, from_node) = M_type;
    end
    
    for b= 1:n
        from_node = branch(b, 1);
        to_node = branch(b, 2);
        i=from_node;
        j=to_node;
        a1 = 0.5 * (1 + sign(M(i, j).^-1 * U(i) - M(j, i).^-1 * U(j)));
        b1 = 0.5 * (1 - sign(M(i, j).^-1 * U(i) - M(j, i).^-1 * U(j)));
        a2 = 0.5 * (1 + sign(M(i, j).^-1 * U(i) -U(j)));
        b2 = 0.5 * (1 - sign(M(i, j).^-1 * U(i) -U(j)));
        Pij(b)=(1-W(i)) *(1- W(j)) * (1-D(i, j)) * ( U(i).^2*G(i, j)- U(i) * U(j) * (G(i, j) * cosd(cita(i)-cita(j)) + B(i, j) * sind(cita(i)-cita(j)))) ...
            + (1-W(i)) *(1- W(j))* D(i, j) * (G(i, j) * ( M(i, j).^-2 * U(i).^2- M(i, j).^-1 * U(i) * M(j, i).^-1 * U(j))) * (a1 * 0.95.^-1 + b1 * 0.95) ...
            + (1-W(i)) * W(j) * D(i, j) * (G(i, j) * ( M(i, j).^-2 * U(i).^2- M(i, j).^-1 * U(i) * U(j))) * (a2 * 0.95.^-1 + b2 * 0.95) ...
            + W(i) * (1- W(j)) * D(i, j) * (G(i, j) * ( U(i).^2- U(i) * M(j, i).^-1 * U(j))) ...
            + W(i) * W(j) * D(i, j) * (G(i, j) * (U(i).^2- U(i) * U(j)));
        Qij(b)=(1-W(i)) *(1- W(j)) * (1-D(i, j)) * (- U(i).^2*B(i, j)- U(i) * U(j) * (G(i, j) * sind(cita(i)-cita(j)) - B(i, j) * cosd(cita(i)-cita(j)))) ...
            + (1-W(i)) *(1- W(j))* D(i, j) *(G(i, j) * ( M(i, j).^-2 * U(i).^2- M(i, j).^-1 * U(i) * M(j, i).^-1 * U(j))) * (a1 * 0.95.^-1 + b1 * 0.95)*tan(acos(0.95)) ...
            + (1-W(i)) * W(j) * D(i, j) *(G(i, j) * ( M(i, j).^-2 * U(i).^2- M(i, j).^-1 * U(i) * U(j))) * (a2 * 0.95.^-1 + b2 * 0.95);
        
        j=from_node;
        i=to_node;
        a1 = 0.5 * (1 + sign(M(i, j).^-1 * U(i) - M(j, i).^-1 * U(j)));
        b1 = 0.5 * (1 - sign(M(i, j).^-1 * U(i) - M(j, i).^-1 * U(j)));
        a2 = 0.5 * (1 + sign(M(i, j).^-1 * U(i) -U(j)));
        b2 = 0.5 * (1 - sign(M(i, j).^-1 * U(i) -U(j)));
        Pji(b)=(1-W(i)) *(1- W(j)) * (1-D(i, j)) * ( U(i).^2*G(i, j)- U(i) * U(j) * (G(i, j) * cosd(cita(i)-cita(j)) + B(i, j) * sind(cita(i)-cita(j)))) ...
            + (1-W(i)) *(1- W(j))* D(i, j) * (G(i, j) * ( M(i, j).^-2 * U(i).^2- M(i, j).^-1 * U(i) * M(j, i).^-1 * U(j))) * (a1 * 0.95.^-1 + b1 * 0.95) ...
            + (1-W(i)) * W(j) * D(i, j) * (G(i, j) * ( M(i, j).^-2 * U(i).^2- M(i, j).^-1 * U(i) * U(j))) * (a2 * 0.95.^-1 + b2 * 0.95) ...
            + W(i) * (1- W(j)) * D(i, j) * (G(i, j) * ( U(i).^2- U(i) * M(j, i).^-1 * U(j))) ...
            + W(i) * W(j) * D(i, j) * (G(i, j) * (U(i).^2- U(i) * U(j)));
        Qji(b)=(1-W(i)) *(1- W(j)) * (1-D(i, j)) * (- U(i).^2*B(i, j)- U(i) * U(j) * (G(i, j) * sind(cita(i)-cita(j)) - B(i, j) * cosd(cita(i)-cita(j)))) ...
            + (1-W(i)) *(1- W(j))* D(i, j) *(G(i, j) * ( M(i, j).^-2 * U(i).^2- M(i, j).^-1 * U(i) * M(j, i).^-1 * U(j))) * (a1 * 0.95.^-1 + b1 * 0.95)*tan(acos(0.95)) ...
            + (1-W(i)) * W(j) * D(i, j) *(G(i, j) * ( M(i, j).^-2 * U(i).^2- M(i, j).^-1 * U(i) * U(j))) * (a2 * 0.95.^-1 + b2 * 0.95);
        
    
    end
        %计算网损
        loss1=Pij+Pji;
        loss=-sum(loss1);
       end
        