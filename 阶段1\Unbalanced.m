% function: 计算功率不平衡量
function [ dP,dQ,<PERSON>,Qi ] = Unbalanced( n,nPQ,P,Q,U,G,B,cita,T,D,M,M_cita,W )
    %计算不平衡量
    Pi = zeros(1,n);
    Qi=zeros(1,n);
    for i = 1:n
        Pn=[];
        for j = 1:n
            if j ~= i
                a1 = 0.5 * (1 + sign(M(i, j)^-1 * U(i) - M(j, i)^-1 * U(j)));
                b1 = 0.5 * (1 - sign(M(i, j)^-1 * U(i) - M(j, i)^-1 * U(j)));
                a2 = 0.5 * (1 + sign(M(i, j)^-1 * U(i) -U(j)));
                b2 = 0.5 * (1 - sign(M(i, j)^-1 * U(i) -U(j)));
                Pn(j) = T(i, j) * ((1-W(i)) *(1- W(j)) * (1-D(i, j)) * (U(i)^2 * G(i, j) - U(i) * U(j) * (G(i, j) * cos(cita(i)-cita(j)) + B(i, j) * sin(cita(i)-cita(j)))) ...
                    + (1-W(i)) *(1- W(j))* D(i, j) * (G(i, j) * (M(i, j)^-2 * U(i)^2 - M(i, j)^-1 * U(i) * M(j, i)^-1 * U(j))) * (a1 * 0.95^-1 + b1 * 0.95) ...
                    + (1-W(i)) * W(j) * D(i, j) * (G(i, j) * (M(i, j)^-2 * U(i)^2 - M(i, j)^-1 * U(i) * U(j))) * (a2 * 0.95^-1 + b2 * 0.95) ...
                    + W(i) * (1- W(j)) * D(i, j) * (G(i, j) * (U(i)^2 - U(i) * M(j, i)^-1 * U(j))) ...
                    + W(i) * W(j) * D(i, j) * (G(i, j) * (U(i)^2 - U(i) * U(j))));
            end
        end
        Pi(i)=sum(-Pn);
    end
    dP=P(1:n-1)-Pi(1:n-1); %dP有n-1个,平衡节点为0         此处写法默认节点1为平衡节点
    %计算ΔQi无功的不平衡量
    for i = 1:n
        Qn=[];
        for j = 1:n
            if j ~= i
                a1 = 0.5 * (1 + sign(M(i, j)^-1 * U(i) - M(j, i)^-1 * U(j)));
                b1 = 0.5 * (1 - sign(M(i, j)^-1 * U(i) - M(j, i)^-1 * U(j)));
                a2 = 0.5 * (1 + sign(M(i, j)^-1 * U(i) -U(j)));
                b2 = 0.5 * (1 - sign(M(i, j)^-1 * U(i) -U(j)));
                Qn(j) = T(i, j) * ((1-W(i)) *(1- W(j)) * (1-D(i, j)) * (-U(i)^2 * B(i, j) - U(i) * U(j) * (G(i, j) * sin(cita(i)-cita(j)) - B(i, j) * cos(cita(i)-cita(j)))) ...
                    + (1-W(i)) *(1- W(j))* D(i, j) *(G(i, j) * (M(i, j)^-2 * U(i)^2 - M(i, j)^-1 * U(i) * M(j, i)^-1 * U(j))) * (a1 * 0.95^-1 + b1 * 0.95)*tan(M_cita(i,j)) ...
                    + (1-W(i)) * W(j) * D(i, j) *(G(i, j) * (M(i, j)^-2 * U(i)^2 - M(i, j)^-1 * U(i) * U(j))) * (a2 * 0.95^-1 + b2 * 0.95)*tan(M_cita(i,j)));
            end
        end
        Qi(i)=sum(-Qn);
    end
    dQ=Q(1:nPQ)-Qi(1:nPQ); %dQ有m个,平衡节点为0
    for i=1:nPQ%直流节点不存在Q与Q的变化
        if W(i)==1
            dQ(i)=0;
            Qi(i)=0;
        end
    end
    end
    
