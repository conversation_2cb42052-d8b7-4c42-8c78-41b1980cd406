%% 测试储能与柔性负荷协调调度效果
% 验证修改后的储能调度逻辑是否能实现更好的协调

clear; clc; close all;

% 全局变量声明
global mpc_base Pwt Ppv price shift_load cut_load Load_ac Load_dc
global N_SL N_CL Nwt Npv ac acq dc
global ESS_params

% 加载数据
try
    load_data_v6;
    fprintf('数据加载完成\n');
catch
    error('请确保 load_data_v6.m 文件存在并可正常运行');
end

% 储能参数设置
ESS_params.Ce = 700;
ESS_params.Cp = 400;
ESS_params.Cmaint = 60;
ESS_params.ir = 0.015;
ESS_params.dr = 0.09;
ESS_params.Y = 10;
ESS_params.Cde = 60;
ESS_params.E_max = 8000;
ESS_params.P_max = 2000;

% 创建测试解决方案
X_test = zeros(1, 6 + N_SL + 48*N_CL);

% 储能配置：较大容量以便观察协调效果
X_test(1:6) = [15, 25, 4000, 3500, 3, 4];  % 节点15+25, 容量4+3.5MWh

% 平移负荷时间：模拟一些负荷从高峰时段移到低谷时段
time_shifts = [12, 14, 10, 22];  % 将部分负荷移到低价格时段
X_test(7:6+N_SL) = time_shifts;

% 削减负荷量和开关：模拟在高价时段进行适度削减
idx = 7 + N_SL;
for t = 1:24
    for i = 1:N_CL
        if t >= 18 && t <= 21  % 晚高峰时段
            X_test(idx) = 0.3 * cut_load(i, t+1);  % 适度削减
        else
            X_test(idx) = 0;  % 其他时段不削减
        end
        idx = idx + 1;
    end
end

% 开关变量：高峰时段开启削减
for t = 1:24
    for i = 1:N_CL
        if t >= 18 && t <= 21
            X_test(idx) = 3;  % 高值，sigmoid后接近1
        else
            X_test(idx) = -3; % 低值，sigmoid后接近0
        end
        idx = idx + 1;
    end
end

fprintf('=== 测试储能与柔性负荷协调调度 ===\n');
fprintf('储能1: 节点%d, 容量%.1fMWh, 功率%.1fMW\n', X_test(1), X_test(3)/1000, X_test(3)/X_test(5)/1000);
fprintf('储能2: 节点%d, 容量%.1fMWh, 功率%.1fMW\n', X_test(2), X_test(4)/1000, X_test(4)/X_test(6)/1000);

% 计算目标函数
try
    [f1, f2, cost_details] = ESS_objective_v6(X_test);
    
    fprintf('\n=== 协调调度结果 ===\n');
    fprintf('目标函数1 (储能成本): %.2f万元\n', f1/10000);
    fprintf('目标函数2 (运行成本): %.2f万元\n', f2/10000);
    
    fprintf('\n运行成本分解:\n');
    fprintf('  电网交互成本: %.2f万元\n', cost_details.grid_cost/10000);
    fprintf('  弃风弃光成本: %.2f万元\n', cost_details.curtail_cost/10000);
    fprintf('  网损成本: %.2f万元\n', cost_details.loss_cost/10000);
    fprintf('  平移负荷补偿: %.2f万元\n', cost_details.C_SL/10000);
    fprintf('  削减负荷补偿: %.2f万元\n', cost_details.C_CL/10000);
    
    if isfield(cost_details, 'renewable_stats')
        fprintf('\n可再生能源利用:\n');
        fprintf('  风电利用率: %.1f%%\n', cost_details.renewable_stats.wind_utilization_rate);
        fprintf('  光伏利用率: %.1f%%\n', cost_details.renewable_stats.pv_utilization_rate);
        fprintf('  总购电量: %.2f MWh\n', cost_details.renewable_stats.total_purchase_mwh);
    end
    
    % 分析储能与柔性负荷的协调效果
    if isfield(cost_details, 'hourly_data')
        ess_power = cost_details.hourly_data.ess_power;
        
        % 计算储能在不同时段的平均功率
        peak_hours = 18:21;
        valley_hours = [1:6, 23:24];
        
        peak_ess = mean(sum(ess_power(peak_hours, :), 2));
        valley_ess = mean(sum(ess_power(valley_hours, :), 2));
        
        fprintf('\n储能调度分析:\n');
        fprintf('  高峰时段平均储能功率: %.2f MW (正值放电)\n', peak_ess);
        fprintf('  低谷时段平均储能功率: %.2f MW (负值充电)\n', valley_ess);
        
        if peak_ess > 0 && valley_ess < 0
            fprintf('  ✓ 储能实现了"低谷充电，高峰放电"的理想调度\n');
        else
            fprintf('  ✗ 储能调度模式需要进一步优化\n');
        end
    end
    
    fprintf('\n=== 协调效果评估 ===\n');
    fprintf('修改后的调度逻辑应该能够:\n');
    fprintf('1. 储能基于调整后负荷进行决策\n');
    fprintf('2. 柔性负荷移峰填谷后，储能配合进行精细调节\n');
    fprintf('3. 整体运行成本相比未协调情况应有所降低\n');
    
catch ME
    fprintf('计算出错: %s\n', ME.message);
    fprintf('错误位置: %s, 行号: %d\n', ME.stack(1).name, ME.stack(1).line);
end 