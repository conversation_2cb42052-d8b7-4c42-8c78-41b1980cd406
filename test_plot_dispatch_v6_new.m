% 测试新的v6绘图功能
clear; clc; close all;

% 加载必要的全局数据
global mpc_base Pwt Ppv price shift_load cut_load Load_ac Load_dc
global ac dc N_SL N_CL

% 运行数据加载
load_data_v6;

% 定义测试决策变量
ESS1_node = 10;
ESS2_node = 20;
ESS1_capacity = 2000;  % kWh
ESS2_capacity = 1500;  % kWh
ESS1_hour_ratio = 4;
ESS2_hour_ratio = 3;

% 构造测试决策变量向量
X_test = zeros(1000, 1);  % 足够大的向量
X_test(1) = ESS1_node;
X_test(2) = ESS2_node;
X_test(3) = ESS1_capacity;
X_test(4) = ESS2_capacity;
X_test(5) = ESS1_hour_ratio;
X_test(6) = ESS2_hour_ratio;

% 平移负荷决策变量
idx = 7;
if N_SL > 0
    % 测试一些平移：从原始时段移到不同时段
    X_test(idx:idx+N_SL-1) = shift_load(:,4)' + [2, -3, 1, 4, -2, 3, -1, 2, -4, 1, 3, -2, 1];  % 示例平移
    idx = idx + N_SL;
end

% 削减负荷决策变量
if N_CL > 0
    % 削减量变量 (24*N_CL个)
    cut_amount_vars = randn(24*N_CL, 1) * 0.02;  % 随机削减量
    X_test(idx:idx+24*N_CL-1) = cut_amount_vars;
    idx = idx + 24*N_CL;
    
    % 削减开关变量 (24*N_CL个)
    cut_switch_vars = randn(24*N_CL, 1) * 2;  % 随机开关状态
    % 倾向于在高峰时段(11-16h, 19-22h)削减
    for t = 1:24
        for i = 1:N_CL
            var_idx = (t-1)*N_CL + i;
            if (t >= 11 && t <= 16) || (t >= 19 && t <= 22)
                cut_switch_vars(var_idx) = cut_switch_vars(var_idx) + 3;  % 增加在峰段削减的概率
            end
        end
    end
    X_test(idx:idx+24*N_CL-1) = cut_switch_vars;
end

% 生成模拟的运行结果数据
hours = 1:24;

% 模拟风光实际出力
wind_actual = Pwt .* [1.5, 2.0, 1.2] .* (0.8 + 0.4*rand(24,3));  % 加入随机波动
pv_actual = Ppv .* [1.8, 2.2, 1.6] .* (0.7 + 0.6*rand(24,3));

% 模拟储能功率（基于简化调度逻辑）
ess_power = zeros(24, 2);
ESS1_max_power = ESS1_capacity / ESS1_hour_ratio / 1000;  % MW
ESS2_max_power = ESS2_capacity / ESS2_hour_ratio / 1000;  % MW

% 简化的调度逻辑用于测试
for t = 1:24
    total_renewable = sum(wind_actual(t,:)) + sum(pv_actual(t,:));
    total_load_t = sum(ac(t,:)) + sum(dc(t,:));
    
    if total_renewable > total_load_t  % 有盈余，充电
        surplus = total_renewable - total_load_t;
        max_charge = ESS1_max_power + ESS2_max_power;
        charge_power = min(surplus, max_charge);
        ess_power(t,1) = -charge_power * ESS1_max_power / max_charge;
        ess_power(t,2) = -charge_power * ESS2_max_power / max_charge;
    elseif price(t) > 0.7  % 峰价时段，放电
        deficit = total_load_t - total_renewable;
        max_discharge = ESS1_max_power + ESS2_max_power;
        discharge_power = min(deficit, max_discharge);
        ess_power(t,1) = discharge_power * ESS1_max_power / max_discharge;
        ess_power(t,2) = discharge_power * ESS2_max_power / max_discharge;
    end
end

% 模拟购电和弃电
P_import = zeros(24, 1);
P_curtail = zeros(24, 1);

for t = 1:24
    total_renewable = sum(wind_actual(t,:)) + sum(pv_actual(t,:));
    total_load_t = sum(ac(t,:)) + sum(dc(t,:));
    total_ess = sum(ess_power(t,:));
    
    balance = total_load_t - total_renewable - total_ess;
    
    if balance > 0
        P_import(t) = balance;
        P_curtail(t) = 0;
    else
        P_import(t) = 0;
        P_curtail(t) = -balance;
    end
end

% 调用新的绘图函数
fprintf('=== 测试新的v6绘图功能 ===\n');
fprintf('储能配置: ESS1=%dkWh@节点%d, ESS2=%dkWh@节点%d\n', ...
        ESS1_capacity, ESS1_node, ESS2_capacity, ESS2_node);
fprintf('平移负荷数量: %d\n', N_SL);
fprintf('可削减负荷数量: %d\n', N_CL);

% 测试绘图函数 - 使用新的函数签名
plot_dispatch_results_v6(X_test, ac, dc, wind_actual, pv_actual, ess_power, ...
                         P_curtail, P_import, ESS1_node, ESS2_node, ...
                         ESS1_capacity, ESS2_capacity);

fprintf('\n=== 绘图完成 ===\n');
fprintf('应该看到以下图形:\n');
fprintf('1. 调度结果堆叠柱形图（使用调整后负荷曲线）\n');
fprintf('2. 储能充放电功率柱形图（两个储能分开显示）\n');
fprintf('3. 可平移负荷移入移出结果图\n');
fprintf('4. 可削减负荷削减结果图\n');
fprintf('5-7. VSC功率图（3个VSC）\n');
fprintf('8. 负荷曲线对比图（原始 vs 调整后）\n'); 