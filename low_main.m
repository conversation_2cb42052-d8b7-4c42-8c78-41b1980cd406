% function [MinimumCost,bestsolution]=low_main(mpc,X,n_switch)

n_switch=1;
mpc=case33ACDC;
X=zeros(1,144);
%%矩阵变化
a=24/n_switch;
for n=1:n_switch
    mpc1((n-1)*a+1:n*a)=repmat(mpc(n),[1,1,a]);
end


%%算法参数
K = 10;    %Knowledge Rate
KF = 0.5;  % Knowledge Factor
KR = 0.9;  %Knowledge Ratio
p = 0.1;
m=30;
Max_gen=10;
runnum=10;
flexible_load;
power_load;

Pwt1=Pwt1-X(1:24);
Pwt2=Pwt2-X(25:48);
Pwt3=Pwt3-X(49:72);
Pwt=[Pwt1' Pwt2' Pwt3'];
Ppv1=Ppv1-X(73:96); 
Ppv2=Ppv2-X(97:120);
Ppv3=Ppv3-X(121:144);
Ppv=[Ppv1' Ppv2' Ppv3'];

%%参数设定
global N_SL  N_CL Ness Nsvg     Nwt Npv Nm Nmcita ac acq dc
ratio=mpc.bus(:,3:5)./sum(mpc.bus(:,3:5));% 各节点在基准潮流中的 Pac / Qac / Pdc 占比
ratioq=mpc.bus(:,4)./mpc.bus(:,3);% 每个交流节点的 Q/P 比，用于生成 Q 负荷
ratioq(isnan(ratioq)) = 0;
ac=[];dc=[];acq=[];
for i=1:24
    ac(i,:)=Load_ac(i)*ratio(:,1); % 交流有功负荷分配到 33 节点
    acq(i,:)=ac(i,:).*ratioq'; % 交流无功负荷 (保持各节点 Q/P 比)
    dc(i,:)=Load_dc(i)*ratio(:,3); % 直流负荷分配
end
N_SL=size(shift_load,1);N_CL=size(cut_load,1);
% N_SL=0;N_CL=0;
Nwt=3; Npv=3; Nm=0; Nmcita=0;
Ness=2;Nsvg=0;
d=( Ness +Nsvg +Nwt+Npv+ N_SL + N_CL*2);

% P_min=repmat((ones(2,1)*-0.4)',24,1);%储能充放电功率
% P_max=repmat((ones(2,1)*0.4)',24,1);
P_min=[0	0	0	0	0	0	0	-0.4	-0.4	-0.4	-0.4	-0.4	-0.4	-0.4	-0.4	-0.4	-0.4	-0.4	-0.4	-0.4	-0.4	-0.4	-0.4	-0.4]';
P_max=[0.4	0.4	0.4	0.4	0.4	0.4	0.4	0.4	0.4	0.4	0.4	0	0	0	0	0.4	0.4	0.4	0	0	0	0.4	0.4	0.4]';

time_SL1=shift_load(:,4);time_SL2=shift_load(:,5)-shift_load(:,6)+1;

% 风光使用量     储能充放电功率     平移负荷起始时间节点      削减负荷削减量与削减标志
% MinParValue= [P_min P_min repmat(time_SL1',24,1)  zeros(24,N_CL)   ones(24,N_CL)*-10];
% MaxParValue=[P_max P_max repmat(time_SL2',24,1)  cut_load(:,2:25)'  ones(24,N_CL)*10];
MinParValue= [zeros(24,6) P_min P_min repmat(time_SL1',24,1)  zeros(24,N_CL)   ones(24,N_CL)*-10];
MaxParValue=[Pwt   Ppv    P_max P_max repmat(time_SL2',24,1)  zeros(24,N_CL)  ones(24,N_CL)*10];
% MinParValue= [P_min P_min];
% MaxParValue=[P_max P_max];

finalresults=[];
lossresults=[];
priceresults=[];
solution = [];
penaltyresult=[];

for run = 1 : runnum
    % 初始化数组
    F1 = zeros(1, m);
    F2 = zeros(1, m);
    F3 = zeros(1, m);
    penalty = zeros(1, m);
    penalty_E = zeros(1, m);
    
    for i=1:m
        %     convergence(i)=0;
        %     while true
        pop(i).Population= MinParValue+ lhsdesign(24,d) .* (MaxParValue - MinParValue);
        for j=1:Ness
            [pop(i).Population(:,Nwt + Npv +Nm+Nmcita+j),penalty_e(j)]=ESS_test(pop(i).Population(:,Nwt + Npv +Nm+Nmcita+j));
        end
        penalty_E(i)=sum(penalty_e);
        [F1_temp,F2_temp,F3_temp,penalty_temp,~] = low_obj(pop(i).Population,mpc1,Pwt,Ppv,price,shift_load,cut_load);
        F1(i) = F1_temp;
        F2(i) = F2_temp;
        F3(i) = F3_temp;
        penalty(i) = penalty_temp;
        %         if convergence(i)==0
        %             break;
        %         end
        %     end
        pop(i).pBest=pop(i).Population;
    end
    Objective_values=F2+penalty+penalty_E;

    pBestScore = Objective_values; %initialize the pbest and the pbest's fitness value
    [gBestScore,gbestid] = min(pBestScore);
    gBest = pop(gbestid).Population;

    gen = 0;
    
    results=[];
    %loss1,price1,V_less1,p1] = low_obj(bestsolution,mpc1,Pwt,Ppv,price,shift_load,cut_load);
   loss=[];
   price_result=[];
    %     V_less=[V_less;V_less1];
    penalt2=[];
    
    while(gen < Max_gen)

        a=2-gen*((2)/Max_gen); % 等式（3）中a随迭代次数从2线性下降至0
        %a2从-1线性下降至-2，计算l时会用到
        a2=-1+gen*((-1)/Max_gen);
        % Sort Population
        %         [Population,Objective_values] = PopulationSort(Population,Objective_values);
        [sorted,indices] = sort(Objective_values);
        Objective_values=sorted;
        pop=pop(indices);
        %         Population=Population(indices,:);

        D_Gained_Shared_Junior = ceil(d*(1 - gen / Max_gen)^K);
        for i = 1 : m

            % Gained_Shared_Junior_R1R2R3
            if(i==1)% best
                r1_Junior = 2;
                r2_Junior = 3;
                r4_Junior = 4;
                r5_Junior = 5;
            elseif(i == m)% worst
                r1_Junior = m - 2;
                r2_Junior = m - 1;
                r4_Junior = m - 4;
                r5_Junior = m - 3;
            elseif(i==2)
                r1_Junior = 1;
                r2_Junior = 3;
                r4_Junior = 4;
                r5_Junior = 5;
            elseif(i==m-1)
                r1_Junior = m - 2;
                r2_Junior = m;
                r4_Junior = m - 4;
                r5_Junior = m - 3;
            else
                r1_Junior = i - 1;
                r2_Junior = i + 1;
                r4_Junior = i - 2;
                r5_Junior = i + 2;
            end
            while true
                r3_Junior = round(m * rand + 0.5);
                if (r3_Junior ~= r1_Junior) && (r3_Junior ~= r2_Junior) && (r3_Junior ~= i) && (r3_Junior ~= r4_Junior)&& (r3_Junior ~= r5_Junior), break, end
            end

            % Gained_Shared_Senior_R1R2R3
            r1_Senior = ceil(p*m * rand);
            r2_Senior = ceil((1-p) * m) + ceil(p*m * rand);
            while true
                r3_Senior = ceil(p * m) + ceil((1-2*p)*m * rand);
                if (r3_Senior ~= i), break, end
            end

            r1=rand(); % r1为[0,1]之间的随机数
            r2=rand(); % r2为[0,1]之间的随机数

            A=2*a*r1-a;  % 等式（3）
            C=2*r2;      % 等式（4）

            b=1;               %  等式（5）中的常数b
            l=(a2-1)*rand+1;   %  等式（5）中的随机数l
            p1 = rand();
            for t=1:24
                for j = 1:d
                    r=rand;
                    if r<KR
                        if rand < D_Gained_Shared_Junior/d
                            if i < r3_Junior

                                pop(i).TempPop(t,j) = pop(i).Population(t,j) + KF * (pop(r1_Junior).Population(t,j) - pop(r2_Junior).Population(t,j)) + ...
                                    KF * (pop(i).Population(t,j) - pop(r3_Junior).Population(t,j))+...
                                    KF/4 * (pop(r4_Junior).Population(t,j) -pop(r5_Junior).Population(t,j)) +KF/4 * (pop(i).Population(t,j) - pop(r3_Junior).Population(t,j));
                            else
                                pop(i).TempPop(t,j)= pop(i).Population(t,j) + KF * (pop(r1_Junior).Population(t,j) - pop(r2_Junior).Population(t,j)) + ...
                                    KF * (pop(r3_Junior).Population(t,j) - pop(i).Population(t,j))+...
                                    KF/4 * (pop(r4_Junior).Population(t,j) -pop(r5_Junior).Population(t,j)) +KF/4 * (pop(r3_Junior).Population(t,j) - pop(i).Population(t,j));
                            end
                        else
                            if i < r3_Senior
                                pop(i).TempPop(t,j) = pop(i).Population(t,j) + KF * (pop(r1_Senior).Population(t,j) - pop(r2_Senior).Population(t,j)) + ...
                                    KF * (pop(i).Population(t,j) - pop(r3_Senior).Population(t,j));
                            else
                                pop(i).TempPop(t,j) = pop(i).Population(t,j) + KF * (pop(r1_Senior).Population(t,j) - pop(r2_Senior).Population(t,j)) + ...
                                    KF * (pop(r3_Senior).Population(t,j) - pop(i).Population(t,j));
                            end
                        end
                    else

                        %                                                 pop(i).TempPop(t,j) = pop(i).Population(t,j);
                        distance2Leader=abs(pop(1).Population(t,j)-pop(i).Population(t,j));
                        % 等式（5）
                        pop(i).TempPop(t,j) =distance2Leader*exp(b.*l).*cos(l.*2*pi)+pop(1).Population(t,j);
                    end%一个未知数更新完
                end%更新完所有未知数（ for j = 1:d）

                %校验数据
                Tp= pop(i).TempPop(t,:)>MaxParValue(t,:);Tm= pop(i).TempPop(t,:)<MinParValue(t,:);
                pop(i).TempPop(t,:)=(pop(i).TempPop(t,:).*(~(Tp+Tm)))+MaxParValue(t,:).*Tp+MinParValue(t,:).*Tm;
            end
            for j=1:Ness
                [pop(i).TempPop(:,Nwt + Npv +Nm+Nmcita+j),penalty_e(j)]=ESS_test(pop(i).TempPop(:,Nwt + Npv +Nm+Nmcita+j));
            end
            penalty_E(i)=sum(penalty_e);

            [F1_temp,F2_temp,F3_temp,penalty_temp,~] = low_obj(pop(i).TempPop,mpc1,Pwt,Ppv,price,shift_load,cut_load);
            F1(i) = F1_temp;
            F2(i) = F2_temp;
            F3(i) = F3_temp;
            penalty(i) = penalty_temp;
        end

        Objective_values_TempPop=F2+penalty+penalty_E;
        mask = Objective_values_TempPop < Objective_values;
        %         aa = repmat(mask',1,d);
        for ii=1:m
            pop(ii).Population=mask(ii).*pop(ii).TempPop+ ~mask(ii) .*pop(ii).Population;
        end
        Objective_values = mask .* Objective_values_TempPop + ~mask .* Objective_values;
        [gbestval,gbestid] = min(Objective_values);
        best = pop(gbestid).Population;

        MinimumCost = gbestval;
        bestsolution = best;
        % Display Iteration Information
        
        
        gen = gen + 1;
        results = [results; MinimumCost];
                %results(run,gen) = MinimumCost;
        [loss1,price1,V_less1,p1,~] = low_obj(bestsolution,mpc1,Pwt,Ppv,price,shift_load,cut_load);
        loss=[loss;loss1];price_result=[price_result;price1];
        %     V_less=[V_less;V_less1];
        penalt2=[penalt2;p1];
    end
    finalresults = [finalresults results];
    lossresults=[lossresults loss];
    priceresults=[priceresults price_result];
    solution = [solution; bestsolution];
    penaltyresult=[penaltyresult penalt2];
%     V_lessresult=[V_lessresult V_less];
    fprintf('Run No.%d Done!\n', run);
    disp(['CPU time: ',num2str(toc)]);
end
% xlswrite('lossresult4.xls', lossresults);
% xlswrite('priceresult4.xls', priceresults);
xlswrite('不重构不柔性运行成本.xls', finalresults);
xlswrite('不重构不柔性运行结果.xls', solution);
% xlswrite('solution4(重构3次)下层运行.xls', solution2);
% xlswrite('price4(重构3次)下层运行成本.xls', price2); 