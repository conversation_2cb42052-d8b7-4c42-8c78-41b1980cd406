%% 分析真正的弃电原因 - 深入调查为什么弃电率高达17.1%
% 发现：盈余时段很少，但弃电量很大，说明弃电原因不是盈余过多

clear; clc; close all;

% 全局变量声明
global mpc_base Pwt Ppv price shift_load cut_load Load_ac Load_dc
global N_SL N_CL Nwt Npv ac acq dc ESS_params

% 储能参数设置
ESS_params.Ce = 700;
ESS_params.Cp = 400;
ESS_params.Cmaint = 180;
ESS_params.ir = 0.015;
ESS_params.dr = 0.09;
ESS_params.Y = 10;
ESS_params.lambda = 0.08;
ESS_params.Cde = 60;
ESS_params.E_max = 8000;
ESS_params.P_max = 2500;

% 加载数据
load_data_v6;

% 风光基准容量
scale = 1.5;
wind_base = [2.16, 1.8, 2.7] * scale;
pv_base = [2.0, 2.5, 2.5] * scale;

fprintf('=== 真正弃电原因分析 ===\n\n');

%% 构造当前最优解进行分析
% 根据投资成本反推储能配置
investment_cost = 294.18 * 10000; % 元
% 假设两个储能容量相等，功率容量配比为3.5h
% investment = 2*E*700 + 2*E/3.5*400 = E*(1400 + 800/3.5) = E*1628.6
total_capacity = investment_cost / 1628.6; % kWh
single_capacity = total_capacity / 2; % kWh
single_power = single_capacity / 3.5; % kW

% 构造测试解
n_var = 2 + 2 + 2 + N_SL + N_CL;
solution_test = zeros(1, n_var);
solution_test(1:2) = [30, 5];                    % 储能安装节点
solution_test(3:4) = [single_capacity, single_capacity]; % 储能容量
solution_test(5:6) = [3.5, 3.5];                 % 功率配比

% 平移负荷时间设置
idx = 7;
if N_SL > 0
    solution_test(idx:idx+N_SL-1) = shift_load(:,4)';
    idx = idx + N_SL;
end

% 削减负荷开关设置
if N_CL > 0
    solution_test(idx:idx+N_CL-1) = 0;
end

fprintf('测试储能配置:\n');
fprintf('单个储能容量: %.0f kWh\n', single_capacity);
fprintf('单个储能功率: %.0f kW\n', single_power);
fprintf('总容量: %.1f MWh\n', total_capacity/1000);
fprintf('总功率: %.1f MW\n', single_power*2/1000);

%% 运行目标函数获取详细数据
[f1, f2, cost_details] = ESS_objective_v6(solution_test);

fprintf('\n运行结果:\n');
fprintf('储能全寿命周期成本: %.2f 万元\n', f1/10000);
fprintf('系统运行成本: %.2f 万元\n', f2/10000);
fprintf('弃风弃光成本: %.2f 万元\n', cost_details.curtail_cost/10000);

if isfield(cost_details, 'renewable_stats')
    fprintf('风电利用率: %.1f%% (弃风率: %.1f%%)\n', ...
        cost_details.renewable_stats.wind_utilization_rate, ...
        cost_details.renewable_stats.wind_curtailment_rate);
    fprintf('光伏利用率: %.1f%% (弃光率: %.1f%%)\n', ...
        cost_details.renewable_stats.pv_utilization_rate, ...
        cost_details.renewable_stats.pv_curtailment_rate);
end

%% 分析逐时弃电情况
if isfield(cost_details, 'hourly_data')
    P_curtail = cost_details.hourly_data.P_curtail;
    P_import = cost_details.hourly_data.P_import;
    ess_power = cost_details.hourly_data.ess_power;
    wind_actual = cost_details.hourly_data.wind_actual;
    pv_actual = cost_details.hourly_data.pv_actual;
    
    fprintf('\n=== 逐时弃电分析 ===\n');
    fprintf('时刻  负荷   风电预测 光伏预测 总RE预测 风电实际 光伏实际 总RE实际 弃电量  储能功率 购电量\n');
    fprintf('----  ----   -------- -------- -------- -------- -------- -------- ------  -------- ------\n');
    
    total_curtailment = 0;
    curtailment_hours = [];
    
    for t = 1:24
        total_load = Load_ac(t) + Load_dc(t);
        wind_forecast_t = sum(Pwt(t,:) .* wind_base);
        pv_forecast_t = sum(Ppv(t,:) .* pv_base);
        total_re_forecast = wind_forecast_t + pv_forecast_t;
        
        wind_actual_t = sum(wind_actual(t,:));
        pv_actual_t = sum(pv_actual(t,:));
        total_re_actual = wind_actual_t + pv_actual_t;
        
        curtailment_t = P_curtail(t);
        total_curtailment = total_curtailment + curtailment_t;
        
        if curtailment_t > 0.01
            curtailment_hours = [curtailment_hours, t];
        end
        
        total_ess_power = sum(ess_power(t,:));
        
        fprintf('%02d:00 %5.2f  %8.2f %8.2f %8.2f %8.2f %8.2f %8.2f %6.3f  %8.3f %6.2f\n', ...
            t-1, total_load, wind_forecast_t, pv_forecast_t, total_re_forecast, ...
            wind_actual_t, pv_actual_t, total_re_actual, curtailment_t, total_ess_power, P_import(t));
    end
    
    fprintf('\n弃电时段: ');
    for i = 1:length(curtailment_hours)
        fprintf('%02d:00 ', curtailment_hours(i)-1);
    end
    fprintf('\n');
    fprintf('总弃电量: %.2f MWh\n', total_curtailment);
    fprintf('弃电时段数: %d 小时\n', length(curtailment_hours));
    
    %% 分析弃电原因
    fprintf('\n=== 弃电原因分析 ===\n');
    
    % 检查是否是网络约束导致的弃电
    fprintf('1. 网络约束分析:\n');
    for i = 1:min(5, length(curtailment_hours))  % 分析前5个弃电时段
        t = curtailment_hours(i);
        total_load = Load_ac(t) + Load_dc(t);
        total_re_forecast = sum(Pwt(t,:) .* wind_base) + sum(Ppv(t,:) .* pv_base);
        renewable_surplus = total_re_forecast - total_load;
        
        fprintf('   时刻%02d:00: 负荷=%.2f MW, 可再生=%.2f MW, 盈余=%.2f MW, 弃电=%.3f MW\n', ...
            t-1, total_load, total_re_forecast, renewable_surplus, P_curtail(t));
        
        if renewable_surplus < 0
            fprintf('     ⚠️  异常：负荷大于可再生能源，但仍有弃电！\n');
            fprintf('     可能原因：网络约束、储能SOC限制、或调度逻辑问题\n');
        end
    end
    
    % 检查储能SOC是否达到上限
    fprintf('\n2. 储能SOC分析:\n');
    % 简单估算SOC变化
    soc1 = 0.1; % 初始SOC 10%
    soc2 = 0.1;
    eta_ch = 0.92;
    eta_dis = 0.92;
    
    for t = 1:24
        % 储能1
        if ess_power(t,1) > 0  % 放电
            soc1 = soc1 - ess_power(t,1) / (single_capacity/1000) / eta_dis;
        else  % 充电
            soc1 = soc1 - ess_power(t,1) / (single_capacity/1000) * eta_ch;
        end
        soc1 = max(0.01, min(0.99, soc1));
        
        % 储能2
        if ess_power(t,2) > 0  % 放电
            soc2 = soc2 - ess_power(t,2) / (single_capacity/1000) / eta_dis;
        else  % 充电
            soc2 = soc2 - ess_power(t,2) / (single_capacity/1000) * eta_ch;
        end
        soc2 = max(0.01, min(0.99, soc2));
        
        if P_curtail(t) > 0.01
            fprintf('   时刻%02d:00: SOC1=%.1f%%, SOC2=%.1f%%, 弃电=%.3f MW\n', ...
                t-1, soc1*100, soc2*100, P_curtail(t));
            
            if soc1 > 0.95 || soc2 > 0.95
                fprintf('     原因：储能SOC接近上限，无法继续充电\n');
            end
        end
    end
    
    %% 检查调度逻辑问题
    fprintf('\n3. 调度逻辑检查:\n');
    logic_issues = 0;
    for t = 1:24
        if P_curtail(t) > 0.01
            total_ess_power = sum(ess_power(t,:));
            if total_ess_power > 0  % 储能在放电
                fprintf('   时刻%02d:00: 有弃电%.3f MW，但储能在放电%.3f MW\n', ...
                    t-1, P_curtail(t), total_ess_power);
                logic_issues = logic_issues + 1;
            elseif abs(total_ess_power) < 0.01  % 储能待机
                fprintf('   时刻%02d:00: 有弃电%.3f MW，但储能待机\n', ...
                    t-1, P_curtail(t));
                logic_issues = logic_issues + 1;
            end
        end
    end
    
    if logic_issues > 0
        fprintf('   发现%d个调度逻辑问题！\n', logic_issues);
    else
        fprintf('   调度逻辑正常\n');
    end
end

%% 解决方案建议
fprintf('\n=== 解决方案建议 ===\n');

fprintf('1. 立即可行的改进:\n');
fprintf('   - 检查网络约束设置，确保不会过度限制可再生能源接入\n');
fprintf('   - 优化储能调度逻辑，有弃电时优先充电\n');
fprintf('   - 提高储能SOC上限到98%%，增加充电空间\n');

fprintf('\n2. 参数调整:\n');
fprintf('   - 大幅提高弃电成本到5.0元/kWh\n');
fprintf('   - 降低储能投资成本，激励更大容量配置\n');
fprintf('   - 增加储能最大容量限制到15MWh\n');

fprintf('\n3. 根本性改进:\n');
fprintf('   - 如果弃电主要由网络约束引起，需要考虑网络扩容\n');
fprintf('   - 如果是储能容量不足，需要大幅增加储能配置\n');
fprintf('   - 考虑分布式储能布局，就近消纳可再生能源\n');

fprintf('\n=== 分析完成 ===\n');
