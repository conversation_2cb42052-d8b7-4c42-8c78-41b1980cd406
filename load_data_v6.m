function load_data_v6()
% v6版本数据加载函数 - 调度逻辑重构
% 加载储能优化所需的所有基础数据

global mpc_base Pwt Ppv price shift_load cut_load Load_ac Load_dc
global N_SL N_CL Nwt Npv ac acq dc

% 加载基础数据
mpc_base = case33ACDC;
flexible_load;
power_load;

% 风光数据处理 - v6版本调整
Pwt = [Pwt1' Pwt2' Pwt3'];
Ppv = [Ppv1' Ppv2' Ppv3'];

% 负荷数据处理
ratio = mpc_base.bus(:,3:5)./sum(mpc_base.bus(:,3:5));
ratioq = mpc_base.bus(:,4)./mpc_base.bus(:,3);
ratioq(isnan(ratioq)) = 0;

for i = 1:24
    ac(i,:) = Load_ac(i) * ratio(:,1);
    acq(i,:) = ac(i,:) .* ratioq';
    dc(i,:) = Load_dc(i) * ratio(:,3);
end

N_SL = size(shift_load,1);
N_CL = size(cut_load,1);
Nwt = 3; Npv = 3;

fprintf('v6版本数据加载完成:\n');
fprintf('平移负荷数量 N_SL: %d\n', N_SL);
fprintf('削减负荷数量 N_CL: %d\n', N_CL);
fprintf('风电场数量: %d, 光伏场数量: %d\n', Nwt, Npv);

end 