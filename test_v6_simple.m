%% v6版本简单测试脚本
% 测试新的调度逻辑和变量结构

clear; clc; close all;

% 全局变量声明
global mpc_base Pwt Ppv price shift_load cut_load Load_ac Load_dc
global N_SL N_CL Nwt Npv ac acq dc ESS_params

% 储能参数设置
ESS_params.Ce = 400;        % 储能单位容量成本 (元/kWh)
ESS_params.Cp = 250;        % 储能单位功率成本 (元/kW)
ESS_params.Cmaint = 60;     % 年运行维护成本 (元/kW·年)
ESS_params.ir = 0.015;      % 通胀膨胀率
ESS_params.dr = 0.09;       % 贴现率
ESS_params.Y = 10;          % 储能寿命 (年)
ESS_params.lambda = 0.08;   % 净报废成本系数
ESS_params.E_max = 6000;    % 最大安装容量 (kWh)
ESS_params.P_max = 1500;    % 最大充放电功率 (kW)

% 加载数据
load_data_v6;

fprintf('=== v6版本测试 ===\n');

%% 构造测试解
% v6决策变量结构：
% 1-2: 储能选址, 3-4: 储能容量, 5-6: 功率容量配比
% 7-30: 弃电量(24h), 31-54: 购电量(24h), 55-102: 储能系数(24×2)
% 其余: 柔性负荷变量

n_var = 6 + 24 + 24 + 48 + N_SL + 24*N_CL + 24*N_CL;
fprintf('总决策变量数: %d\n', n_var);

% 创建测试解
X_test = zeros(1, n_var);

% 储能选址和容量
X_test(1:2) = [15, 25];     % 储能安装在节点15和25
X_test(3:4) = [2000, 1500]; % 储能容量 2MWh, 1.5MWh
X_test(5:6) = [4, 3];       % 功率容量配比 4h, 3h

% 弃电量 (7-30) - 设置为较小值测试调度逻辑
X_test(7:30) = 0.5 * ones(1, 24);  % 每小时弃电0.5MW

% 购电量 (31-54) - 设置为负荷的一部分
load_profile = Load_ac + Load_dc;
X_test(31:54) = 0.3 * load_profile;  % 购电为负荷的30%

% 储能充放电系数 (55-102) - 设置简单的充放电模式
ess_coeff = zeros(24, 2);
% 白天充电 (8-16点)
ess_coeff(8:16, :) = -0.5;  % 充电
% 晚上放电 (17-22点)
ess_coeff(17:22, :) = 0.8;  % 放电
X_test(55:102) = reshape(ess_coeff, 1, 48);

% 柔性负荷变量设置为默认值
idx = 103;
% 平移负荷时间
X_test(idx:idx+N_SL-1) = shift_load(:,4)';
idx = idx + N_SL;

% 削减负荷量和开关设为0
X_test(idx:end) = 0;

fprintf('测试解构造完成，变量数: %d\n', length(X_test));

%% 测试目标函数
try
    fprintf('\n开始测试目标函数...\n');
    [f1, f2, cost_details] = ESS_objective_v6(X_test);
    
    fprintf('目标函数计算成功！\n');
    fprintf('f1 (储能全寿命周期成本): %.2f 万元\n', f1/10000);
    fprintf('f2 (系统运行成本): %.2f 万元\n', f2/10000);
    
    % 显示详细成本信息
    if exist('cost_details', 'var') && isstruct(cost_details)
        fprintf('\n=== 详细成本分析 ===\n');
        fprintf('储能投资成本: %.2f 万元\n', cost_details.C_i/10000);
        fprintf('储能运维成本: %.2f 万元\n', cost_details.C_m/10000);
        fprintf('储能报废成本: %.2f 万元\n', cost_details.C_d/10000);
        fprintf('电网交互成本: %.2f 万元\n', cost_details.grid_cost/10000);
        fprintf('弃风弃光成本: %.2f 万元\n', cost_details.curtail_cost/10000);
        fprintf('网损成本: %.2f 万元\n', cost_details.loss_cost/10000);
        
        if isfield(cost_details, 'renewable_stats')
            fprintf('\n=== 风光利用率分析 ===\n');
            fprintf('风电利用率: %.1f%%\n', cost_details.renewable_stats.wind_utilization_rate);
            fprintf('光伏利用率: %.1f%%\n', cost_details.renewable_stats.pv_utilization_rate);
            fprintf('总购电量: %.2f MWh\n', cost_details.renewable_stats.total_purchase_mwh);
        end
    end
    
catch ME
    fprintf('目标函数测试失败！\n');
    fprintf('错误信息: %s\n', ME.message);
    fprintf('错误位置: %s\n', ME.stack(1).name);
    return;
end

%% 测试绘图功能
try
    fprintf('\n开始测试绘图功能...\n');
    plot_dispatch_results_v6(X_test, cost_details, ESS_params);
    fprintf('绘图测试成功！\n');
catch ME
    fprintf('绘图测试失败！\n');
    fprintf('错误信息: %s\n', ME.message);
end

fprintf('\n=== v6版本测试完成 ===\n');
fprintf('主要改进:\n');
fprintf('1. 弃电放在最后环节，风光优先满足负荷和储能充电\n');
fprintf('2. 删除风光实际出力决策变量，改为弃电量和购电量变量\n');
fprintf('3. 新的调度逻辑更符合实际运行原则\n'); 