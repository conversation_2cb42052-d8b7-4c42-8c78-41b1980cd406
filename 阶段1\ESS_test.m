function [Population,penalty_E]=ESS_test(Population)
Ebat0=0.4;
for t=1:24
    if t==1
        if Population(t)>0
            Ebat1(1)=Ebat0+Population(t)*0.9;
            if Ebat1(1)>0.72
                Population(t)=(0.72-Ebat0)/0.9;
                Ebat1(1)=0.72;
            end
        else
            Ebat1(1)=Ebat0+Population(t)/0.9;
            if Ebat1(1)<0.16
                Population(t)=(0.16-Ebat0)*0.9;
                Ebat1(1)=0.16;
                %                         Ebat1(1)=Ebat0+Population(t,15)/0.9;
            end
        end

    elseif t==24
        if Ebat1(23)>Ebat0%需要放电回归原始Ebat
            Population(t)=(Ebat0-Ebat1(23))*0.9;
        else                        %需要充电回归原始Ebat
            Population(t)=(Ebat0-Ebat1(23))/0.9;
        end

    else%t=2:23
        if Population(t)>0
            Ebat1(t)=Ebat1(t-1)+Population(t)*0.9;
            if Ebat1(t)>0.72
                Population(t)=floor((0.72-Ebat1(t-1))/0.9*1000)/1000;
                %                             Ebat1(t)=0.72;
                Ebat1(t)=Ebat1(t-1)+Population(t)*0.9;
            end
        else
            Ebat1(t)=Ebat1(t-1)+Population(t)/0.9;
            if Ebat1(t)<0.16
                Population(t)=ceil((0.16-Ebat1(t-1))*0.9*1000)/1000;
                %                             Ebat1(t)=0.16;
                Ebat1(t)=Ebat1(t-1)+Population(t)/0.9;
            end
        end
    end
end

if Population(t)>0.4%校验末时充放电功率是否满足约束
    penalty_E=Population(t)*1000000;
elseif Population(t)<-0.4
    penalty_E=Population(t)*1000000;
else
    penalty_E=0;
end

