%% 交直流柔性配电网储能设备选址定容主程序 (v4版本 - 修正版)
% 基于MMO_CLRPSO算法的双目标优化
% 修正：SOC更新方向、成本参数统一、最低消纳率约束、功率容量配比约束
clear all; clc; close all;

%% 系统参数设置
global mpc_base Pwt Ppv price shift_load cut_load Load_ac Load_dc
global N_SL N_CL Nwt Npv ac acq dc
global ESS_params % 储能参数

% 加载基础数据
mpc_base = case33ACDC;
flexible_load;
power_load;

% 修正3: 储能参数设置 - 调整成本参数以平衡储能投资与弃电成本
ESS_params.Ce = 700;        % 储能单位容量成本 (元/kWh)
ESS_params.Cp = 400;        % 储能单位功率成本 (元/kW)
ESS_params.Cmaint = 180;    % 年运行维护成本 (元/kW·年)
ESS_params.ir = 0.015;      % 通胀膨胀率
ESS_params.dr = 0.09;       % 贴现率
ESS_params.Y = 15;          % 储能寿命 (年)
ESS_params.lambda = 0.08;   % 净报废成本系数
ESS_params.E_max = 2000;    % 最大安装容量 (kWh) - 进一步提高以匹配负荷
ESS_params.P_max = 800;     % 最大充放电功率 (kW) - 提高功率以应对峰值

% 风光数据处理 - 与新配电网结构匹配
Pwt = [Pwt1' Pwt2' Pwt3'];
Ppv = [Ppv1' Ppv2' Ppv3'];

% 风光基准容量 - 与新配电网结构中的发电机额定容量一致
wind_base = [0.5, 0.5, 0.5]; % 各风电场额定容量 (节点5,24,29) - 与case33ACDC.m一致
pv_base = [0.25, 0.25, 0.25]; % 各光伏电站额定容量 (节点10,17,22) - 与case33ACDC.m一致

% 为了匹配更高的负荷需求，合理增大风光装机规模
wind_scale = 3.5;  % 风电扩容倍数
pv_scale = 4.0;    % 光伏扩容倍数

wind_base_new = wind_base * wind_scale;  % 风电总装机：5.25MW
pv_base_new = pv_base * pv_scale;        % 光伏总装机：3.0MW

fprintf('=== 风光装机调整 (v4版本) ===\n');
fprintf('配电网结构风电装机: %.2f MW -> 优化模型风电装机: %.2f MW\n', sum(wind_base), sum(wind_base_new));
fprintf('配电网结构光伏装机: %.2f MW -> 优化模型光伏装机: %.2f MW\n', sum(pv_base), sum(pv_base_new));
fprintf('风光总装机: %.2f MW (系统最大负荷约6.2MW)\n', sum(wind_base_new) + sum(pv_base_new));
fprintf('风光装机占最大负荷比例: %.1f%%\n', (sum(wind_base_new) + sum(pv_base_new))/6.2*100);

% 负荷数据处理
ratio = mpc_base.bus(:,3:5)./sum(mpc_base.bus(:,3:5));
ratioq = mpc_base.bus(:,4)./mpc_base.bus(:,3);
ratioq(isnan(ratioq)) = 0;

for i = 1:24
    ac(i,:) = Load_ac(i) * ratio(:,1);
    acq(i,:) = ac(i,:) .* ratioq';
    dc(i,:) = Load_dc(i) * ratio(:,3);
end

N_SL = size(shift_load,1);
N_CL = size(cut_load,1);
Nwt = 3; Npv = 3;

fprintf('系统参数:\n');
fprintf('平移负荷数量 N_SL: %d\n', N_SL);
fprintf('削减负荷数量 N_CL: %d\n', N_CL);

%% 优化变量定义 - v4版本
% 决策变量：[ESS1_node, ESS2_node, ESS1_capacity, ESS2_capacity, ESS1_power, ESS2_power, 
%           风光实际上网功率(24*6), 储能功率(24*2), 平移负荷时间(N_SL), 削减负荷量(24*N_CL), 削减负荷开关(24*N_CL)]

% 详细计算决策变量数量
n_ess_vars = 6;                    % 储能选址定容变量
n_renewable_vars = 24 * 6;         % 风光实际上网功率变量 (24小时 × 6个风光场站)
n_ess_power_vars = 24 * 2;         % 储能功率变量 (24小时 × 2个储能)
n_shift_time_vars = N_SL;          % 平移负荷时间变量
n_cut_amount_vars = 24 * N_CL;     % 削减负荷量变量 (24小时 × N_CL个削减负荷)
n_cut_switch_vars = 24 * N_CL;     % 削减负荷开关变量 (24小时 × N_CL个削减负荷)

n_var = n_ess_vars + n_renewable_vars + n_ess_power_vars + n_shift_time_vars + n_cut_amount_vars + n_cut_switch_vars;
n_obj = 2; % 目标函数数

fprintf('决策变量详细计算 (v4版本):\n');
fprintf('储能选址定容: %d\n', n_ess_vars);
fprintf('风光实际上网功率: 24×6 = %d\n', n_renewable_vars);
fprintf('储能功率: 24×2 = %d\n', n_ess_power_vars);
fprintf('平移负荷时间: %d\n', n_shift_time_vars);
fprintf('削减负荷量: 24×%d = %d\n', N_CL, n_cut_amount_vars);
fprintf('削减负荷开关: 24×%d = %d\n', N_CL, n_cut_switch_vars);
fprintf('总决策变量数: %d\n', n_var);

% 验证计算
expected_vars = 6 + 144 + 48 + N_SL + 96 + 96;
fprintf('预期变量数: %d (验证: %s)\n', expected_vars, iif(n_var == expected_vars, '正确', '错误'));

% 变量边界
VRmin = zeros(1, n_var);
VRmax = zeros(1, n_var);

% 储能选址边界 (节点2-33，不能选择节点1)
VRmin(1:2) = 2;
VRmax(1:2) = 33;

% 修正2: 储能容量边界 (kWh) - 考虑功率容量配比，最小容量应满足2小时充放电
VRmin(3:4) = 0;
VRmax(3:4) = ESS_params.E_max;

% 储能功率边界 (kW)
VRmin(5:6) = 0;
VRmax(5:6) = ESS_params.P_max;

% 风光实际上网功率边界 - v4版本：改为实际上网功率(MW)，范围从0到预测出力
idx = 7;
for t = 1:24
    % 风电场1,2,3的实际上网功率边界 - 使用新的基准容量
    VRmin(idx:idx+2) = [0 0 0];                    % 最小上网功率为0
    VRmax(idx:idx+2) = [Pwt(t,1)*wind_base_new(1) Pwt(t,2)*wind_base_new(2) Pwt(t,3)*wind_base_new(3)]; % 最大上网功率为预测出力
    % 光伏电站1,2,3的实际上网功率边界 - 使用新的基准容量
    VRmin(idx+3:idx+5) = [0 0 0];                  % 最小上网功率为0
    VRmax(idx+3:idx+5) = [Ppv(t,1)*pv_base_new(1) Ppv(t,2)*pv_base_new(2) Ppv(t,3)*pv_base_new(3)]; % 最大上网功率为预测出力
    idx = idx + 6;
end

% 储能充放电功率边界 (MW) - 根据新的功率上限调整
for t = 1:24
    VRmin(idx:idx+1) = [-0.8 -0.8];  % 负值为充电，对应800kW
    VRmax(idx:idx+1) = [0.8 0.8];    % 正值为放电，对应800kW
    idx = idx + 2;
end

% 平移负荷时间边界
time_SL1 = shift_load(:,4);
time_SL2 = shift_load(:,5) - shift_load(:,6) + 1;
VRmin(idx:idx+N_SL-1) = time_SL1';
VRmax(idx:idx+N_SL-1) = time_SL2';
idx = idx + N_SL;

% 削减负荷量边界
for t = 1:24
    VRmin(idx:idx+N_CL-1) = zeros(1,N_CL);
    VRmax(idx:idx+N_CL-1) = cut_load(:,t+1)';
    idx = idx + N_CL;
end

% 削减负荷开关边界
for t = 1:24
    VRmin(idx:idx+N_CL-1) = -10 * ones(1,N_CL);
    VRmax(idx:idx+N_CL-1) = 10 * ones(1,N_CL);
    idx = idx + N_CL;
end

% 验证边界向量长度
fprintf('边界向量长度验证: VRmin=%d, VRmax=%d, n_var=%d\n', length(VRmin), length(VRmax), n_var);
if length(VRmin) ~= n_var || length(VRmax) ~= n_var
    error('边界向量长度与决策变量数量不匹配！');
end

%% MMO_CLRPSO算法参数
popsize = 100;
Max_Gen = 30;  % 适中的优化代数

fprintf('\n开始储能选址定容优化 (v4版本 - 修正版)...\n');
fprintf('决策变量数: %d\n', n_var);
fprintf('目标函数数: %d\n', n_obj);
fprintf('储能最大容量: %d kWh\n', ESS_params.E_max);
fprintf('储能最大功率: %d kW\n', ESS_params.P_max);
fprintf('储能容量成本: %d 元/kWh\n', ESS_params.Ce);
fprintf('储能功率成本: %d 元/kW\n', ESS_params.Cp);
fprintf('弃风弃光成本: 12.0 元/kWh\n'); % 修正3: 统一显示弃电成本
fprintf('最低风光消纳率: 70%%\n'); % 修正4: 显示最低消纳率约束

%% 调用MMO_CLRPSO算法
[ps, pf] = MMO_CLRPSO_ESS_v2('ESS_objective', VRmin, VRmax, n_obj, popsize, Max_Gen);

%% 目标函数归一化处理
fprintf('\n进行目标函数归一化处理...\n');

% 计算目标函数的最小值和最大值
f1_min = min(pf(:,1));
f1_max = max(pf(:,1));
f2_min = min(pf(:,2));
f2_max = max(pf(:,2));

fprintf('目标函数1 (储能全寿命周期成本): %.2f - %.2f 万元\n', f1_min/10000, f1_max/10000);
fprintf('目标函数2 (系统运行成本): %.2f - %.2f 万元\n', f2_min/10000, f2_max/10000);

% Min-Max归一化
pf_norm = zeros(size(pf));
if f1_max > f1_min
    pf_norm(:,1) = (pf(:,1) - f1_min) / (f1_max - f1_min);
else
    pf_norm(:,1) = 0.5 * ones(size(pf,1), 1);
end

if f2_max > f2_min
    pf_norm(:,2) = (pf(:,2) - f2_min) / (f2_max - f2_min);
else
    pf_norm(:,2) = 0.5 * ones(size(pf,1), 1);
end

%% 基于归一化后的Pareto前沿选择最优解
% 计算到理想点(0,0)的距离
distances = sqrt(pf_norm(:,1).^2 + pf_norm(:,2).^2);
[~, best_idx] = min(distances);
best_solution = ps(best_idx,:);

%% 结果分析
fprintf('\n优化完成！\n');
fprintf('获得Pareto解的数量: %d\n', size(ps,1));

fprintf('\n最优解分析 (v4版本 - 修正版):\n');
fprintf('储能1安装节点: %d\n', round(best_solution(1)));
fprintf('储能2安装节点: %d\n', round(best_solution(2)));
fprintf('储能1额定容量: %.2f kWh\n', best_solution(3));
fprintf('储能2额定容量: %.2f kWh\n', best_solution(4));
fprintf('储能1额定功率: %.2f kW\n', best_solution(5));
fprintf('储能2额定功率: %.2f kW\n', best_solution(6));

% 检查功率容量配比
if best_solution(3) > 0
    fprintf('储能1功率容量配比: %.2f h (建议≥2h)\n', best_solution(3)/best_solution(5));
end
if best_solution(4) > 0
    fprintf('储能2功率容量配比: %.2f h (建议≥2h)\n', best_solution(4)/best_solution(6));
end

% 计算最优解的目标函数值
[f1, f2, cost_details] = ESS_objective(best_solution);
fprintf('储能全寿命周期成本: %.2f 万元\n', f1/10000);
fprintf('系统运行成本: %.2f 万元\n', f2/10000);
fprintf('归一化后的目标函数值: f1_norm=%.3f, f2_norm=%.3f\n', pf_norm(best_idx,1), pf_norm(best_idx,2));
fprintf('到理想点的距离: %.3f\n', distances(best_idx));

%% 详细成本分析
fprintf('\n=== 详细成本分析 ===\n');
fprintf('目标函数1 - 储能全寿命周期成本 (%.2f万元):\n', f1/10000);
fprintf('  储能投资成本: %.2f 万元\n', cost_details.C_i/10000);
fprintf('  储能运维成本: %.2f 万元\n', cost_details.C_m/10000);
fprintf('  储能报废成本: %.2f 万元\n', cost_details.C_d/10000);
if isfield(cost_details, 'capacity_power_penalty')
    fprintf('  功率容量配比惩罚: %.2f 万元\n', cost_details.capacity_power_penalty/10000);
end

fprintf('\n目标函数2 - 系统运行成本 (%.2f万元):\n', f2/10000);
fprintf('  电网交互成本: %.2f 万元\n', cost_details.grid_cost/10000);
fprintf('  弃风弃光成本: %.2f 万元 (%.1f元/kWh)\n', cost_details.curtail_cost/10000, cost_details.curtail_cost_rate);
fprintf('  网损成本: %.2f 万元\n', cost_details.loss_cost/10000);
fprintf('  平移负荷补偿成本: %.2f 万元\n', cost_details.C_SL/10000);
fprintf('  削减负荷补偿成本: %.2f 万元\n', cost_details.C_CL/10000);
fprintf('  约束惩罚成本: %.2f 万元\n', cost_details.penalty_total/10000);
fprintf('  储能约束惩罚成本: %.2f 万元\n', cost_details.penalty_E/10000);
if isfield(cost_details, 'min_utilization_penalty')
    fprintf('  最低消纳率惩罚成本: %.2f 万元\n', cost_details.min_utilization_penalty/10000);
end

%% 分析弃风弃光情况
fprintf('\n=== 弃风弃光分析 ===\n');

total_wind_forecast = 0;
total_pv_forecast = 0;
total_wind_actual = 0;
total_pv_actual = 0;
total_wind_curtailed = 0;
total_pv_curtailed = 0;

idx = 7;
for t = 1:24
    % 获取实际上网功率
    wind_actual = best_solution(idx:idx+2);
    pv_actual = best_solution(idx+3:idx+5);
    
    % 计算预测出力 - 使用新的基准容量
    wind_forecast = [Pwt(t,1)*wind_base_new(1), Pwt(t,2)*wind_base_new(2), Pwt(t,3)*wind_base_new(3)];
    pv_forecast = [Ppv(t,1)*pv_base_new(1), Ppv(t,2)*pv_base_new(2), Ppv(t,3)*pv_base_new(3)];
    
    % 计算弃风弃光量
    wind_curtailed = max(0, wind_forecast - wind_actual);
    pv_curtailed = max(0, pv_forecast - pv_actual);
    
    % 累计统计
    total_wind_forecast = total_wind_forecast + sum(wind_forecast);
    total_pv_forecast = total_pv_forecast + sum(pv_forecast);
    total_wind_actual = total_wind_actual + sum(wind_actual);
    total_pv_actual = total_pv_actual + sum(pv_actual);
    total_wind_curtailed = total_wind_curtailed + sum(wind_curtailed);
    total_pv_curtailed = total_pv_curtailed + sum(pv_curtailed);
    
    idx = idx + 6;
end

fprintf('风电预测出力: %.2f MWh, 实际上网: %.2f MWh, 弃风: %.2f MWh (%.1f%%)\n', ...
    total_wind_forecast, total_wind_actual, total_wind_curtailed, ...
    total_wind_curtailed/total_wind_forecast*100);
fprintf('光伏预测出力: %.2f MWh, 实际上网: %.2f MWh, 弃光: %.2f MWh (%.1f%%)\n', ...
    total_pv_forecast, total_pv_actual, total_pv_curtailed, ...
    total_pv_curtailed/total_pv_forecast*100);
fprintf('总弃风弃光: %.2f MWh, 弃风弃光成本: %.2f 万元 (%.1f元/kWh)\n', ...
    total_wind_curtailed + total_pv_curtailed, ...
    cost_details.curtail_cost_rate * (total_wind_curtailed + total_pv_curtailed) * 1000 / 10000, ...
    cost_details.curtail_cost_rate);

%% 绘制Pareto前沿 (原始值和归一化值)
figure(1);
subplot(1,2,1);
scatter(pf(:,1)/10000, pf(:,2)/10000, 50, 'filled', 'blue');
xlabel('储能全寿命周期成本 (万元)');
ylabel('系统运行成本 (万元)');
title('Pareto前沿 (原始值) - v4修正版');
legend('Pareto解', 'Location', 'best');
grid on;

subplot(1,2,2);
scatter(pf_norm(:,1), pf_norm(:,2), 50, 'filled', 'blue');
xlabel('归一化储能全寿命周期成本');
ylabel('归一化系统运行成本');
title('Pareto前沿 (归一化) - v4修正版');
legend('Pareto解', 'Location', 'best');
grid on;
xlim([-0.1, 1.1]);
ylim([-0.1, 1.1]);

%% 绘制调度结果 - 使用分离图表
plot_dispatch_results_separated(best_solution);

fprintf('\nv4版本修正完成！\n');
fprintf('主要修正:\n');
fprintf('1. 修正SOC更新方向：正功率=放电，负功率=充电\n');
fprintf('2. 增加功率-容量配比约束：P ≤ C/2h\n');
fprintf('3. 统一成本参数：Ce=2500元/kWh, Cp=1000元/kW, 弃电=12元/kWh\n');
fprintf('4. 增加最低可再生消纳率约束：风光≥70%%利用率\n');
fprintf('5. 修正cut_switch判断逻辑：使用>0.5判断开关状态\n');
fprintf('6. 放宽VSC约束：从0.4放宽到0.5，增加储能AC侧无功支持\n');

%% 辅助函数
function result = iif(condition, true_value, false_value)
% 简单的三元运算符实现
if condition
    result = true_value;
else
    result = false_value;
end
end 