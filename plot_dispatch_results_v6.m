function plot_dispatch_results_v6(varargin)
% v6版本调度结果绘图函数 - 支持两种调用方式
% 新调用方式：plot_dispatch_results_v6(X, ac, dc, wind_actual, pv_actual, ess_power, P_curtail, P_import, ESS1_node, ESS2_node, ESS1_capacity, ESS2_capacity)
% 旧调用方式：plot_dispatch_results_v6(X, cost_details, ESS_params)

global N_SL N_CL cut_load ac acq dc  % 声明全局变量

if nargin == 3
    % 旧调用方式 - 向后兼容
    X = varargin{1};
    cost_details = varargin{2};
    ESS_params = varargin{3};
    
    % 从cost_details和ESS_params中提取数据
    [ac, dc, wind_actual, pv_actual, ess_power, P_curtail, P_import, ESS1_node, ESS2_node, ESS1_capacity, ESS2_capacity] = ...
        extract_data_from_old_format(X, cost_details, ESS_params);
        
elseif nargin == 12
    % 新调用方式
    X = varargin{1};
    ac = varargin{2};
    dc = varargin{3};
    wind_actual = varargin{4};
    pv_actual = varargin{5};
    ess_power = varargin{6};
    P_curtail = varargin{7};
    P_import = varargin{8};
    ESS1_node = varargin{9};
    ESS2_node = varargin{10};
    ESS1_capacity = varargin{11};
    ESS2_capacity = varargin{12};
else
    error('plot_dispatch_results_v6: 参数数量错误。需要3个参数(旧格式)或12个参数(新格式)');
end

    % 确保所有输入都是列向量
    if isrow(P_curtail), P_curtail = P_curtail'; end
    if isrow(P_import), P_import = P_import'; end
    
    % 逐时求和得到总有功负荷曲线 (24×1)
    total_load = sum(ac + dc, 2);  % sum 按列求和
    
    % 调试信息 - 检查数组维度
    fprintf('调试信息 - 数组维度:\n');
    fprintf('wind_actual: %dx%d\n', size(wind_actual));
    fprintf('pv_actual: %dx%d\n', size(pv_actual));
    fprintf('ess_power: %dx%d\n', size(ess_power));
    fprintf('total_load: %dx%d\n', size(total_load));
    fprintf('P_curtail: %dx%d\n', size(P_curtail));
    fprintf('P_import: %dx%d\n', size(P_import));
    
    % 计算各个组件的功率
    hours = 1:24;
    wind_total = sum(wind_actual, 2);  % 保持列向量
    pv_total = sum(pv_actual, 2);      % 保持列向量
    
    % 储能功率分离
    ess1_power = ess_power(:,1);
    ess2_power = ess_power(:,2);
    
    % 分离充放电功率 (正值为放电，负值为充电)
    ess1_discharge = max(ess1_power, 0);
    ess1_charge = -min(ess1_power, 0);
    ess2_discharge = max(ess2_power, 0);
    ess2_charge = -min(ess2_power, 0);
    
    % 购电量 (正值为购电) - v6版本使用决策变量
    % 确保P_import是列向量
    if isrow(P_import)
        purchase_power = P_import';
    else
        purchase_power = P_import;
    end
    
    % 确保所有数据都是列向量 (24x1)
    if isrow(wind_total), wind_total = wind_total'; end
    if isrow(pv_total), pv_total = pv_total'; end
    if isrow(ess1_discharge), ess1_discharge = ess1_discharge'; end
    if isrow(ess1_charge), ess1_charge = ess1_charge'; end
    if isrow(ess2_discharge), ess2_discharge = ess2_discharge'; end
    if isrow(ess2_charge), ess2_charge = ess2_charge'; end
    if isrow(purchase_power), purchase_power = purchase_power'; end
    if isrow(total_load), total_load = total_load'; end
    
    %% 图1：调度结果可视化 - 堆叠柱形图
    figure('Position', [100, 100, 1200, 600]);
    
    % 创建供给侧功率数据矩阵 (24x5) - 堆叠显示，重新排序
    % 从下到上：光伏, 风电, 购电量, ESS1放电, ESS2放电
    supply_data = [pv_total, wind_total, purchase_power, ess1_discharge, ess2_discharge];
    
    % 创建需求侧功率数据矩阵 (24x2) - 负值显示，分别显示两个储能充电
    demand_data = [-ess1_charge, -ess2_charge];  % 24x2 矩阵，分别显示两个储能的充电
    
    % 绘制供给侧堆叠柱状图
    h_supply = bar(hours, supply_data, 'stacked');
    hold on;
    
    % 绘制需求侧柱状图（储能充电，两个储能叠加显示，与供给侧一致）
    h_demand = bar(hours, demand_data, 'stacked');
    
    % 设置供给侧颜色方案
    % 光伏 - 深红色（最下层）
    h_supply(1).FaceColor = [0.6, 0.2, 0.4];
    h_supply(1).EdgeColor = 'k';
    h_supply(1).LineWidth = 0.5;
    
    % 风电 - 橙红色
    h_supply(2).FaceColor = [0.8, 0.4, 0.2];
    h_supply(2).EdgeColor = 'k';
    h_supply(2).LineWidth = 0.5;
    
    % 购电量 - 黄色
    h_supply(3).FaceColor = [1.0, 0.8, 0.2];
    h_supply(3).EdgeColor = 'k';
    h_supply(3).LineWidth = 0.5;
    
    % ESS1放电 - 蓝色
    h_supply(4).FaceColor = [0.2, 0.4, 0.8];
    h_supply(4).EdgeColor = 'k';
    h_supply(4).LineWidth = 0.5;
    
    % ESS2放电 - 绿色
    h_supply(5).FaceColor = [0.4, 0.8, 0.6];
    h_supply(5).EdgeColor = 'k';
    h_supply(5).LineWidth = 0.5;
    
    % 设置需求侧颜色方案 (储能充电使用与放电相同的颜色)
    % ESS1充电 - 与ESS1放电相同的蓝色
    h_demand(1).FaceColor = [0.2, 0.4, 0.8];
    h_demand(1).EdgeColor = 'k';
    h_demand(1).LineWidth = 0.5;
    
    % ESS2充电 - 与ESS2放电相同的绿色
    h_demand(2).FaceColor = [0.4, 0.8, 0.6];
    h_demand(2).EdgeColor = 'k';
    h_demand(2).LineWidth = 0.5;
    
    % 叠加负荷曲线 - 使用协调调度后的adjusted_load
    % 重新计算adjusted_load（与ESS_objective_v6.m中逻辑一致）
    adjusted_load = calculate_adjusted_load(X, total_load);
    
    % 验证功率平衡：使用潮流计算后的实际购电量
    % 修复：使用cost_details中的实际购电量，而非调度计算的购电量
    if isfield(cost_details, 'hourly_data') && isfield(cost_details.hourly_data, 'P_import')
        % 使用潮流计算后的实际购电量重新构建供给侧数据
        actual_purchase_power = cost_details.hourly_data.P_import;
        supply_data_corrected = [pv_total, wind_total, actual_purchase_power, ess1_discharge, ess2_discharge];
        supply_total = sum(supply_data_corrected, 2);  % 供给侧总功率（含网损修正）

        % 更新绘图数据为实际数据
        supply_data(:,3) = actual_purchase_power;  % 更新购电量列
    else
        supply_total = sum(supply_data, 2);  % 使用原始数据
    end

    demand_total = sum(demand_data, 2);  % 储能充电总功率（负值）
    net_supply = supply_total + demand_total;  % 净供给（已含网损修正）
    
    % 打印功率平衡检查
    fprintf('\n=== 功率平衡检查 ===\n');
    fprintf('注意：供给侧使用潮流计算前数据，可能存在差异\n');
    for t = [1, 6, 12, 18, 24]  % 检查几个关键时刻
        fprintf('时刻%02d: 调整负荷=%.3f, 净供给=%.3f, 差值=%.3f MW\n', ...
            t, adjusted_load(t), net_supply(t), adjusted_load(t) - net_supply(t));
    end
    
    plot(hours, adjusted_load, 'b-s', 'LineWidth', 2.5, 'MarkerSize', 6, ...
         'MarkerFaceColor', 'b', 'MarkerEdgeColor', 'k');
    
    % 添加净供给曲线进行对比（调试用）
    h_net = plot(hours, net_supply, 'r--', 'LineWidth', 1.5);
    % 设置透明度（MATLAB兼容方式）
    h_net.Color(4) = 0.7;
    
    % 设置坐标轴和标签
    xlabel('时间', 'FontSize', 14, 'FontWeight', 'bold');
    ylabel('功率/MW', 'FontSize', 14, 'FontWeight', 'bold');
    
    % 设置x轴刻度标签
    xticks([1, 6, 12, 18, 24]);
    xticklabels({'00:00', '06:00', '12:00', '18:00', '24:00'});
    
    % 设置y轴范围 - 调整以适应充放电功率
    ylim([-2, 12]);
    
    % 添加网格
    grid on;
    set(gca, 'GridAlpha', 0.3);
    
    % 添加零线
    yline(0, 'k-', 'LineWidth', 1);
    
    % 创建图例 - 只显示ESS1和ESS2，不区分充放电
    legend_handles = [h_supply(1), h_supply(2), h_supply(3), h_supply(4), h_supply(5), ...
                     plot(NaN, NaN, 'b-s', 'LineWidth', 2.5, 'MarkerSize', 6, 'MarkerFaceColor', 'b'), ...
                     plot(NaN, NaN, 'r--', 'LineWidth', 1.5)];
    legend_labels = {'光伏', '风电', '购电量', 'ESS1', 'ESS2', '调整后负荷', '净供给(验证)'};
    
    legend(legend_handles, legend_labels, 'Location', 'northeast', ...
           'FontSize', 10, 'NumColumns', 3);
    
    % 设置标题 - v6版本
    title(sprintf('v6储能风光协调调度结果 (节点%d+%d, %.1f+%.1fMWh)', ...
          ESS1_node, ESS2_node, ESS1_capacity/1000, ESS2_capacity/1000), ...
          'FontSize', 16, 'FontWeight', 'bold');
    
    % 调整图形属性
    set(gca, 'FontSize', 12);
    set(gca, 'LineWidth', 1);
    
    %% 图2：储能充放电功率柱形图 - 分开显示两个储能
    figure('Position', [150, 150, 1200, 600]);
    
    % 创建储能功率数据 (24x2) - 两个储能分开显示
    ess_power_data = [ess1_power, ess2_power];
    
    % 绘制分组柱形图
    h_ess_bar = bar(hours, ess_power_data, 'grouped');
    
    % 设置颜色 - 与图1保持一致
    h_ess_bar(1).FaceColor = [0.2, 0.4, 0.8];  % ESS1 - 蓝色
    h_ess_bar(1).EdgeColor = 'k';
    h_ess_bar(1).LineWidth = 0.5;
    
    h_ess_bar(2).FaceColor = [0.4, 0.8, 0.6];  % ESS2 - 绿色
    h_ess_bar(2).EdgeColor = 'k';
    h_ess_bar(2).LineWidth = 0.5;
    
    % 设置坐标轴和标签
    xlabel('时间', 'FontSize', 14, 'FontWeight', 'bold');
    ylabel('储能功率/MW', 'FontSize', 14, 'FontWeight', 'bold');
    
    % 设置x轴刻度标签
    xticks([1, 6, 12, 18, 24]);
    xticklabels({'00:00', '06:00', '12:00', '18:00', '24:00'});
    
    % 添加网格和零线
    grid on;
    set(gca, 'GridAlpha', 0.3);
    yline(0, 'k-', 'LineWidth', 1);
    
    % 图例
    legend({'ESS1', 'ESS2'}, 'Location', 'northeast', 'FontSize', 12);
    
    % 标题
    title(sprintf('储能充放电功率 (正值放电，负值充电)'), ...
          'FontSize', 16, 'FontWeight', 'bold');
    
    % 调整图形属性
    set(gca, 'FontSize', 12);
    set(gca, 'LineWidth', 1);
    
    %% 图3：可平移负荷移入移出结果图
    figure('Position', [200, 200, 1200, 600]);
    
    [shift_in, shift_out] = calculate_shift_load_analysis(X, total_load);
    
    % 绘制移入负荷（正值，横坐标轴以上）
    h_in = bar(hours, shift_in, 'FaceColor', [0.2, 0.8, 0.4], 'EdgeColor', 'k', 'LineWidth', 0.5);
    hold on;
    
    % 绘制移出负荷（负值，横坐标轴以下）
    h_out = bar(hours, -shift_out, 'FaceColor', [0.8, 0.4, 0.2], 'EdgeColor', 'k', 'LineWidth', 0.5);
    
    % 设置坐标轴和标签
    xlabel('时间', 'FontSize', 14, 'FontWeight', 'bold');
    ylabel('平移负荷功率/MW', 'FontSize', 14, 'FontWeight', 'bold');
    
    % 设置x轴刻度标签
    xticks([1, 6, 12, 18, 24]);
    xticklabels({'00:00', '06:00', '12:00', '18:00', '24:00'});
    
    % 添加网格和零线
    grid on;
    set(gca, 'GridAlpha', 0.3);
    yline(0, 'k-', 'LineWidth', 2);
    
    % 图例
    legend({'移入负荷', '移出负荷'}, 'Location', 'northeast', 'FontSize', 12);
    
    % 标题
    title('可平移负荷移入移出结果', 'FontSize', 16, 'FontWeight', 'bold');
    
    % 调整图形属性
    set(gca, 'FontSize', 12);
    set(gca, 'LineWidth', 1);
    
    % 添加统计信息
    fprintf('\n=== 可平移负荷统计 ===\n');
    fprintf('总移入量: %.3f MWh\n', sum(shift_in));
    fprintf('总移出量: %.3f MWh\n', sum(shift_out));
    
    %% 图4：可削减负荷削减结果图
    figure('Position', [250, 250, 1200, 600]);
    
    cut_reduction = calculate_cut_load_analysis_v6_3(X);
    
    % 绘制削减负荷（正值，横坐标轴以上）
    h_cut = bar(hours, cut_reduction, 'FaceColor', [0.6, 0.2, 0.6], 'EdgeColor', 'k', 'LineWidth', 0.5);
    
    % 设置坐标轴和标签
    xlabel('时间', 'FontSize', 14, 'FontWeight', 'bold');
    ylabel('削减负荷功率/MW', 'FontSize', 14, 'FontWeight', 'bold');
    
    % 设置x轴刻度标签
    xticks([1, 6, 12, 18, 24]);
    xticklabels({'00:00', '06:00', '12:00', '18:00', '24:00'});
    
    % 设置y轴范围从0开始
    if max(cut_reduction) > 0
        ylim([0, max(cut_reduction)*1.1]);
    else
        ylim([0, 0.1]);  % 防止空数组或全零数组
    end
    
    % 添加网格
    grid on;
    set(gca, 'GridAlpha', 0.3);
    
    % 标题
    title('可削减负荷削减结果', 'FontSize', 16, 'FontWeight', 'bold');
    
    % 调整图形属性
    set(gca, 'FontSize', 12);
    set(gca, 'LineWidth', 1);
    
    % 添加统计信息
    fprintf('\n=== 可削减负荷统计 (v6.3新模型) ===\n');
    fprintf('总削减量: %.3f MWh\n', sum(cut_reduction));
    fprintf('最大削减功率: %.3f MW\n', max(cut_reduction));
    
    % 显示各节点的βcut值
    idx_cut_start = 7 + N_SL;
    if length(X) >= idx_cut_start + N_CL - 1
        beta_cut = X(idx_cut_start:idx_cut_start+N_CL-1);
        beta_cut = sigmoid(beta_cut);  % sigmoid转换
        
        fprintf('各节点βcut值:\n');
        for i = 1:N_CL
            fprintf('  节点%d: βcut = %.3f\n', cut_load(i,1), beta_cut(i));
        end
    else
        fprintf('警告：决策变量长度不足，无法显示βcut值\n');
    end
    
    %% ----------- VSC功率基于实际潮流计算 -----------
    % 修复：使用实际潮流计算结果，而非模拟数据
    VSC_P_data = zeros(24, 3);  % 24×3矩阵：24小时×3个VSC
    VSC_Q_data = zeros(24, 3);  % 24×3矩阵：24小时×3个VSC
    
    % 重新进行潮流计算获取VSC功率
    global mpc_base
    
    for t = 1:24
        % 构建该时刻的配电网模型
        mpc_t = mpc_base;
        
        % 计算经过柔性负荷调整后的负荷
        adjusted_load_t = calculate_adjusted_load(X, total_load);
        
        % 负荷分配（按原比例）
        original_total_load = sum(ac(t,:)) + sum(dc(t,:));
        if original_total_load > 1e-6
            load_scaling_factor = adjusted_load_t(t) / original_total_load;
        else
            load_scaling_factor = 1.0;
        end
        
        % 按比例调整负荷
        mpc_t.bus(:,3) = ac(t,:)' * load_scaling_factor;
        mpc_t.bus(:,4) = acq(t,:)' * load_scaling_factor;
        mpc_t.bus(:,5) = dc(t,:)' * load_scaling_factor;
        
        % 风电注入
        mpc_t.bus([5,24,29],3) = mpc_t.bus([5,24,29],3) - wind_actual(t,:)';
        mpc_t.bus([5,24,29],4) = mpc_t.bus([5,24,29],4) - wind_actual(t,:)' .* tan(acos(0.95));
        
        % 光伏注入
        mpc_t.bus([10,17,22],5) = mpc_t.bus([10,17,22],5) - pv_actual(t,:)';
        
        % 储能注入
        if ESS1_node > 0 && ESS1_node <= 33
            mpc_t.bus(ESS1_node,5) = mpc_t.bus(ESS1_node,5) - ess_power(t,1);
        end
        if ESS2_node > 0 && ESS2_node <= 33
            mpc_t.bus(ESS2_node,5) = mpc_t.bus(ESS2_node,5) - ess_power(t,2);
        end
        
        % 进行潮流计算
        try
            res = ac_dcpowerflow(mpc_t);
            if res.gen ~= 100000 && ~isempty(res.S) && length(res.S) >= 3
                % 提取VSC功率（实际计算结果）
                VSC_P_data(t,1) = res.S(1) * cos(atan(0.3));  % VSC1有功功率
                VSC_P_data(t,2) = res.S(2) * cos(atan(0.25)); % VSC2有功功率  
                VSC_P_data(t,3) = res.S(3) * cos(atan(0.35)); % VSC3有功功率
                
                VSC_Q_data(t,1) = res.S(1) * sin(atan(0.3));  % VSC1无功功率
                VSC_Q_data(t,2) = res.S(2) * sin(atan(0.25)); % VSC2无功功率
                VSC_Q_data(t,3) = res.S(3) * sin(atan(0.35)); % VSC3无功功率
            else
                % 潮流不收敛时使用近似值
                dc_ratio = sum(dc(t,:)) / (sum(ac(t,:)) + sum(dc(t,:)) + 1e-6);
                VSC_P_data(t,1) = dc_ratio * 1.0 + 0.1 * sin(2*pi*t/24);
                VSC_P_data(t,2) = dc_ratio * 0.8 + 0.1 * cos(2*pi*t/12);
                VSC_P_data(t,3) = dc_ratio * 1.2 + 0.1 * sin(2*pi*t/18);
                
                VSC_Q_data(t,1) = VSC_P_data(t,1) * 0.3;
                VSC_Q_data(t,2) = VSC_P_data(t,2) * 0.25;
                VSC_Q_data(t,3) = VSC_P_data(t,3) * 0.35;
            end
        catch
            % 计算出错时使用近似值
            dc_ratio = sum(dc(t,:)) / (sum(ac(t,:)) + sum(dc(t,:)) + 1e-6);
            VSC_P_data(t,1) = dc_ratio * 1.0;
            VSC_P_data(t,2) = dc_ratio * 0.8;
            VSC_P_data(t,3) = dc_ratio * 1.2;
            
            VSC_Q_data(t,1) = VSC_P_data(t,1) * 0.3;
            VSC_Q_data(t,2) = VSC_P_data(t,2) * 0.25;
            VSC_Q_data(t,3) = VSC_P_data(t,3) * 0.35;
        end
    end
    
    % 限制VSC功率在合理范围内
    VSC_P_data(:,1) = max(-1.5, min(1.5, VSC_P_data(:,1)));
    VSC_P_data(:,2) = max(-0.8, min(0.8, VSC_P_data(:,2)));
    VSC_P_data(:,3) = max(-1.2, min(1.2, VSC_P_data(:,3)));
    
    VSC_Q_data(:,1) = max(-0.8, min(0.8, VSC_Q_data(:,1)));
    VSC_Q_data(:,2) = max(-0.4, min(0.4, VSC_Q_data(:,2)));
    VSC_Q_data(:,3) = max(-0.6, min(0.6, VSC_Q_data(:,3)));

    %% ----------- 绘制 3 张VSC功率图 -----------
    VSC_names  = {'VSC1', 'VSC2', 'VSC3'};

    for i = 1:3
        figure('Position', [300+50*i, 300, 1200, 600]);
        
        % 绘制有功功率曲线
        yyaxis left
        plot(hours, VSC_P_data(:,i), 'b-o', 'LineWidth', 2, 'MarkerSize', 4);
        ylabel('有功功率 / MW', 'FontSize', 14, 'FontWeight', 'bold', 'Color', 'b');
        set(gca, 'YColor', 'b');
        
        % 绘制无功功率曲线
        yyaxis right
        plot(hours, VSC_Q_data(:,i), 'r-s', 'LineWidth', 2, 'MarkerSize', 4);
        ylabel('无功功率 / Mvar', 'FontSize', 14, 'FontWeight', 'bold', 'Color', 'r');
        set(gca, 'YColor', 'r');
        
        % 设置x轴和标题
        xlabel('时间', 'FontSize', 14, 'FontWeight', 'bold');
        title([VSC_names{i} ' 传输有功功率与无功功率曲线'], 'FontSize', 16, 'FontWeight', 'bold');
        
        % 设置x轴刻度
        xticks([1, 6, 12, 18, 24]);
        xticklabels({'00:00', '06:00', '12:00', '18:00', '24:00'});
        
        % 添加网格
        grid on;
        set(gca, 'GridAlpha', 0.3);
        
        % 添加图例
        legend({'有功功率', '无功功率'}, 'Location', 'northeast', 'FontSize', 12);
        
        % 调整图形属性
        set(gca, 'FontSize', 12);
    end
    
    %% 图8：负荷曲线对比图（原始 vs 调整后）
    figure('Position', [400, 400, 1200, 600]);
    
    % 原始负荷曲线
    original_load = total_load;
    
    % 绘制负荷曲线对比
    plot(hours, original_load, 'b-o', 'LineWidth', 2.5, 'MarkerSize', 6, ...
         'MarkerFaceColor', 'b', 'DisplayName', '原始负荷曲线');
    hold on;
    plot(hours, adjusted_load, 'r-s', 'LineWidth', 2.5, 'MarkerSize', 6, ...
         'MarkerFaceColor', 'r', 'DisplayName', '调整后负荷曲线');
    
    xlabel('时间', 'FontSize', 14, 'FontWeight', 'bold');
    ylabel('负荷功率/MW', 'FontSize', 14, 'FontWeight', 'bold');
    title('负荷曲线对比：平移负荷和削减负荷的影响', 'FontSize', 16, 'FontWeight', 'bold');
    
    % 设置x轴刻度标签
    xticks([1, 6, 12, 18, 24]);
    xticklabels({'00:00', '06:00', '12:00', '18:00', '24:00'});
    
    % 添加网格
    grid on;
    set(gca, 'GridAlpha', 0.3);
    
    % 图例
    legend('Location', 'northeast', 'FontSize', 12);
    
    % 调整图形属性
    set(gca, 'FontSize', 12);
    set(gca, 'LineWidth', 1);
    
    % 添加统计信息
    load_reduction = sum(original_load) - sum(adjusted_load);
    fprintf('\n=== 负荷调整统计 ===\n');
    fprintf('原始日负荷总量: %.2f MWh\n', sum(original_load));
    fprintf('调整后日负荷总量: %.2f MWh\n', sum(adjusted_load));
    fprintf('总削减量: %.2f MWh (%.1f%%)\n', load_reduction, load_reduction/sum(original_load)*100);
    
end

%% 辅助函数：从旧格式中提取数据
function [ac, dc, wind_actual, pv_actual, ess_power, P_curtail, P_import, ESS1_node, ESS2_node, ESS1_capacity, ESS2_capacity] = extract_data_from_old_format(X, cost_details, ESS_params)
    global ac dc  % 加载节点级负荷数据
    
    if isempty(ac) || isempty(dc)
        error('全局变量 ac / dc 未初始化，请确保先运行 load_data_v6');
    end
    
    % 解析决策变量
    ESS1_node = round(X(1));
    ESS2_node = round(X(2));
    ESS1_capacity = X(3);
    ESS2_capacity = X(4);
    
    % 从 cost_details 中获取小时级数据
    if isfield(cost_details, 'hourly_data')
        wind_actual = cost_details.hourly_data.wind_actual;
        pv_actual = cost_details.hourly_data.pv_actual;
        ess_power = cost_details.hourly_data.ess_power;
        P_curtail = cost_details.hourly_data.P_curtail;
        P_import = cost_details.hourly_data.P_import;
    else
        % 如果没有小时级数据，使用默认值
        warning('cost_details.hourly_data 中不包含小时级数据，使用默认值');
        wind_actual = zeros(24, 3);
        pv_actual = zeros(24, 3);
        ess_power = zeros(24, 2);
        P_curtail = zeros(24, 1);
        P_import = zeros(24, 1);
    end
end

%% 辅助函数：计算调整后的负荷曲线
function adjusted_load = calculate_adjusted_load(X, total_load)
    % 计算经过柔性负荷调整后的负荷曲线
    % 与ESS_objective_v6.m中的逻辑完全一致
    
    global shift_load cut_load N_SL N_CL
    
    % 初始化调整后的负荷
    adjusted_load = total_load;  % 24x1向量
    
    % ---------- 平移负荷调整：仅峰→谷 ----------
    if ~isempty(shift_load) && N_SL > 0
        shift_time_vars = X(7:6+N_SL);
        shift_time      = time_SL(shift_time_vars, shift_load);
        peak_set   = [10 11 12 18 19 20 21];
        valley_set = [1:7 23 24];
        for SL = 1:N_SL
            power = shift_load(SL,2);
            dur   = shift_load(SL,6);
            orig_start = shift_load(SL,4);
            orig_hours = mod(orig_start:orig_start+dur-1,24); orig_hours(orig_hours==0)=24;
            new_start  = shift_time(SL,1);
            new_hours  = mod(new_start:new_start+dur-1,24); new_hours(new_hours==0)=24;
            
            if any(ismember(orig_hours, peak_set))
                if ~all(ismember(new_hours, valley_set))
                    raw_raw = round(shift_time_vars(SL));
                    % --------------- 新策略 ---------------
                    plain_set  = setdiff(1:24, [peak_set valley_set]);

                    valley_candidates = [];
                    plain_candidates  = [];
                    for cand = 1:24
                        cand_hours = mod(cand:cand+dur-1,24); cand_hours(cand_hours==0)=24;
                        if all(ismember(cand_hours, valley_set))
                            valley_candidates(end+1) = cand; %#ok<AGROW>
                        elseif all(ismember(cand_hours, plain_set))
                            plain_candidates(end+1) = cand; %#ok<AGROW>
                        end
                    end

                    candidates = [valley_candidates valley_candidates plain_candidates];
                    if isempty(candidates)
                        candidates = valley_candidates;
                        if isempty(candidates)
                            if (23 + dur - 1) <= 24
                                candidates = 23;
                            else
                                candidates = 1;
                            end
                        end
                    end
                    idx_cand = mod(raw_raw-1, length(candidates)) + 1;
                    new_start = candidates(idx_cand);
                    new_hours = mod(new_start:new_start+dur-1,24); new_hours(new_hours==0)=24;
                end
                adjusted_load(orig_hours) = adjusted_load(orig_hours) - power;
                adjusted_load(new_hours)  = adjusted_load(new_hours)  + power;
            end
        end
    end

    % ---------- v6.3版本：削减负荷调整 - 新模型 P'cut,i = Pcut,i(1-βcut) ----------
    if ~isempty(cut_load) && N_CL > 0
        idx_cut_start = 7 + N_SL;
        beta_cut = X(idx_cut_start:idx_cut_start+N_CL-1);  % 4个二进制变量
        beta_cut = 1./(1+exp(-beta_cut));  % sigmoid转换
        
        for t = 1:24
            total_reduction = 0;
            for i = 1:N_CL
                % 新模型：P'cut,i = Pcut,i(1-βcut)
                % βcut=1时完全削减，βcut=0时不削减
                beta_cut_value = beta_cut(i);
                max_curtail_power = cut_load(i, t+1);  % MW
                actual_reduction = max_curtail_power * beta_cut_value;
                total_reduction = total_reduction + actual_reduction;
            end
            adjusted_load(t) = adjusted_load(t) - total_reduction;
        end
    end
end

%% 辅助函数2：分析可平移负荷的移入移出
function [shift_in, shift_out] = calculate_shift_load_analysis(X, total_load)
    global shift_load N_SL
    
    % 初始化24小时的移入移出数组
    shift_in = zeros(24, 1);
    shift_out = zeros(24, 1);
    
    if ~isempty(shift_load) && N_SL > 0
        shift_time_vars = X(7:6+N_SL);
        shift_time      = time_SL(shift_time_vars, shift_load);
        peak_set   = [10 11 12 18 19 20 21];
        valley_set = [1:7 23 24];
        
        for SL = 1:N_SL
            power = shift_load(SL,2);
            dur   = shift_load(SL,6);
            orig_start = shift_load(SL,4);
            orig_hours = mod(orig_start:orig_start+dur-1,24); orig_hours(orig_hours==0)=24;
            new_start  = shift_time(SL,1);
            new_hours  = mod(new_start:new_start+dur-1,24); new_hours(new_hours==0)=24;
            
            if any(ismember(orig_hours, peak_set))
                if ~all(ismember(new_hours, valley_set))
                    raw_raw = round(shift_time_vars(SL));
                    % --------------- 新策略 ---------------
                    plain_set  = setdiff(1:24, [peak_set valley_set]);

                    valley_candidates = [];
                    plain_candidates  = [];
                    for cand = 1:24
                        cand_hours = mod(cand:cand+dur-1,24); cand_hours(cand_hours==0)=24;
                        if all(ismember(cand_hours, valley_set))
                            valley_candidates(end+1) = cand; %#ok<AGROW>
                        elseif all(ismember(cand_hours, plain_set))
                            plain_candidates(end+1) = cand; %#ok<AGROW>
                        end
                    end

                    candidates = [valley_candidates valley_candidates plain_candidates];
                    if isempty(candidates)
                        candidates = valley_candidates;
                        if isempty(candidates)
                            if (23 + dur - 1) <= 24
                                candidates = 23;
                            else
                                candidates = 1;
                            end
                        end
                    end
                    idx_cand = mod(raw_raw-1, length(candidates)) + 1;
                    new_start = candidates(idx_cand);
                    new_hours = mod(new_start:new_start+dur-1,24); new_hours(new_hours==0)=24;
                end
                shift_out(orig_hours) = shift_out(orig_hours) + power;
                shift_in(new_hours)  = shift_in(new_hours)  + power;
            end
        end
    end
end

%% 辅助函数3：分析可削减负荷 - v6.3版本新模型
function cut_reduction = calculate_cut_load_analysis_v6_3(X)
    global cut_load N_SL N_CL
    
    % 初始化24小时的削减数组
    cut_reduction = zeros(24, 1);
    
    if ~isempty(cut_load) && N_CL > 0
        idx_cut_start = 7 + N_SL;
        beta_cut = X(idx_cut_start:idx_cut_start+N_CL-1);  % 4个二进制变量
        beta_cut = sigmoid(beta_cut);  % sigmoid转换
        
        for t = 1:24
            total_reduction = 0;
            for i = 1:N_CL
                % 新模型：P'cut,i = Pcut,i(1-βcut)
                % βcut=1时完全削减，βcut=0时不削减
                beta_cut_value = beta_cut(i);
                max_curtail_power = cut_load(i, t+1);  % MW
                actual_reduction = max_curtail_power * beta_cut_value;
                total_reduction = total_reduction + actual_reduction;
            end
            cut_reduction(t) = total_reduction;
        end
    end
end

%% 辅助函数3 (旧版本)：分析可削减负荷
function cut_reduction = calculate_cut_load_analysis(X)
    global cut_load N_SL N_CL
    
    % 初始化24小时的削减数组
    cut_reduction = zeros(24, 1);
    
    if ~isempty(cut_load) && N_CL > 0
        idx_cut_start = 7 + N_SL;
        cut_amount = reshape(X(idx_cut_start:idx_cut_start+24*N_CL-1), 24, N_CL);
        cut_switch = reshape(X(idx_cut_start+24*N_CL:idx_cut_start+24*N_CL+24*N_CL-1), 24, N_CL);
        cut_switch = sigmoid(cut_switch);
        
        for t = 1:24
            for i = 1:N_CL
                is_peak = (t>=10 && t<=12) || (t>=18 && t<=21);
                if is_peak && (cut_switch(t,i) > 0.5)
                    cut_reduction(t) = cut_reduction(t) + cut_amount(t,i);
                end
            end
        end
    end
end

% 辅助函数：sigmoid函数
function y = sigmoid(x)
    y = 1 ./ (1 + exp(-x));
end

 