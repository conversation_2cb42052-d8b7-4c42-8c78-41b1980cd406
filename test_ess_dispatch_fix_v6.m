%% 测试储能调度逻辑修复效果 - v6版本
% 验证修复后的储能调度策略是否能正确工作

clear; clc; close all;

% 全局变量声明
global mpc_base Pwt Ppv price shift_load cut_load Load_ac Load_dc
global N_SL N_CL Nwt Npv ac acq dc ESS_params

% 储能参数设置
ESS_params.Ce = 700;        % 储能单位容量成本 (元/kWh)
ESS_params.Cp = 400;        % 储能单位功率成本 (元/kW)
ESS_params.Cmaint = 180;    % 年运行维护成本 (元/kW·年)
ESS_params.ir = 0.015;      % 通胀膨胀率
ESS_params.dr = 0.09;       % 贴现率
ESS_params.Y = 10;          % 储能寿命 (年)
ESS_params.lambda = 0.08;   % 净报废成本系数
ESS_params.Cde = 60;        % 单位容量报废处置成本 (元/kWh)
ESS_params.E_max = 8000;    % 最大安装容量 8 MWh
ESS_params.P_max = 2500;    % 最大充放电功率 2.5 MW

% 加载数据
load_data_v6;

fprintf('=== 储能调度逻辑修复测试 ===\n\n');

%% 测试1：不配置储能的基准情况
fprintf('【测试1】不配置储能的基准情况\n');

% 构造无储能解
n_var = 2 + 2 + 2 + N_SL + N_CL;  % 总变量数
solution_no_ess = zeros(1, n_var);
solution_no_ess(1:2) = [10, 15];   % 储能位置（虽然容量为0）
solution_no_ess(3:4) = [0, 0];     % 储能容量为0
solution_no_ess(5:6) = [3, 3];     % 功率配比（无意义）

% 平移负荷时间设置
idx = 7;
if N_SL > 0
    solution_no_ess(idx:idx+N_SL-1) = shift_load(:,4)';  % 使用默认时间
    idx = idx + N_SL;
end

% 削减负荷开关设置
if N_CL > 0
    solution_no_ess(idx:idx+N_CL-1) = 0;  % 不削减负荷
end

[f1_no_ess, f2_no_ess, cost_details_no_ess] = ESS_objective_v6(solution_no_ess);

fprintf('系统运行成本: %.2f 万元\n', f2_no_ess/10000);
fprintf('弃风弃光成本: %.2f 万元\n', cost_details_no_ess.curtail_cost/10000);
fprintf('电网交互成本: %.2f 万元\n', cost_details_no_ess.grid_cost/10000);
fprintf('网损成本: %.2f 万元\n', cost_details_no_ess.loss_cost/10000);

if isfield(cost_details_no_ess, 'renewable_stats')
    fprintf('风电利用率: %.1f%% (弃风率: %.1f%%)\n', ...
        cost_details_no_ess.renewable_stats.wind_utilization_rate, ...
        cost_details_no_ess.renewable_stats.wind_curtailment_rate);
    fprintf('光伏利用率: %.1f%% (弃光率: %.1f%%)\n', ...
        cost_details_no_ess.renewable_stats.pv_utilization_rate, ...
        cost_details_no_ess.renewable_stats.pv_curtailment_rate);
end

%% 测试2：配置储能的改进情况
fprintf('\n【测试2】配置储能的改进情况\n');

% 构造有储能解
solution_with_ess = solution_no_ess;
solution_with_ess(1:2) = [30, 5];      % 储能安装节点
solution_with_ess(3:4) = [2500, 2000]; % 储能容量 2.5MWh 和 2MWh
solution_with_ess(5:6) = [3.5, 3.2];   % 功率配比 3.5h 和 3.2h

[f1_with_ess, f2_with_ess, cost_details_with_ess] = ESS_objective_v6(solution_with_ess);

fprintf('储能全寿命周期成本: %.2f 万元\n', f1_with_ess/10000);
fprintf('系统运行成本: %.2f 万元\n', f2_with_ess/10000);
fprintf('弃风弃光成本: %.2f 万元\n', cost_details_with_ess.curtail_cost/10000);
fprintf('电网交互成本: %.2f 万元\n', cost_details_with_ess.grid_cost/10000);
fprintf('网损成本: %.2f 万元\n', cost_details_with_ess.loss_cost/10000);

if isfield(cost_details_with_ess, 'renewable_stats')
    fprintf('风电利用率: %.1f%% (弃风率: %.1f%%)\n', ...
        cost_details_with_ess.renewable_stats.wind_utilization_rate, ...
        cost_details_with_ess.renewable_stats.wind_curtailment_rate);
    fprintf('光伏利用率: %.1f%% (弃光率: %.1f%%)\n', ...
        cost_details_with_ess.renewable_stats.pv_utilization_rate, ...
        cost_details_with_ess.renewable_stats.pv_curtailment_rate);
end

if isfield(cost_details_with_ess, 'ess_utilization')
    fprintf('储能1利用率: %.1f%% (充放电次数: %.1f次/天)\n', ...
        cost_details_with_ess.ess_utilization.ess1_rate*100, ...
        cost_details_with_ess.ess_utilization.ess1_cycles);
    fprintf('储能2利用率: %.1f%% (充放电次数: %.1f次/天)\n', ...
        cost_details_with_ess.ess_utilization.ess2_rate*100, ...
        cost_details_with_ess.ess_utilization.ess2_cycles);
end

%% 对比分析
fprintf('\n=== 储能效果对比分析 ===\n');

% 系统运行成本变化
cost_change = f2_with_ess - f2_no_ess;
cost_change_pct = cost_change / f2_no_ess * 100;
fprintf('系统运行成本变化: %.3f 万元 (%.1f%%)\n', cost_change/10000, cost_change_pct);

% 弃电成本变化
curtail_change = cost_details_with_ess.curtail_cost - cost_details_no_ess.curtail_cost;
fprintf('弃电成本变化: %.3f 万元\n', curtail_change/10000);

% 电网交互成本变化
grid_change = cost_details_with_ess.grid_cost - cost_details_no_ess.grid_cost;
fprintf('电网交互成本变化: %.3f 万元\n', grid_change/10000);

% 网损成本变化
loss_change = cost_details_with_ess.loss_cost - cost_details_no_ess.loss_cost;
fprintf('网损成本变化: %.3f 万元\n', loss_change/10000);

% 弃电率变化
if isfield(cost_details_no_ess, 'renewable_stats') && isfield(cost_details_with_ess, 'renewable_stats')
    curtail_rate_change = cost_details_with_ess.renewable_stats.wind_curtailment_rate - ...
                         cost_details_no_ess.renewable_stats.wind_curtailment_rate;
    fprintf('弃风率变化: %.1f个百分点\n', curtail_rate_change);
end

%% 储能调度策略分析
fprintf('\n=== 储能调度策略分析 ===\n');

if isfield(cost_details_with_ess, 'hourly_data')
    ess_power = cost_details_with_ess.hourly_data.ess_power;
    
    % 分析充放电时段
    charge_hours = find(sum(ess_power, 2) < -0.01);  % 充电时段
    discharge_hours = find(sum(ess_power, 2) > 0.01); % 放电时段
    
    fprintf('充电时段: ');
    for i = 1:length(charge_hours)
        fprintf('%02d:00 ', charge_hours(i)-1);
    end
    fprintf('\n');
    
    fprintf('放电时段: ');
    for i = 1:length(discharge_hours)
        fprintf('%02d:00 ', discharge_hours(i)-1);
    end
    fprintf('\n');
    
    % 检查负荷高峰期的储能行为
    peak_hours = [10, 11, 12, 18, 19, 20, 21];  % 负荷高峰时段
    peak_ess_power = sum(ess_power(peak_hours, :), 2);
    
    fprintf('\n负荷高峰期储能功率 (MW):\n');
    for i = 1:length(peak_hours)
        h = peak_hours(i);
        fprintf('  %02d:00: %.3f MW', h-1, peak_ess_power(i));
        if peak_ess_power(i) > 0
            fprintf(' (放电 ✓)');
        elseif peak_ess_power(i) < 0
            fprintf(' (充电 ✗)');
        else
            fprintf(' (待机)');
        end
        fprintf('\n');
    end
end

%% 判断修复效果
fprintf('\n=== 修复效果评估 ===\n');

success_count = 0;
total_tests = 4;

% 测试1：弃电成本是否减少
if curtail_change < 0
    fprintf('✓ 弃电成本减少: %.3f 万元\n', -curtail_change/10000);
    success_count = success_count + 1;
else
    fprintf('✗ 弃电成本增加: %.3f 万元\n', curtail_change/10000);
end

% 测试2：系统运行成本是否改善
if cost_change < 0
    fprintf('✓ 系统运行成本降低: %.3f 万元\n', -cost_change/10000);
    success_count = success_count + 1;
else
    fprintf('✗ 系统运行成本增加: %.3f 万元\n', cost_change/10000);
end

% 测试3：负荷高峰期是否主要放电
if exist('peak_ess_power', 'var')
    discharge_ratio = sum(peak_ess_power > 0.01) / length(peak_ess_power);
    if discharge_ratio >= 0.6
        fprintf('✓ 负荷高峰期主要放电: %.1f%% 时段放电\n', discharge_ratio*100);
        success_count = success_count + 1;
    else
        fprintf('✗ 负荷高峰期放电不足: %.1f%% 时段放电\n', discharge_ratio*100);
    end
end

% 测试4：储能利用率是否合理
if isfield(cost_details_with_ess, 'ess_utilization')
    avg_utilization = (cost_details_with_ess.ess_utilization.ess1_rate + ...
                      cost_details_with_ess.ess_utilization.ess2_rate) / 2;
    if avg_utilization >= 0.5
        fprintf('✓ 储能利用率合理: %.1f%%\n', avg_utilization*100);
        success_count = success_count + 1;
    else
        fprintf('✗ 储能利用率偏低: %.1f%%\n', avg_utilization*100);
    end
end

fprintf('\n总体评估: %d/%d 项测试通过\n', success_count, total_tests);
if success_count >= 3
    fprintf('🎉 储能调度逻辑修复成功！\n');
else
    fprintf('⚠️  储能调度逻辑仍需进一步优化\n');
end

%% 绘制对比图
try
    fprintf('\n正在生成对比图表...\n');
    plot_dispatch_results_v6(solution_with_ess, cost_details_with_ess, ESS_params);
    fprintf('图表生成完成！\n');
catch ME
    fprintf('绘图出错: %s\n', ME.message);
end

fprintf('\n=== 测试完成 ===\n');
