function [F1,F2,F3,penalty] = CostFunction(X,mpc,date,ac,acq,dc)

    global   Nv Nwt Npv Nm Nmcita Ness Nsvg
    % mpc1=mpc;
    mpc1=repmat(mpc,[1,1,24]);
    % a=mpc1.gencost(:,5);
    % b=mpc1.gencost(:,6);
    % c=mpc1.gencost(:,7);
    Sbase=mpc.baseMVA;
    nb=length(mpc.bus(:,1));
    ng=length(mpc.gen(:,1));
    % A = sum(mpc.gen(:, 2) ~= 0);
    A=3;%风机数量
    M=find(mpc.branch(:, 6) ~= 0);
    penalty=zeros(1,24);
    F1=zeros(1,24);
    F2=zeros(1,24);
    price=date(:,5);%分时电价
    
    %%24 hour 矩阵赋值
    
    parfor t=1:24
        %% Modification of the MATPOWER case struct with solution vector
        %Generator voltage风机电压
        mpc1(t).gen(2:A+1,7)=X(t,1:A);
        %the active power of wind,Solar发电量
        mpc1(t).gen(2:A+1,2)=X(t,Nv+1: Nv+Nwt);%发电量WT
        % mpc1(t).gen(2:A+1,3)=X(t,1: Nwt)*tan(acos(0.95));
        mpc1(t).gen(A+2:end,4)=X(t, Nv+Nwt+1: Nv+Nwt + Npv);%发电量PV
        %VSC  M
        % mpc1(t).branch(M,6)=X(t, Nv+Nwt + Npv+1: Nwt + Npv + Nm);
        % mpc1(t).branch(M,7)=X(t, Nv+Nwt + Npv + Nm +1:Nwt + Npv + Nm + Nmcita);
        %Stored energy
        mpc1(t).bus(:,3)=ac(t,:)';
        mpc1(t).bus(:,4)=acq(t,:)';
        mpc1(t).bus(:,5)=dc(t,:)';
        %储能
        % mpc1(t).bus(22,5)=mpc1(t).bus(22,5)+X(t,Nwt + Npv + Nm + Nmcita + 1);
        mpc1(t).bus(17,5)=mpc1(t).bus(17,5)+X(t,Nv+Nwt + Npv + Nm + Nmcita + 1);
        mpc1(t).bus(29,5)=mpc1(t).bus(29,5)+X(t,Nv+Nwt + Npv + Nm + Nmcita + Ness);
        % mpc1(t).bus(33,5)=mpc1(t).bus(33,5)+X(t, Nwt + Npv + Nm + Nmcita + Ness);
        %SVG补偿
        % mpc1(t).bus(7,4)=bus(7,4)-X(t, Nwt + Npv + Nm + Nmcita + Ness + Nsvg-2);
        % mpc1(t).bus(12,4)=bus(12,4)-X(t, Nwt + Npv + Nm + Nmcita + Ness + Nsvg-1);
        % mpc1(t).bus(33,4)=bus(33,4)-X(t, Nwt + Npv + Nm + Nmcita + Ness + Nsvg);
    end
    
    %%24 hour 潮流计算
    parfor t=1:24
        %% POWER FLOW
        [res]=ac_dcpowerflow(mpc1(t));
        Pgslack=[];
        Pgslack=res.gen(1,2);
        V=res.bus(:,7);
        Qgen=res.gen(:,3);
        S=res.S;
        loss(t)= res.loss;
    
        %% PENALTY FACTORS惩罚因子
        P1=500;
        P2=1000;
        P3=7000;
        P4=200;
    
    
        %% Penalty function for slack bus power violation
        penalty_slack=[];
        Pgslackmin=mpc.gen(1,9);
        Pgslackmax=mpc.gen(1,8);
    
        if Pgslack>Pgslackmax
            penalty_slack=100+P1*(((Pgslack-Pgslackmax)/Sbase)^2);
    
        elseif Pgslack<Pgslackmin
            penalty_slack=100+P1*(((Pgslackmin-Pgslack)/Sbase)^2);
    
        else
            penalty_slack=0;
        end
    
    
        %% Penalty function for bus voltage violation
    
        penalty_V=[];
        Vmax=mpc.bus(:,10);
        Vmin=mpc.bus(:,11);
        for i=1:nb
            if res.bus(i,2)==1
                if V(i)>Vmax(i)
                    penalty_V(i)=100+P2*(V(i)-Vmax(i))^2;
    
                elseif V(i)<Vmin(i)
                    penalty_V(i)=100+P2*(Vmin(i)-V(i))^2;
    
                else
                    penalty_V(i)=0;
                end
            end
        end
        penalty_V=sum(penalty_V);
    
    
        %% Penalty function for reactive power generation violation
        % penalty_Qgen=[];
        % Qmax=mpc1.gen(:,5);
        % Qmin=mpc1.gen(:,6);
        %    for i=1:ng
        %         if Qgen(i)>Qmax(i)
        %              penalty_Qgen(i)=P3*(((Qgen(i)-Qmax(i))/Sbase)^2);
        %
        %         elseif Qgen(i)<Qmin(i)
        %              penalty_Qgen(i)=P3*(((Qmin(i)-Qgen(i))/Sbase)^2);
        %
        %          else
        %              penalty_Qgen(i)=0;
        %         end
        %    end
        %     penalty_Qgen=sum(penalty_Qgen);
        penalty_Qgen=0;
    
        %% Penalty function for VSC M
        M_Smax=0.4;
        M_Smin=0;
        penalty_M=[];
        for i=1:size(M,1)
            if S(i)>M_Smax
                penalty_M(i)=100+P4*(S(i)-M_Smax)^2;
    
            elseif S(i)<M_Smin
                penalty_M(i)=100+P4*(S(i)-M_Smin)^2;
    
            else
                penalty_M(i)=0;
            end
        end
        penalty_M=sum(penalty_M);
    
    
        %% CUMULATIVE PENALTY FUNTION累积惩罚函数
    
        penalty(t)=(penalty_slack+penalty_V+penalty_Qgen+penalty_M)*10000;
        % penalty(t)=penalty_slack+penalty_V+penalty_M;
        %% TOTAL AUGMENTED OBJECTIVE FUNCTION全增广目标函数
    
        F1(t) = loss(t);
    
        cgrid=price(t)*Pgslack*1000;%电网交互成本
        cdg=0.065*sum(X(t,Nv+1:Nv + Nwt ))*1000+0.055*sum(X(t,Nv + Nwt+1:Nv + Nwt +Npv))*1000;%风光发电成本
        cess=0.05*sum(abs(X(t,Nv+Nwt + Npv +Nm+Nmcita+ 1:Nv+Nwt + Npv +Nm+Nmcita+ Ness)))*1000;%储能维护成本
        cwpp=0.2*(sum(date(t,1:2)) - sum(X(t,Nv+1:Nv + Nwt+Npv)))*1000;%弃风弃光惩罚
        F2(t)=cgrid+cdg+cess+cwpp;%运行成本，包含弃风弃光惩罚
    %     F2(t)=F2(t)+0.2*(sum(date(t,1:2)) - sum(X(t,Nv+1:Nv + Nwt+Npv)))*1000;%运行成本，包含弃风弃光惩罚
        F3(t)=sum(abs((V.*V)-1.05*1.05));
    
    end
    F1=sum(F1);%网损
    F2=sum(F2);%运行成本
    F3=sum(F3);%电压偏差
    penalty=sum(penalty);
    end    