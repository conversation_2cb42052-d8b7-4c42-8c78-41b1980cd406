%% v6版本修正后测试脚本
% 测试修正后的柔性负荷调度和储能调度逻辑

clear; clc; close all;

% 全局变量声明
global mpc_base Pwt Ppv price shift_load cut_load Load_ac Load_dc
global N_SL N_CL Nwt Npv ac acq dc ESS_params

% 储能参数设置
ESS_params.Ce = 700;        % 储能单位容量成本 (元/kWh)
ESS_params.Cp = 400;        % 储能单位功率成本 (元/kW)
ESS_params.Cmaint = 60;     % 年运行维护成本 (元/kW·年)
ESS_params.ir = 0.015;      % 通胀膨胀率
ESS_params.dr = 0.09;       % 贴现率
ESS_params.Y = 10;          % 储能寿命 (年)
ESS_params.lambda = 0.08;   % 净报废成本系数
ESS_params.Cde = 60;        % 单位容量报废处置成本 (元/kWh)
ESS_params.E_max = 8000;    % 最大安装容量 (kWh)
ESS_params.P_max = 2000;    % 最大充放电功率 (kW)

% 加载数据
load_data_v6;

fprintf('=== v6版本修正后测试 ===\n');

%% 创建测试解 - 基于您的运行结果
n_var = 6 + N_SL + 24*N_CL + 24*N_CL;
X_test = zeros(1, n_var);

% 储能参数（基于您的结果）
X_test(1) = 2;         % ESS1节点
X_test(2) = 32;        % ESS2节点
X_test(3) = 1018;      % ESS1容量 (kWh)
X_test(4) = 1061;      % ESS2容量 (kWh)
X_test(5) = 4.63;      % ESS1小时比
X_test(6) = 4.56;      % ESS2小时比

% 平移负荷时间 - 设置为从高峰期转到低谷期
idx = 7;
for i = 1:N_SL
    orig_start = shift_load(i,4);
    % 如果原始时间在高峰期，转移到低谷期
    if (orig_start >= 10 && orig_start <= 12) || (orig_start >= 18 && orig_start <= 21)
        X_test(idx) = 3;  % 转移到凌晨3点（低谷期）
    elseif orig_start >= 1 && orig_start <= 7
        X_test(idx) = 20; % 如果原本在低谷期，转移到晚高峰前
    else
        X_test(idx) = orig_start; % 保持原时间
    end
    idx = idx + 1;
end

% 削减负荷量 - 主要在高峰时段削减
for t = 1:24
    is_peak_hour = (t >= 10 && t <= 12) || (t >= 18 && t <= 21);
    for i = 1:N_CL
        if is_peak_hour
            X_test(idx) = cut_load(i,t+1) * 0.8;  % 高峰时段削减80%
        else
            X_test(idx) = cut_load(i,t+1) * 0.1;  % 非高峰时段削减10%
        end
        idx = idx + 1;
    end
end

% 削减负荷开关 - 主要在高峰时段开启
for t = 1:24
    is_peak_hour = (t >= 10 && t <= 12) || (t >= 18 && t <= 21);
    for i = 1:N_CL
        if is_peak_hour
            X_test(idx) = 5;   % 高概率开启（sigmoid(5) ≈ 0.99）
        else
            X_test(idx) = -3;  % 低概率开启（sigmoid(-3) ≈ 0.05）
        end
        idx = idx + 1;
    end
end

fprintf('决策变量设置完成，总变量数: %d\n', length(X_test));

%% 测试目标函数
fprintf('\n=== 测试目标函数 ===\n');
[f1, f2, cost_details] = ESS_objective_v6(X_test);

fprintf('储能全寿命周期成本: %.2f 万元\n', f1/10000);
fprintf('系统运行成本: %.2f 万元\n', f2/10000);

%% 分析成本构成
fprintf('\n=== 成本构成分析 ===\n');
fprintf('储能投资成本: %.2f 万元\n', cost_details.C_i/10000);
fprintf('储能运维成本: %.2f 万元\n', cost_details.C_m/10000);
fprintf('储能报废成本: %.2f 万元\n', cost_details.C_d/10000);
fprintf('电网交互成本: %.2f 万元\n', cost_details.grid_cost/10000);
fprintf('弃风弃光成本: %.2f 万元\n', cost_details.curtail_cost/10000);
fprintf('网损成本: %.2f 万元\n', cost_details.loss_cost/10000);
fprintf('平移负荷补偿成本: %.2f 万元\n', cost_details.C_SL/10000);
fprintf('削减负荷补偿成本: %.2f 万元\n', cost_details.C_CL/10000);

%% 分析风光利用率
fprintf('\n=== 风光利用率分析 ===\n');
fprintf('风电利用率: %.1f%% (弃风率: %.1f%%)\n', ...
    cost_details.renewable_stats.wind_utilization_rate, ...
    cost_details.renewable_stats.wind_curtailment_rate);
fprintf('光伏利用率: %.1f%% (弃光率: %.1f%%)\n', ...
    cost_details.renewable_stats.pv_utilization_rate, ...
    cost_details.renewable_stats.pv_curtailment_rate);

%% 分析储能利用率
fprintf('\n=== 储能利用率分析 ===\n');
fprintf('储能1利用率: %.1f%% (充放电次数: %.1f次/天)\n', ...
    cost_details.ess_utilization.ess1_rate*100, ...
    cost_details.ess_utilization.ess1_cycles);
fprintf('储能2利用率: %.1f%% (充放电次数: %.1f次/天)\n', ...
    cost_details.ess_utilization.ess2_rate*100, ...
    cost_details.ess_utilization.ess2_cycles);

%% 分析小时级数据
fprintf('\n=== 小时级数据分析 ===\n');
hourly_data = cost_details.hourly_data;

% 计算平移负荷统计
total_shift_in = 0;
total_shift_out = 0;
for t = 1:24
    % 这里需要重新计算平移负荷的影响
    % 简化分析，显示关键时段的数据
    if t == 11 || t == 19  % 高峰时段
        fprintf('时段%02d: 弃电%.3f MW, 购电%.3f MW, 储能1:%.3f MW, 储能2:%.3f MW\n', ...
            t, hourly_data.P_curtail(t), hourly_data.P_import(t), ...
            hourly_data.ess_power(t,1), hourly_data.ess_power(t,2));
    end
end

%% 绘制结果
fprintf('\n=== 绘制调度结果 ===\n');
try
    % 使用新的绘图函数
    plot_dispatch_results_v6(X_test, ac, dc, hourly_data.wind_actual, hourly_data.pv_actual, ...
        hourly_data.ess_power, hourly_data.P_curtail, hourly_data.P_import, ...
        X_test(1), X_test(2), X_test(3), X_test(4));
    fprintf('调度结果绘制完成\n');
catch ME
    fprintf('绘图出错: %s\n', ME.message);
end

fprintf('\n=== 测试完成 ===\n'); 