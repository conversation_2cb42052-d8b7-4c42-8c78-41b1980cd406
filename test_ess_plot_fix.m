% 测试修改后的储能功率绘图
clear; clc; close all;

% 加载数据
load_data_v6;

% 创建测试数据
X_test = [11, 10, 1315.17, 1019.31, 2.85, 5.91];  % 从您的运行结果中提取

% 模拟 cost_details 结构
cost_details = struct();
cost_details.hourly_data = struct();

% 创建模拟的小时级数据，包含明显的充放电模式
hours = 1:24;

% 模拟储能功率 - 创建明显的充放电模式
ess1_power = zeros(24, 1);
ess2_power = zeros(24, 1);

% ESS1: 白天充电(负值)，晚上放电(正值)
for t = 1:24
    if t >= 10 && t <= 16  % 白天充电
        ess1_power(t) = -0.3 - 0.2 * rand();  % -0.3到-0.5 MW
    elseif t >= 18 && t <= 22  % 晚上放电
        ess1_power(t) = 0.2 + 0.3 * rand();   % 0.2到0.5 MW
    else
        ess1_power(t) = 0.1 * (rand() - 0.5); % 小幅波动
    end
end

% ESS2: 不同的充放电模式
for t = 1:24
    if t >= 11 && t <= 15  % 白天充电
        ess2_power(t) = -0.2 - 0.15 * rand(); % -0.2到-0.35 MW
    elseif t >= 19 && t <= 23  % 晚上放电
        ess2_power(t) = 0.15 + 0.25 * rand(); % 0.15到0.4 MW
    else
        ess2_power(t) = 0.05 * (rand() - 0.5); % 小幅波动
    end
end

cost_details.hourly_data.ess_power = [ess1_power, ess2_power];

% 其他模拟数据
cost_details.hourly_data.wind_actual = rand(24, 3) * 2;
cost_details.hourly_data.pv_actual = rand(24, 3) * 1.5;
cost_details.hourly_data.P_curtail = rand(24, 1) * 0.5;
cost_details.hourly_data.P_import = rand(24, 1) * 2;

% 创建模拟的 ESS_params
ESS_params = struct();

fprintf('=== 测试修改后的储能功率绘图 ===\n');
fprintf('预期效果:\n');
fprintf('1. 图1中ESS1和ESS2的充电柱形应该分开显示\n');
fprintf('2. ESS1充电和放电使用相同的蓝色\n');
fprintf('3. ESS2充电和放电使用相同的绿色\n');
fprintf('4. 图例只显示ESS1和ESS2，不区分充放电\n');
fprintf('5. 正值表示放电(坐标轴以上)，负值表示充电(坐标轴以下)\n\n');

try
    % 测试修改后的绘图
    plot_dispatch_results_v6(X_test, cost_details, ESS_params);
    fprintf('绘图成功！请检查图形效果。\n');
catch ME
    fprintf('绘图失败: %s\n', ME.message);
    disp(ME.stack);
end

% 打印储能功率数据摘要
fprintf('\n=== 储能功率数据摘要 ===\n');
fprintf('ESS1功率范围: %.3f 到 %.3f MW\n', min(ess1_power), max(ess1_power));
fprintf('ESS2功率范围: %.3f 到 %.3f MW\n', min(ess2_power), max(ess2_power));
fprintf('ESS1充电总量: %.3f MWh\n', sum(max(-ess1_power, 0)));
fprintf('ESS1放电总量: %.3f MWh\n', sum(max(ess1_power, 0)));
fprintf('ESS2充电总量: %.3f MWh\n', sum(max(-ess2_power, 0)));
fprintf('ESS2放电总量: %.3f MWh\n', sum(max(ess2_power, 0))); 