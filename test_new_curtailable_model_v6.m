%% 测试新的可削减负荷模型 - v6.3版本
% 验证简化的二进制变量模型：P'cut,i = Pcut,i(1-βcut)

clear; clc; close all;

% 声明全局变量
global mpc_base Pwt Ppv price shift_load cut_load Load_ac Load_dc
global N_SL N_CL Nwt Npv ac acq dc ESS_params

% 加载数据
load_data_v6;

% 初始化ESS_params
ESS_params.Ce = 600;        % 储能单位容量成本 (元/kWh)
ESS_params.Cp = 350;        % 储能单位功率成本 (元/kW)
ESS_params.Cmaint = 50;     % 年运行维护成本 (元/kW·年)
ESS_params.ir = 0.015;      % 通胀膨胀率
ESS_params.dr = 0.09;       % 贴现率
ESS_params.Y = 10;          % 储能寿命 (年)
ESS_params.lambda = 0.08;   % 净报废成本系数
ESS_params.Cde = 60;        % 单位容量报废处置成本 (元/kWh)
ESS_params.E_max = 12000;   % 最大安装容量 12 MWh
ESS_params.P_max = 4000;    % 最大充放电功率 4 MW

fprintf('=== 新可削减负荷模型测试 (v6.3版本) ===\n');
fprintf('模型公式：P''cut,i = Pcut,i(1-βcut)\n');
fprintf('决策变量：每个节点一个βcut ∈ {0,1}\n\n');

% 显示可削减负荷数据
fprintf('可削减负荷节点信息：\n');
for i = 1:N_CL
    fprintf('节点%d：24小时可削减上限 = %.3f - %.3f MW\n', ...
        cut_load(i,1), min(cut_load(i,2:end)), max(cut_load(i,2:end)));
end

% 测试不同βcut值的效果
beta_values = [0.0, 0.3, 0.6, 1.0];  % 测试值
fprintf('\n=== 不同βcut值的削减效果测试 ===\n');

for beta_idx = 1:length(beta_values)
    beta_test = beta_values(beta_idx);
    fprintf('\nβcut = %.1f 时的削减效果：\n', beta_test);
    
    total_reduction_24h = 0;
    for t = 1:24
        hourly_reduction = 0;
        for i = 1:N_CL
            max_curtail_power = cut_load(i, t+1);  % MW
            actual_reduction = max_curtail_power * beta_test;
            hourly_reduction = hourly_reduction + actual_reduction;
        end
        total_reduction_24h = total_reduction_24h + hourly_reduction;
        
        if t == 1 || t == 12 || t == 20  % 显示几个典型时段
            fprintf('  时段%02d: 总削减量 = %.3f MW\n', t, hourly_reduction);
        end
    end
    
    fprintf('  24小时总削减量: %.2f MWh\n', total_reduction_24h);
    
    % 计算补偿成本
    base_compensation = 0.8;  % 元/kWh
    compensation_cost = total_reduction_24h * 1000 * base_compensation;  % 元
    fprintf('  补偿成本: %.0f 元 (%.2f 万元)\n', compensation_cost, compensation_cost/10000);
end

% 测试决策变量数量变化
fprintf('\n=== 决策变量数量对比 ===\n');
fprintf('原模型 (v6.2)：\n');
fprintf('  削减负荷量变量: 24×%d = %d\n', N_CL, 24*N_CL);
fprintf('  削减负荷开关变量: 24×%d = %d\n', N_CL, 24*N_CL);
fprintf('  削减负荷相关变量总数: %d\n', 24*N_CL*2);

fprintf('\n新模型 (v6.3)：\n');
fprintf('  削减负荷二进制变量: %d (每个节点一个βcut)\n', N_CL);
fprintf('  变量减少数量: %d\n', 24*N_CL*2 - N_CL);
fprintf('  压缩比例: %.1f%%\n', (1 - N_CL/(24*N_CL*2)) * 100);

% 总决策变量数量
n_siting_vars = 2;
n_capacity_vars = 2;
n_power_ratio_vars = 2;
n_shift_time_vars = N_SL;

old_total = n_siting_vars + n_capacity_vars + n_power_ratio_vars + n_shift_time_vars + 24*N_CL*2;
new_total = n_siting_vars + n_capacity_vars + n_power_ratio_vars + n_shift_time_vars + N_CL;

fprintf('\n总决策变量数量：\n');
fprintf('  原模型总数: %d\n', old_total);
fprintf('  新模型总数: %d\n', new_total);
fprintf('  总体压缩比例: %.1f%%\n', (1 - new_total/old_total) * 100);

% 测试实际优化效果
fprintf('\n=== 测试实际优化调用 ===\n');
try
    % 构造测试决策变量
    X_test = zeros(1, new_total);
    X_test(1:2) = [10, 20];  % 储能节点
    X_test(3:4) = [3000, 4000];  % 储能容量
    X_test(5:6) = [3.0, 3.5];  % 功率配比
    X_test(7:6+N_SL) = shift_load(:,4)';  % 平移负荷时间（不变）
    X_test(7+N_SL:end) = [0.2, 0.5, 0.8, 0.3];  % 4个节点的βcut值
    
    fprintf('测试决策变量构造成功，长度: %d\n', length(X_test));
    
    % 调用目标函数
    [f1, f2, cost_details] = ESS_objective_v6(X_test);
    
    fprintf('目标函数计算成功：\n');
    fprintf('  f1 (储能全寿命周期成本): %.2f 万元\n', f1/10000);
    fprintf('  f2 (系统运行成本): %.2f 万元\n', f2/10000);
    fprintf('  削减负荷补偿成本: %.2f 万元\n', cost_details.C_CL/10000);
    
catch ME
    fprintf('目标函数调用出错: %s\n', ME.message);
    fprintf('错误位置: %s (行 %d)\n', ME.stack(1).name, ME.stack(1).line);
end

fprintf('\n=== 新模型优势总结 ===\n');
fprintf('1. 决策变量大幅减少：从%d维降至%d维\n', old_total, new_total);
fprintf('2. 模型更符合实际：每个节点统一削减策略\n');
fprintf('3. 优化效率提升：搜索空间大幅缩小\n');
fprintf('4. 补偿成本计算简化：基于βcut直接计算\n');
fprintf('5. 物理意义明确：βcut=1完全削减，βcut=0不削减\n');

fprintf('\n测试完成！\n'); 