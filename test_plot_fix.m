%% v6绘图修复测试脚本
clear; clc; close all;

% 全局变量声明
global mpc_base Pwt Ppv price shift_load cut_load Load_ac Load_dc
global N_SL N_CL Nwt Npv ac acq dc ESS_params

% 储能参数设置
ESS_params.Ce = 400;        
ESS_params.Cp = 250;        
ESS_params.Cmaint = 60;     
ESS_params.ir = 0.015;      
ESS_params.dr = 0.09;       
ESS_params.Y = 10;          
ESS_params.lambda = 0.08;   
ESS_params.E_max = 6000;    
ESS_params.P_max = 1500;    

% 加载数据
load_data_v6;

fprintf('=== v6绘图修复测试 ===\n');

%% 基于实际运行结果创建测试解
% 储能参数 (来自实际运行结果)
ESS1_node = 4;
ESS2_node = 12;
ESS1_capacity = 2392.64; % kWh
ESS2_capacity = 2071.81; % kWh
ESS1_power = 702.63; % kW
ESS2_power = 539.27; % kW

% 构造决策变量
n_var = 6 + N_SL + N_CL;
X_test = zeros(1, n_var);

% 储能参数
X_test(1) = ESS1_node;
X_test(2) = ESS2_node;
X_test(3) = ESS1_capacity;
X_test(4) = ESS2_capacity;
X_test(5) = ESS1_capacity / ESS1_power; % 小时比
X_test(6) = ESS2_capacity / ESS2_power; % 小时比

% 平移负荷时间
X_test(7:6+N_SL) = shift_load(:,4)';

% 削减负荷开关 (v6.3新模型)
X_test(7+N_SL:6+N_SL+N_CL) = [0.59, 0.505, 0.591, 0.58]; % βcut值

fprintf('测试解构造完成，变量数: %d\n', length(X_test));

%% 计算目标函数获取cost_details
try
    fprintf('计算目标函数...\n');
    [f1, f2, cost_details] = ESS_objective_v6(X_test);
    
    fprintf('目标函数计算成功！\n');
    fprintf('储能全寿命周期成本: %.2f 万元\n', f1/10000);
    fprintf('系统运行成本: %.2f 万元\n', f2/10000);
    
catch ME
    fprintf('目标函数计算失败: %s\n', ME.message);
    % 创建模拟的cost_details
    cost_details = struct();
    cost_details.hourly_data.wind_actual = rand(24, 3) * 2;
    cost_details.hourly_data.pv_actual = rand(24, 3) * 1.5;
    cost_details.hourly_data.ess_power = (rand(24, 2) - 0.5) * 1;
    cost_details.hourly_data.P_curtail = rand(24, 1) * 0.5;
    cost_details.hourly_data.P_import = rand(24, 1) * 2;
end

%% 测试绘图功能
try
    fprintf('开始测试绘图功能...\n');
    plot_dispatch_results_v6(X_test, cost_details, ESS_params);
    fprintf('绘图测试成功！\n');
    
    fprintf('\n生成的图表:\n');
    fprintf('图1: v6储能风光协调调度结果\n');
    fprintf('图2: 储能充放电功率\n');
    fprintf('图3: 可平移负荷移入移出结果\n');
    fprintf('图4: 可削减负荷削减结果\n');
    fprintf('图5-7: VSC1-3传输功率曲线\n');
    fprintf('图8: 负荷曲线对比\n');
    
catch ME
    fprintf('绘图测试失败！\n');
    fprintf('错误信息: %s\n', ME.message);
    if ~isempty(ME.stack)
        fprintf('错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
    end
end

fprintf('\n=== v6绘图修复测试完成 ===\n'); 