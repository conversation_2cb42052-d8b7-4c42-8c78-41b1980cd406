%% v6修复版本测试脚本
% 测试修复后的储能调度逻辑和VSC功率计算

clear; clc; close all;

% 全局变量声明
global mpc_base Pwt Ppv price shift_load cut_load Load_ac Load_dc
global N_SL N_CL Nwt Npv ac acq dc ESS_params

% 储能参数设置
ESS_params.Ce = 400;        
ESS_params.Cp = 250;        
ESS_params.Cmaint = 60;     
ESS_params.ir = 0.015;      
ESS_params.dr = 0.09;       
ESS_params.Y = 10;          
ESS_params.lambda = 0.08;   
ESS_params.E_max = 6000;    
ESS_params.P_max = 1500;    

% 加载数据
load_data_v6;

fprintf('=== v6修复版本测试 ===\n');

%% 创建测试解 - 基于实际运行结果但优化储能调度
n_var = 6 + 24 + 24 + 48 + N_SL + N_CL;
X_test = zeros(1, n_var);

% 储能参数 (基于实际结果)
X_test(1:2) = [30, 20];              % 储能安装节点
X_test(3:4) = [2316.70, 2650.56];    % 储能容量 (kWh)
X_test(5:6) = [3.34, 3.67];          % 功率容量配比

% 弃电量 (7-30) - 设置为0，通过储能消纳
X_test(7:30) = 0.01 * ones(1, 24);   % 最小弃电量

% 购电量 (31-54) - 基于负荷设置
load_profile = sum(Load_ac + Load_dc, 2);
X_test(31:54) = max(0, 0.2 * load_profile');  % 购电为负荷的20%

% 储能充放电系数 (55-102) - 新的调度策略
ess_coeff = zeros(24, 2);
% 可再生能源高峰期充电 (9-15时)
ess_coeff(9:15, :) = -0.8;   % 强制充电消纳可再生能源
% 负荷高峰期放电 (18-21时)
ess_coeff(18:21, :) = 0.9;   % 强制放电支撑负荷
% 低谷期小功率充电 (1-6时)
ess_coeff(1:6, :) = -0.3;    % 低价充电
X_test(55:102) = reshape(ess_coeff, 1, 48);

% 平移负荷时间 (103-114)
X_test(103:103+N_SL-1) = shift_load(:,4)';

% 削减负荷开关 (115-118) - v6.3新模型
X_test(103+N_SL:103+N_SL+N_CL-1) = [0.3, 0.4, 0.35, 0.45];  % 适度削减

fprintf('测试解构造完成，变量数: %d\n', length(X_test));

%% 测试目标函数计算
try
    fprintf('\n开始计算目标函数...\n');
    [f1, f2, cost_details] = ESS_objective_v6(X_test);
    
    fprintf('\n=== 修复后结果 ===\n');
    fprintf('储能全寿命周期成本: %.2f 万元\n', f1/10000);
    fprintf('系统运行成本: %.2f 万元\n', f2/10000);
    
    if isfield(cost_details, 'renewable_stats')
        fprintf('\n=== 风光利用率 ===\n');
        fprintf('风电利用率: %.1f%% (弃风率: %.1f%%)\n', ...
            cost_details.renewable_stats.wind_utilization_rate, ...
            cost_details.renewable_stats.wind_curtailment_rate);
        fprintf('光伏利用率: %.1f%% (弃光率: %.1f%%)\n', ...
            cost_details.renewable_stats.pv_utilization_rate, ...
            cost_details.renewable_stats.pv_curtailment_rate);
        fprintf('总购电量: %.2f MWh\n', cost_details.renewable_stats.total_purchase_mwh);
    end
    
    if isfield(cost_details, 'cost_breakdown')
        fprintf('\n=== 成本分解 ===\n');
        fprintf('电网交互成本: %.2f 万元\n', cost_details.cost_breakdown.grid_cost/10000);
        fprintf('弃风弃光成本: %.2f 万元\n', cost_details.cost_breakdown.curtail_cost/10000);
        fprintf('网损成本: %.2f 万元\n', cost_details.cost_breakdown.loss_cost/10000);
        fprintf('平移负荷补偿成本: %.2f 万元\n', cost_details.cost_breakdown.C_SL/10000);
        fprintf('削减负荷补偿成本: %.2f 万元\n', cost_details.cost_breakdown.C_CL/10000);
    end
    
    fprintf('\n=== 测试成功 ===\n');
    fprintf('v6修复版本目标函数计算正常\n');
    
catch ME
    fprintf('\n=== 测试失败 ===\n');
    fprintf('错误信息: %s\n', ME.message);
    fprintf('错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
end

%% 测试绘图功能
try
    fprintf('\n开始测试绘图功能...\n');
    plot_dispatch_results_v6(X_test, cost_details, ESS_params);
    fprintf('绘图测试成功！\n');
    
    fprintf('\n生成的图表:\n');
    fprintf('图1: v6储能风光协调调度结果\n');
    fprintf('图2: 储能充放电功率\n');
    fprintf('图3: 可平移负荷移入移出结果\n');
    fprintf('图4: 可削减负荷削减结果\n');
    fprintf('图5-7: VSC1-3传输功率曲线（修复版）\n');
    fprintf('图8: 负荷曲线对比\n');
    
catch ME
    fprintf('绘图测试失败！\n');
    fprintf('错误信息: %s\n', ME.message);
    fprintf('错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
end

%% 对比分析
fprintf('\n=== 修复效果对比 ===\n');
fprintf('预期改进:\n');
fprintf('1. 储能在可再生能源过剩时强制充电，减少弃电\n');
fprintf('2. 储能在负荷高峰时强制放电，减少购电成本\n');
fprintf('3. VSC功率曲线基于实际潮流计算，更准确\n');
fprintf('4. 简化调度逻辑，避免复杂策略的副作用\n');

fprintf('\n=== v6修复版本测试完成 ===\n'); 