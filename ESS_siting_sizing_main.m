%% 交直流柔性配电网储能设备选址定容主程序
% 基于MMO_CLRPSO算法的双目标优化
clear all; clc; close all;

%% 系统参数设置
global mpc_base Pwt Ppv price shift_load cut_load Load_ac Load_dc
global N_SL N_CL Nwt Npv ac acq dc
global ESS_params % 储能参数

% 加载基础数据
mpc_base = case33ACDC;
flexible_load;
power_load;

% 储能参数设置
ESS_params.Ce = 700;        % 储能单位容量成本 (元/kWh)
ESS_params.Cp = 400;        % 储能单位功率成本 (元/kW)
ESS_params.Cmaint = 180;    % 年运行维护成本 (元/kW·年)
ESS_params.ir = 0.015;      % 通胀膨胀率
ESS_params.dr = 0.09;       % 贴现率
ESS_params.Y = 15;          % 储能寿命 (年)
ESS_params.lambda = 0.08;   % 净报废成本系数
ESS_params.E_max = 2000;    % 最大安装容量 (kWh)
ESS_params.P_max = 400;     % 最大充放电功率 (kW)

% 风光数据处理
Pwt = [Pwt1' Pwt2' Pwt3'];
Ppv = [Ppv1' Ppv2' Ppv3'];

% 负荷数据处理
ratio = mpc_base.bus(:,3:5)./sum(mpc_base.bus(:,3:5));
ratioq = mpc_base.bus(:,4)./mpc_base.bus(:,3);
ratioq(isnan(ratioq)) = 0;

for i = 1:24
    ac(i,:) = Load_ac(i) * ratio(:,1);
    acq(i,:) = ac(i,:) .* ratioq';
    dc(i,:) = Load_dc(i) * ratio(:,3);
end

N_SL = size(shift_load,1);
N_CL = size(cut_load,1);
Nwt = 3; Npv = 3;

%% 优化变量定义
% 决策变量：[ESS1_node, ESS2_node, ESS1_capacity, ESS2_capacity, ESS1_power, ESS2_power, 
%           风光削减(24*6), 储能功率(24*2), 平移负荷时间(N_SL), 削减负荷量(24*N_CL), 削减负荷开关(24*N_CL)]

n_var = 6 + 24*6 + 24*2 + N_SL + 24*N_CL + 24*N_CL; % 总决策变量数
n_obj = 2; % 目标函数数

% 变量边界
VRmin = zeros(1, n_var);
VRmax = zeros(1, n_var);

% 储能选址边界 (节点2-33，不能选择节点1)
VRmin(1:2) = 2;
VRmax(1:2) = 33;

% 储能容量边界 (kWh)
VRmin(3:4) = 0;
VRmax(3:4) = ESS_params.E_max;

% 储能功率边界 (kW)
VRmin(5:6) = 0;
VRmax(5:6) = ESS_params.P_max;

% 风光削减量边界
idx = 7;
for t = 1:24
    VRmin(idx:idx+5) = [0 0 0 0 0 0];
    VRmax(idx:idx+5) = [Pwt(t,:) Ppv(t,:)];
    idx = idx + 6;
end

% 储能充放电功率边界 (MW)
for t = 1:24
    VRmin(idx:idx+1) = [-0.4 -0.4];
    VRmax(idx:idx+1) = [0.4 0.4];
    idx = idx + 2;
end

% 平移负荷时间边界
time_SL1 = shift_load(:,4);
time_SL2 = shift_load(:,5) - shift_load(:,6) + 1;
VRmin(idx:idx+N_SL-1) = time_SL1';
VRmax(idx:idx+N_SL-1) = time_SL2';
idx = idx + N_SL;

% 削减负荷量边界
for t = 1:24
    VRmin(idx:idx+N_CL-1) = zeros(1,N_CL);
    VRmax(idx:idx+N_CL-1) = cut_load(:,t+1)';
    idx = idx + N_CL;
end

% 削减负荷开关边界
for t = 1:24
    VRmin(idx:idx+N_CL-1) = -10 * ones(1,N_CL);
    VRmax(idx:idx+N_CL-1) = 10 * ones(1,N_CL);
    idx = idx + N_CL;
end

%% MMO_CLRPSO算法参数
popsize = 100;
Max_Gen = 50;

fprintf('开始储能选址定容优化...\n');
fprintf('决策变量数: %d\n', n_var);
fprintf('目标函数数: %d\n', n_obj);

%% 调用MMO_CLRPSO算法
[ps, pf] = MMO_CLRPSO_ESS('ESS_objective', VRmin, VRmax, n_obj, popsize, Max_Gen);

%% 结果分析
fprintf('\n优化完成！\n');
fprintf('获得Pareto解的数量: %d\n', size(ps,1));

% 选择一个代表性解进行详细分析
[~, idx] = min(sum(pf,2)); % 选择两个目标函数和最小的解
best_solution = ps(idx,:);

fprintf('\n最优解分析:\n');
fprintf('储能1安装节点: %d\n', round(best_solution(1)));
fprintf('储能2安装节点: %d\n', round(best_solution(2)));
fprintf('储能1额定容量: %.2f kWh\n', best_solution(3));
fprintf('储能2额定容量: %.2f kWh\n', best_solution(4));
fprintf('储能1额定功率: %.2f kW\n', best_solution(5));
fprintf('储能2额定功率: %.2f kW\n', best_solution(6));

% 计算最优解的目标函数值
[f1, f2] = ESS_objective(best_solution);
fprintf('储能全寿命周期成本: %.2f 万元\n', f1/10000);
fprintf('系统运行成本: %.2f 万元\n', f2/10000);

%% 绘制Pareto前沿
figure(1);
scatter(pf(:,1)/10000, pf(:,2)/10000, 50, 'filled');
xlabel('储能全寿命周期成本 (万元)');
ylabel('系统运行成本 (万元)');
title('储能选址定容Pareto前沿');
grid on;

%% 绘制调度结果
plot_dispatch_results(best_solution);

%% 保存结果
save('ESS_siting_sizing_results.mat', 'ps', 'pf', 'best_solution');
fprintf('\n结果已保存到 ESS_siting_sizing_results.mat\n'); 