%% 测试修正后的 Pareto 解集与前沿绘图函数
% 这个脚本用于验证 plot_pareto_results_v6.m 的正确性

clear; clc; close all;

% 模拟一些 Pareto 解数据用于测试
n_solutions = 20;  % 解的数量

%% 生成模拟的 Pareto 解集（决策变量空间）
% 决策变量：[ESS1_node, ESS2_node, ESS1_capacity, ESS2_capacity, ESS1_ratio, ESS2_ratio, ...]
ps = zeros(n_solutions, 6);  % 简化为6个主要决策变量

% 储能1选址: 节点 5-30
ps(:,1) = 5 + rand(n_solutions,1) * 25;

% 储能2选址: 节点 8-33  
ps(:,2) = 8 + rand(n_solutions,1) * 25;

% 储能1容量: 1000-5000 kWh
ps(:,3) = 1000 + rand(n_solutions,1) * 4000;

% 储能2容量: 1500-4500 kWh
ps(:,4) = 1500 + rand(n_solutions,1) * 3000;

% 功率容量配比: 2-5小时
ps(:,5) = 2 + rand(n_solutions,1) * 3;
ps(:,6) = 2 + rand(n_solutions,1) * 3;

%% 生成模拟的 Pareto 前沿（目标函数空间）
% 目标函数1: 储能全寿命周期成本 (元)
% 目标函数2: 系统运行成本 (元)

% 模拟真实的Pareto前沿特征：两个目标函数之间的权衡关系
t = linspace(0, 1, n_solutions);

% 储能成本：随容量增加而增加
f1_base = 50000 + (ps(:,3) + ps(:,4)) * 0.7;  % 基于总容量的成本

% 运行成本：储能越多，运行成本相对降低（但有递减效应）
total_capacity = ps(:,3) + ps(:,4);
f2_base = 80000 - total_capacity * 3 + 0.5 * total_capacity.^1.2;

% 添加一些随机扰动，模拟真实优化的复杂性
f1 = f1_base + randn(n_solutions,1) * 5000;
f2 = f2_base + randn(n_solutions,1) * 3000;

% 确保都是正值
f1 = max(f1, 30000);
f2 = max(f2, 40000);

pf = [f1, f2];

%% 归一化处理
f1_min = min(f1); f1_max = max(f1);
f2_min = min(f2); f2_max = max(f2);

pf_norm = zeros(size(pf));
pf_norm(:,1) = (f1 - f1_min) / (f1_max - f1_min);
pf_norm(:,2) = (f2 - f2_min) / (f2_max - f2_min);

%% 找到最优解（距离理想点最近）
distances = sqrt(pf_norm(:,1).^2 + pf_norm(:,2).^2);
[~, best_idx] = min(distances);
best_solution = ps(best_idx, :);

%% 输出测试信息
fprintf('=== Pareto 绘图测试 ===\n');
fprintf('生成 %d 个测试解\n', n_solutions);
fprintf('储能1选址范围: 节点 %.0f - %.0f\n', min(ps(:,1)), max(ps(:,1)));
fprintf('储能2选址范围: 节点 %.0f - %.0f\n', min(ps(:,2)), max(ps(:,2)));
fprintf('储能1容量范围: %.0f - %.0f kWh\n', min(ps(:,3)), max(ps(:,3)));
fprintf('储能2容量范围: %.0f - %.0f kWh\n', min(ps(:,4)), max(ps(:,4)));
fprintf('储能成本范围: %.2f - %.2f 万元\n', min(f1)/10000, max(f1)/10000);
fprintf('运行成本范围: %.2f - %.2f 万元\n', min(f2)/10000, max(f2)/10000);
fprintf('最优解索引: %d\n', best_idx);
fprintf('最优解储能选址: 节点 %.0f, %.0f\n', best_solution(1), best_solution(2));
fprintf('最优解储能容量: %.0f, %.0f kWh\n', best_solution(3), best_solution(4));

%% 调用修正后的绘图函数
plot_pareto_results_v6(ps, pf, pf_norm, best_solution, best_idx);

fprintf('\n图表已生成！请检查以下内容：\n');
fprintf('1. 左上图：储能选址分布（决策变量空间）- 应显示节点编号散点图\n');
fprintf('2. 右上图：储能容量分布（决策变量空间）- 应显示容量相关性\n');
fprintf('3. 左下图：原始目标函数值（目标函数空间）- 应显示成本权衡前沿\n');
fprintf('4. 右下图：归一化目标函数值（目标函数空间）- 应显示0-1范围的前沿\n');
fprintf('5. 红色五角星：最优解在各图中的位置标记\n'); 