%% 储能效果对比测试 - v6版本
% 测试配置储能vs不配置储能的系统性能

clear; clc; close all;

% 全局变量声明
global mpc_base Pwt Ppv price shift_load cut_load Load_ac Load_dc
global N_SL N_CL Nwt Npv ac acq 
global ESS_params

% 加载数据
load_data_v6;

% 储能参数设置
ESS_params.Ce = 700;        
ESS_params.Cp = 400;        
ESS_params.Cmaint = 60;     
ESS_params.ir = 0.015;      
ESS_params.dr = 0.09;       
ESS_params.Y = 10;          
ESS_params.lambda = 0.08;   
ESS_params.Cde = 60;        
ESS_params.E_max = 8000;    
ESS_params.P_max = 2000;    

fprintf('=== 储能效果对比测试 ===\n\n');

%% 测试1：不配置储能
fprintf('【测试1】不配置储能的系统性能\n');

% 构造无储能解（储能容量为0）
solution_no_ess = zeros(1, 98); % 假设总变量数为98
solution_no_ess(1:2) = [10, 15]; % 储能位置（虽然容量为0）
solution_no_ess(3:4) = [0, 0];   % 储能容量为0
solution_no_ess(5:6) = [3, 3];   % 功率配比（无意义，因为容量为0）

% 其他变量设置为合理值（平移负荷和削减负荷）
% 这里简化处理，设为默认值
idx = 7;
% 平移负荷时间（假设N_SL=5）
solution_no_ess(idx:idx+4) = [8, 10, 12, 14, 16]; % 平移到谷期
idx = idx + 5;
% 削减负荷量和开关（假设24*N_CL=24*3=72个变量）
solution_no_ess(idx:end) = 0; % 简化为0

[f1_no_ess, f2_no_ess, cost_details_no_ess] = ESS_objective_v6(solution_no_ess);

fprintf('系统运行成本: %.2f 万元\n', f2_no_ess/10000);
fprintf('弃风弃光成本: %.2f 万元\n', cost_details_no_ess.curtail_cost/10000);
if isfield(cost_details_no_ess, 'renewable_stats')
    fprintf('风电利用率: %.1f%% (弃风率: %.1f%%)\n', ...
        cost_details_no_ess.renewable_stats.wind_utilization_rate, ...
        cost_details_no_ess.renewable_stats.wind_curtailment_rate);
    fprintf('光伏利用率: %.1f%% (弃光率: %.1f%%)\n', ...
        cost_details_no_ess.renewable_stats.pv_utilization_rate, ...
        cost_details_no_ess.renewable_stats.pv_curtailment_rate);
end

%% 测试2：配置储能
fprintf('\n【测试2】配置储能的系统性能\n');

% 构造有储能解
solution_with_ess = solution_no_ess;
solution_with_ess(3:4) = [2000, 1500]; % 储能容量2MWh和1.5MWh
solution_with_ess(5:6) = [4, 3];       % 功率配比4h和3h

[f1_with_ess, f2_with_ess, cost_details_with_ess] = ESS_objective_v6(solution_with_ess);

fprintf('储能全寿命周期成本: %.2f 万元\n', f1_with_ess/10000);
fprintf('系统运行成本: %.2f 万元\n', f2_with_ess/10000);
fprintf('弃风弃光成本: %.2f 万元\n', cost_details_with_ess.curtail_cost/10000);
if isfield(cost_details_with_ess, 'renewable_stats')
    fprintf('风电利用率: %.1f%% (弃风率: %.1f%%)\n', ...
        cost_details_with_ess.renewable_stats.wind_utilization_rate, ...
        cost_details_with_ess.renewable_stats.wind_curtailment_rate);
    fprintf('光伏利用率: %.1f%% (弃光率: %.1f%%)\n', ...
        cost_details_with_ess.renewable_stats.pv_utilization_rate, ...
        cost_details_with_ess.renewable_stats.pv_curtailment_rate);
end

if isfield(cost_details_with_ess, 'ess_utilization')
    fprintf('储能1利用率: %.1f%% (充放电次数: %.1f次/天)\n', ...
        cost_details_with_ess.ess_utilization.ess1_rate*100, ...
        cost_details_with_ess.ess_utilization.ess1_cycles);
    fprintf('储能2利用率: %.1f%% (充放电次数: %.1f次/天)\n', ...
        cost_details_with_ess.ess_utilization.ess2_rate*100, ...
        cost_details_with_ess.ess_utilization.ess2_cycles);
end

%% 对比分析
fprintf('\n=== 储能效果对比分析 ===\n');
fprintf('系统运行成本变化: %.3f 万元 (%.1f%%)\n', ...
    (f2_with_ess - f2_no_ess)/10000, ...
    (f2_with_ess - f2_no_ess)/f2_no_ess*100);

curtail_reduction = cost_details_no_ess.curtail_cost - cost_details_with_ess.curtail_cost;
fprintf('弃电成本减少: %.3f 万元\n', curtail_reduction/10000);

if isfield(cost_details_no_ess, 'renewable_stats') && isfield(cost_details_with_ess, 'renewable_stats')
    wind_curtail_reduction = cost_details_no_ess.renewable_stats.wind_curtailment_rate - ...
                            cost_details_with_ess.renewable_stats.wind_curtailment_rate;
    pv_curtail_reduction = cost_details_no_ess.renewable_stats.pv_curtailment_rate - ...
                          cost_details_with_ess.renewable_stats.pv_curtailment_rate;
    fprintf('弃风率降低: %.1f%% (从%.1f%%降至%.1f%%)\n', ...
        wind_curtail_reduction, ...
        cost_details_no_ess.renewable_stats.wind_curtailment_rate, ...
        cost_details_with_ess.renewable_stats.wind_curtailment_rate);
    fprintf('弃光率降低: %.1f%% (从%.1f%%降至%.1f%%)\n', ...
        pv_curtail_reduction, ...
        cost_details_no_ess.renewable_stats.pv_curtailment_rate, ...
        cost_details_with_ess.renewable_stats.pv_curtailment_rate);
end

%% 判断储能是否有效
total_benefit = curtail_reduction; % 主要效益来自弃电减少
annual_ess_cost = f1_with_ess / ESS_params.Y; % 年化储能成本

fprintf('\n=== 储能经济性分析 ===\n');
fprintf('年化储能成本: %.2f 万元/年\n', annual_ess_cost/10000);
fprintf('年弃电成本减少: %.2f 万元/年 (按365天计算)\n', total_benefit*365/10000);
fprintf('储能投资回收期: %.1f 年\n', annual_ess_cost/total_benefit/365);

if curtail_reduction > 0
    fprintf('\n✓ 储能有效：减少了弃电成本\n');
else
    fprintf('\n✗ 储能无效：未能减少弃电成本\n');
end

if f2_with_ess < f2_no_ess
    fprintf('✓ 系统运行成本降低：储能发挥了调峰作用\n');
else
    fprintf('✗ 系统运行成本增加：储能调度策略需要优化\n');
end 