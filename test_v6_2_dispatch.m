%% 测试v6.2版本直接调度逻辑
% 验证删除充放电系数后的调度逻辑是否正确

clear; clc; close all;

% 加载数据
load_data_v6;

% 测试用储能参数
ESS1_capacity = 800;  % kWh
ESS2_capacity = 800;  % kWh
ESS1_power = 150;     % kW
ESS2_power = 150;     % kW

% 测试决策变量 (v6.2版本 - 删除充放电系数)
% 1-2: 储能节点, 3-4: 储能容量, 5-6: 功率容量配比, 7-...: 其他变量
test_X = [12, 5, ESS1_capacity, ESS2_capacity, 5.33, 5.33];

% 添加其他决策变量 (平移负荷时间、削减负荷量等)
% 这里简化处理，添加足够的变量
additional_vars = zeros(1, 200);  % 足够的变量数量
test_X = [test_X, additional_vars];

fprintf('=== 测试v6.2版本直接调度逻辑 ===\n');
fprintf('储能1: 节点%d, 容量%d kWh, 功率%d kW\n', round(test_X(1)), round(test_X(3)), round(test_X(3)/test_X(5)));
fprintf('储能2: 节点%d, 容量%d kWh, 功率%d kW\n', round(test_X(2)), round(test_X(4)), round(test_X(4)/test_X(6)));

try
    % 调用目标函数
    [f1, f2, cost_details] = ESS_objective_v6(test_X);
    
    fprintf('\n=== 优化结果 ===\n');
    fprintf('储能全寿命周期成本: %.2f 万元\n', f1/10000);
    fprintf('系统运行成本: %.2f 万元\n', f2/10000);
    
    if isfield(cost_details, 'renewable_stats')
        fprintf('\n=== 风光利用率 ===\n');
        fprintf('风电利用率: %.1f%% (弃风率: %.1f%%)\n', ...
            cost_details.renewable_stats.wind_utilization_rate, ...
            cost_details.renewable_stats.wind_curtailment_rate);
        fprintf('光伏利用率: %.1f%% (弃光率: %.1f%%)\n', ...
            cost_details.renewable_stats.pv_utilization_rate, ...
            cost_details.renewable_stats.pv_curtailment_rate);
        fprintf('总购电量: %.2f MWh\n', cost_details.renewable_stats.total_purchase_mwh);
    end
    
    if isfield(cost_details, 'ess_utilization')
        fprintf('\n=== 储能利用率 ===\n');
        fprintf('储能1利用率: %.1f%% (充放电次数: %.1f次/天)\n', ...
            cost_details.ess_utilization.ess1_rate*100, ...
            cost_details.ess_utilization.ess1_cycles);
        fprintf('储能2利用率: %.1f%% (充放电次数: %.1f次/天)\n', ...
            cost_details.ess_utilization.ess2_rate*100, ...
            cost_details.ess_utilization.ess2_cycles);
    end
    
    % 绘制充放电功率曲线
    if isfield(cost_details, 'hourly_data')
        figure('Name', 'v6.2储能充放电功率曲线');
        ess_power = cost_details.hourly_data.ess_power;
        
        bar_data = [ess_power(:,1), ess_power(:,2)];
        bar(1:24, bar_data);
        xlabel('时间 (h)');
        ylabel('功率 (MW)');
        title('v6.2储能充放电功率 (正值放电，负值充电)');
        legend('ESS1', 'ESS2');
        grid on;
        
        % 绘制弃电购电曲线
        figure('Name', 'v6.2弃电购电曲线');
        P_curtail = cost_details.hourly_data.P_curtail;
        P_import = cost_details.hourly_data.P_import;
        
        bar_data = [P_curtail, P_import];
        bar(1:24, bar_data);
        xlabel('时间 (h)');
        ylabel('功率 (MW)');
        title('v6.2弃电量与购电量 (调度逻辑重构)');
        legend('弃电量', '购电量');
        grid on;
    end
    
    fprintf('\n=== 测试成功 ===\n');
    fprintf('v6.2版本直接调度逻辑运行正常\n');
    fprintf('弃电优先充电策略已生效\n');
    
catch ME
    fprintf('\n=== 测试失败 ===\n');
    fprintf('错误信息: %s\n', ME.message);
    fprintf('错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
end 