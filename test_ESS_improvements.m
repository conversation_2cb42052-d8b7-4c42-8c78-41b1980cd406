%% 测试储能利用率改进效果
% 验证修改后的储能充放电策略和参数设置
clear all; clc; close all;

%% 加载数据和参数
global mpc_base Pwt Ppv price shift_load cut_load Load_ac Load_dc
global N_SL N_CL Nwt Npv ac acq dc ESS_params

% 加载基础数据
mpc_base = case33ACDC;
flexible_load;
power_load;

% 储能参数设置
ESS_params.Ce = 480;        % 储能单位容量成本 (元/kWh)
ESS_params.Cp = 240;        % 储能单位功率成本 (元/kW)
ESS_params.Cmaint = 80;     % 年运行维护成本 (元/kW·年)
ESS_params.ir = 0.015;      % 通胀膨胀率
ESS_params.dr = 0.09;       % 贴现率
ESS_params.Y = 15;          % 储能寿命 (年)
ESS_params.lambda = 0.08;   % 净报废成本系数
ESS_params.E_max = 5000;    % 最大安装容量 5 MWh (kWh)
ESS_params.P_max = 1200;    % 最大充放电功率 1.2 MW (kW)

% 风光数据处理
Pwt = [Pwt1' Pwt2' Pwt3'];
Ppv = [Ppv1' Ppv2' Ppv3'];

% 负荷数据处理
ratio = mpc_base.bus(:,3:5)./sum(mpc_base.bus(:,3:5));
ratioq = mpc_base.bus(:,4)./mpc_base.bus(:,3);
ratioq(isnan(ratioq)) = 0;

for i = 1:24
    ac(i,:) = Load_ac(i) * ratio(:,1);
    acq(i,:) = ac(i,:) .* ratioq';
    dc(i,:) = Load_dc(i) * ratio(:,3);
end

N_SL = size(shift_load,1);
N_CL = size(cut_load,1);
Nwt = 3; Npv = 3;

%% 创建测试决策变量
% 基于您的最优解结果创建测试变量
X_test = zeros(1, 402);

% 储能选址定容 (基于您的结果进行调整)
X_test(1) = 29;        % ESS1节点
X_test(2) = 29;        % ESS2节点  
X_test(3) = 1200;      % ESS1容量 (kWh) - 增大
X_test(4) = 1000;      % ESS2容量 (kWh) - 增大
X_test(5) = 3;         % ESS1小时比 - 提高功率
X_test(6) = 3;         % ESS2小时比 - 提高功率

% 风光实际上网功率 - 设置为较高的利用率
wind_base_new = [2.16, 1.8, 2.7];
pv_base_new = [2.0, 2.5, 2.5];

idx = 7;
for t = 1:24
    % 风电设置为85%利用率
    X_test(idx:idx+2) = [Pwt(t,1)*wind_base_new(1)*0.85, ...
                         Pwt(t,2)*wind_base_new(2)*0.85, ...
                         Pwt(t,3)*wind_base_new(3)*0.85];
    % 光伏设置为85%利用率
    X_test(idx+3:idx+5) = [Ppv(t,1)*pv_base_new(1)*0.85, ...
                           Ppv(t,2)*pv_base_new(2)*0.85, ...
                           Ppv(t,3)*pv_base_new(3)*0.85];
    idx = idx + 6;
end

% 储能充放电系数 - 基于价格和负荷需求设置
for t = 1:24
    if price(t) <= 0.45  % 谷价时段充电
        X_test(idx:idx+1) = [-0.8, -0.8];  % 充电
    elseif price(t) >= 0.55  % 峰价时段放电
        X_test(idx:idx+1) = [0.8, 0.8];    % 放电
    else  % 平价时段根据负荷调节
        load_factor = (Load_ac(t) + Load_dc(t)) / max(Load_ac + Load_dc);
        if load_factor > 0.7
            X_test(idx:idx+1) = [0.4, 0.4];  % 轻度放电
        elseif load_factor < 0.3
            X_test(idx:idx+1) = [-0.4, -0.4]; % 轻度充电
        else
            X_test(idx:idx+1) = [0, 0];       % 待机
        end
    end
    idx = idx + 2;
end

% 平移负荷时间 - 设置为默认值
X_test(idx:idx+N_SL-1) = shift_load(:,4)';
idx = idx + N_SL;

% 削减负荷量 - 设置为较小值
for t = 1:24
    X_test(idx:idx+N_CL-1) = cut_load(:,t+1)' * 0.1;
    idx = idx + N_CL;
end

% 削减负荷开关 - 设置为较少使用
for t = 1:24
    X_test(idx:idx+N_CL-1) = -5 * ones(1,N_CL);  % 倾向于不使用
    idx = idx + N_CL;
end

fprintf('=== 测试储能利用率改进效果 ===\n');
fprintf('测试储能配置:\n');
fprintf('ESS1: 节点%d, 容量%.0fkWh, 功率%.0fkW (配比%.1fh)\n', ...
        X_test(1), X_test(3), X_test(3)/X_test(5), X_test(5));
fprintf('ESS2: 节点%d, 容量%.0fkWh, 功率%.0fkW (配比%.1fh)\n', ...
        X_test(2), X_test(4), X_test(4)/X_test(6), X_test(6));

%% 调用目标函数进行测试
[f1, f2, cost_details] = ESS_objective_v5(X_test);

%% 分析结果
fprintf('\n=== 测试结果分析 ===\n');
fprintf('储能全寿命周期成本: %.2f 万元\n', f1/10000);
fprintf('系统运行成本: %.2f 万元\n', f2/10000);

if isfield(cost_details, 'cost_breakdown')
    fprintf('\n目标函数1详细分解:\n');
    fprintf('  储能投资成本: %.2f 万元\n', cost_details.cost_breakdown.investment_cost/10000);
    fprintf('  储能运维成本: %.2f 万元\n', cost_details.cost_breakdown.maintenance_cost/10000);
    fprintf('  储能报废成本: %.2f 万元\n', cost_details.cost_breakdown.disposal_cost/10000);
    
    fprintf('\n目标函数2详细分解:\n');
    fprintf('  电网交互成本: %.2f 万元\n', cost_details.cost_breakdown.grid_cost/10000);
    fprintf('  弃风弃光成本: %.2f 万元\n', cost_details.cost_breakdown.curtailment_cost/10000);
    fprintf('  网损成本: %.2f 万元\n', cost_details.cost_breakdown.loss_cost/10000);
    fprintf('  柔性负荷成本: %.2f 万元\n', (cost_details.cost_breakdown.shift_cost + cost_details.cost_breakdown.cut_cost)/10000);
    fprintf('  约束惩罚成本: %.2f 万元\n', (cost_details.cost_breakdown.constraint_penalty + cost_details.cost_breakdown.ess_penalty)/10000);
    fprintf('  最低消纳率惩罚: %.2f 万元\n', cost_details.cost_breakdown.min_utilization_penalty/10000);
end

if isfield(cost_details, 'ess_utilization')
    fprintf('\n储能利用率分析:\n');
    fprintf('ESS1利用率: %.1f%% (充放电次数: %.1f次/天)\n', ...
            cost_details.ess_utilization.ess1_rate*100, cost_details.ess_utilization.ess1_cycles);
    fprintf('ESS2利用率: %.1f%% (充放电次数: %.1f次/天)\n', ...
            cost_details.ess_utilization.ess2_rate*100, cost_details.ess_utilization.ess2_cycles);
end

if isfield(cost_details, 'renewable_stats')
    fprintf('\n风光利用率分析:\n');
    fprintf('风电利用率: %.1f%% (弃风率: %.1f%%)\n', ...
            cost_details.renewable_stats.wind_utilization_rate, cost_details.renewable_stats.wind_curtailment_rate);
    fprintf('光伏利用率: %.1f%% (弃光率: %.1f%%)\n', ...
            cost_details.renewable_stats.pv_utilization_rate, cost_details.renewable_stats.pv_curtailment_rate);
    fprintf('总购电量: %.2f MWh\n', cost_details.renewable_stats.total_purchase_mwh);
end

%% 可视化结果
if isfield(cost_details, 'hourly_data')
    fprintf('\n正在生成可视化图表...\n');
    plot_dispatch_results_v5(X_test, cost_details, ESS_params);
    
    % 额外绘制价格和储能策略对比图
    figure('Position', [200, 200, 1200, 400]);
    
    subplot(1,2,1);
    hours = 1:24;
    yyaxis left;
    plot(hours, price, 'b-o', 'LineWidth', 2);
    ylabel('电价 (元/kWh)', 'Color', 'b');
    ylim([0.2, 0.8]);
    
    yyaxis right;
    total_ess_power = cost_details.hourly_data.ess_power(:,1) + cost_details.hourly_data.ess_power(:,2);
    bar(hours, total_ess_power, 'FaceColor', [0.8, 0.4, 0.4], 'EdgeColor', 'k');
    ylabel('储能总功率 (MW)', 'Color', 'r');
    
    xlabel('时间');
    title('电价与储能充放电策略');
    grid on;
    
    subplot(1,2,2);
    total_load = Load_ac' + Load_dc';
    total_renewable = sum(cost_details.hourly_data.wind_actual, 2) + sum(cost_details.hourly_data.pv_actual, 2);
    
    plot(hours, total_load, 'b-s', 'LineWidth', 2, 'DisplayName', '总负荷');
    hold on;
    plot(hours, total_renewable, 'g-^', 'LineWidth', 2, 'DisplayName', '风光出力');
    plot(hours, total_load - total_renewable, 'r--', 'LineWidth', 2, 'DisplayName', '净负荷');
    
    xlabel('时间');
    ylabel('功率 (MW)');
    title('负荷与风光出力对比');
    legend('Location', 'best');
    grid on;
    
    sgtitle('储能充放电策略分析', 'FontSize', 16, 'FontWeight', 'bold');
end

fprintf('\n测试完成！\n'); 