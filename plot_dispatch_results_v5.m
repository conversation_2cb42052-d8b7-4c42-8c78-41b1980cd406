function plot_dispatch_results_v5(X, cost_details, ESS_params)
    %% v5版本：储能风光协调调度结果可视化 - 分离绘图
    
    % 解析决策变量
    ESS1_node = round(X(1));
    ESS2_node = round(X(2));
    ESS1_capacity = X(3);
    ESS2_capacity = X(4);
    ESS1_hour_ratio = X(5);
    ESS2_hour_ratio = X(6);
    
    ESS1_power = ESS1_capacity / ESS1_hour_ratio;
    ESS2_power = ESS2_capacity / ESS2_hour_ratio;
    
    % 从cost_details中获取小时级数据
    if isfield(cost_details, 'hourly_data')
        wind_actual = cost_details.hourly_data.wind_actual;
        pv_actual = cost_details.hourly_data.pv_actual;
        ess_power = cost_details.hourly_data.ess_power;
        grid_power = cost_details.hourly_data.grid_power;
    else
        % 如果没有小时级数据，重新计算
        [wind_actual, pv_actual, ess_power, grid_power] = recalculate_hourly_data(X);
    end
    
    % 加载负荷数据
    global Load_ac Load_dc
    total_load = Load_ac' + Load_dc';
    
    % 计算各个组件的功率
    hours = 1:24;
    wind_total = sum(wind_actual, 2)';  % 总风电功率
    pv_total = sum(pv_actual, 2)';      % 总光伏功率
    
    % 储能功率分离
    ess1_power = ess_power(:,1)';
    ess2_power = ess_power(:,2)';
    
    % 分离充放电功率 (正值为放电，负值为充电)
    ess1_discharge = max(ess1_power, 0);
    ess1_charge = -min(ess1_power, 0);
    ess2_discharge = max(ess2_power, 0);
    ess2_charge = -min(ess2_power, 0);
    
    % 购电量 (正值为购电)
    purchase_power = max(grid_power', 0);
    
    %% 图1：调度结果可视化 - 所有功率从基准线开始绘制
    figure('Position', [100, 100, 1200, 600]);
    
    % 创建所有功率数据矩阵 - 从基准线开始绘制
    % 正值功率：购电量, 风电, 光伏, ESS1放电, ESS2放电
    % 负值功率：ESS1充电, ESS2充电
    power_data = [purchase_power; wind_total; pv_total; ess1_discharge; -ess1_charge; ess2_discharge; -ess2_charge]';
    
    % 绘制所有功率柱状图
    h_bars = bar(hours, power_data, 'grouped');
    hold on;
    
    % 设置颜色方案
    % 购电量 - 黄色
    h_bars(1).FaceColor = [1.0, 0.8, 0.2];
    h_bars(1).EdgeColor = 'k';
    h_bars(1).LineWidth = 0.5;
    
    % 风电 - 橙红色
    h_bars(2).FaceColor = [0.8, 0.4, 0.2];
    h_bars(2).EdgeColor = 'k';
    h_bars(2).LineWidth = 0.5;
    
    % 光伏 - 深红色
    h_bars(3).FaceColor = [0.6, 0.2, 0.4];
    h_bars(3).EdgeColor = 'k';
    h_bars(3).LineWidth = 0.5;
    
    % ESS1放电 - 浅绿色
    h_bars(4).FaceColor = [0.7, 0.9, 0.7];
    h_bars(4).EdgeColor = 'k';
    h_bars(4).LineWidth = 0.5;
    
    % ESS1充电 - 浅绿色（负值）
    h_bars(5).FaceColor = [0.7, 0.9, 0.7];
    h_bars(5).EdgeColor = 'k';
    h_bars(5).LineWidth = 0.5;
    
    % ESS2放电 - 深绿色
    h_bars(6).FaceColor = [0.4, 0.8, 0.6];
    h_bars(6).EdgeColor = 'k';
    h_bars(6).LineWidth = 0.5;
    
    % ESS2充电 - 深绿色（负值）
    h_bars(7).FaceColor = [0.4, 0.8, 0.6];
    h_bars(7).EdgeColor = 'k';
    h_bars(7).LineWidth = 0.5;
    
    % 叠加负荷曲线
    plot(hours, total_load, 'b-s', 'LineWidth', 2, 'MarkerSize', 6, ...
         'MarkerFaceColor', 'b', 'MarkerEdgeColor', 'k');
    
    % 设置坐标轴和标签
    xlabel('时间', 'FontSize', 14, 'FontWeight', 'bold');
    ylabel('功率/MW', 'FontSize', 14, 'FontWeight', 'bold');
    
    % 设置x轴刻度标签
    xticks([1, 6, 12, 18, 24]);
    xticklabels({'00:00', '06:00', '12:00', '18:00', '24:00'});
    
    % 设置y轴范围 - 调整以适应充放电功率
    ylim([-2, 12]);
    
    % 添加网格
    grid on;
    set(gca, 'GridAlpha', 0.3);
    
    % 添加零线
    yline(0, 'k-', 'LineWidth', 1);
    
    % 创建图例
    legend_handles = [h_bars(1), h_bars(2), h_bars(3), h_bars(4), h_bars(6), ...
                     plot(NaN, NaN, 'b-s', 'LineWidth', 2, 'MarkerSize', 6, 'MarkerFaceColor', 'b')];
    legend_labels = {'购电量', '风电', '光伏', 'ESS1', 'ESS2', '负荷'};
    
    legend(legend_handles, legend_labels, 'Location', 'northeast', ...
           'FontSize', 12, 'NumColumns', 3);
    
    % 设置标题
    title(sprintf('储能风光协调调度结果 (节点%d+%d, %.1f+%.1fMWh) - 正值放电，负值充电', ...
          ESS1_node, ESS2_node, ESS1_capacity/1000, ESS2_capacity/1000), ...
          'FontSize', 16, 'FontWeight', 'bold');
    
    % 调整图形属性
    set(gca, 'FontSize', 12);
    set(gca, 'LineWidth', 1);
    
    %% 图2：储能充放电功率柱形图 - 独立绘制
    figure('Position', [150, 150, 1200, 600]);
    
    % 创建充放电功率数据
    ess_power_data = [ess1_power; ess2_power]';
    
    % 绘制柱形图
    h_ess_bar = bar(hours, ess_power_data, 'grouped');
    
    % 设置颜色
    h_ess_bar(1).FaceColor = [0.2, 0.6, 0.2];  % ESS1 - 绿色
    h_ess_bar(1).EdgeColor = 'k';
    h_ess_bar(1).LineWidth = 0.5;
    
    h_ess_bar(2).FaceColor = [0.0, 0.4, 0.8];  % ESS2 - 蓝色
    h_ess_bar(2).EdgeColor = 'k';
    h_ess_bar(2).LineWidth = 0.5;
    
    % 添加零线
    yline(0, 'k--', 'LineWidth', 1);
    
    % 设置坐标轴和标签
    xlabel('时间', 'FontSize', 14, 'FontWeight', 'bold');
    ylabel('功率/MW', 'FontSize', 14, 'FontWeight', 'bold');
    
    % 设置x轴刻度标签
    xticks([1, 6, 12, 18, 24]);
    xticklabels({'00:00', '06:00', '12:00', '18:00', '24:00'});
    
    % 添加网格
    grid on;
    set(gca, 'GridAlpha', 0.3);
    
    % 设置图例
    legend({'ESS1', 'ESS2'}, 'Location', 'best', 'FontSize', 12);
    
    % 设置标题
    title('储能充放电功率 (正值放电, 负值充电)', 'FontSize', 16, 'FontWeight', 'bold');
    
    % 调整图形属性
    set(gca, 'FontSize', 12);
    set(gca, 'LineWidth', 1);
    
end

function [wind_actual, pv_actual, ess_power, grid_power] = recalculate_hourly_data(X)
    % 重新计算小时级数据的辅助函数
    global Pwt Ppv price Load_ac Load_dc
    
    % 风光基准容量
    wind_base = [0.5, 0.5, 0.5];
    pv_base = [0.25, 0.25, 0.25];
    
    % 解析决策变量
    ESS1_capacity = X(3);
    ESS2_capacity = X(4);
    ESS1_hour_ratio = X(5);
    ESS2_hour_ratio = X(6);
    
    ESS1_power = ESS1_capacity / ESS1_hour_ratio;
    ESS2_power = ESS2_capacity / ESS2_hour_ratio;
    
    % 解析风光实际上网功率
    wind_actual = reshape(X(7:150), 24, 6);
    pv_actual = wind_actual(:,4:6);
    wind_actual = wind_actual(:,1:3);
    
    % 解析储能充放电系数
    ess_coeff = reshape(X(151:198), 24, 2);
    
    % 价格驱动触发阈值
    prc_low = 0.30;   % 与主模型参数保持一致
    prc_high = 0.60;
    
    % 计算储能功率
    ess_power = zeros(24, 2);
    
    for t = 1:24
        % 可再生能源剩余功率
        total_renewable_forecast = sum(Pwt(t,:) .* wind_base) + sum(Ppv(t,:) .* pv_base);
        total_renewable_actual   = sum(wind_actual(t,:)) + sum(pv_actual(t,:));
        renewable_surplus = max(0, total_renewable_forecast - total_renewable_actual);
        
        % 触发条件
        allow_discharge = price(t) >= prc_high;
        allow_charge    = (renewable_surplus > 1e-3) || (price(t) <= prc_low);
        
        % 修正系数
        for ess_idx = 1:2
            if ess_coeff(t, ess_idx) > 0 && ~allow_discharge
                ess_coeff(t, ess_idx) = 0;
            elseif ess_coeff(t, ess_idx) < 0 && ~allow_charge
                ess_coeff(t, ess_idx) = 0;
            end
        end
        
        % 计算功率
        for ess_idx = 1:2
            if ess_idx == 1
                max_power_kw = ESS1_power;
            else
                max_power_kw = ESS2_power;
            end
            max_power_mw = max_power_kw / 1000;
            
            if ess_coeff(t, ess_idx) < 0 % 充电
                charge_pwr = min(abs(ess_coeff(t, ess_idx)) * max_power_mw, renewable_surplus);
                ess_power(t, ess_idx) = -charge_pwr;
            elseif ess_coeff(t, ess_idx) > 0 % 放电
                price_factor = min(1, max(0, (price(t) - prc_low) / (prc_high - prc_low)));
                ess_power(t, ess_idx) = ess_coeff(t, ess_idx) * max_power_mw * (1 + 0.5 * price_factor);
            else
                ess_power(t, ess_idx) = 0;
            end
        end
    end
    
    % 约束检查
    [ess_power, ~] = ESS_constraint_check_v5(ess_power, [ESS1_capacity, ESS2_capacity], [ESS1_power, ESS2_power]);
    
    % 计算电网交互功率
    total_load = Load_ac' + Load_dc';
    grid_power = zeros(24, 1);
    for t = 1:24
        total_renewable = sum(wind_actual(t,:)) + sum(pv_actual(t,:));
        total_ess = sum(ess_power(t,:));
        grid_power(t) = total_load(t) - total_renewable - total_ess;
    end
end

%% 辅助函数：储能约束检查（复制v5版本）
function [ess_power_corrected, penalty_E] = ESS_constraint_check_v5(ess_power, capacities, max_powers)
ess_power_corrected = ess_power;
penalty_E = 0;

eta_ch = 0.92; eta_dis = 0.92; dt = 1;

for ess_idx = 1:2
    Ebat0 = 0.5 * capacities(ess_idx) / 1000;
    Emin = 0.2 * capacities(ess_idx) / 1000;
    Emax = 0.8 * capacities(ess_idx) / 1000;
    Pmax = max_powers(ess_idx) / 1000;
    
    % 功率约束
    for t = 1:24
        if abs(ess_power_corrected(t, ess_idx)) > Pmax
            penalty_E = penalty_E + 5000 * (abs(ess_power_corrected(t, ess_idx)) - Pmax)^2;
            if ess_power_corrected(t, ess_idx) > Pmax
                ess_power_corrected(t, ess_idx) = Pmax;
            elseif ess_power_corrected(t, ess_idx) < -Pmax
                ess_power_corrected(t, ess_idx) = -Pmax;
            end
        end
    end
    
    Ebat = zeros(24,1);
    
    for t = 1:24
        if t == 1
            if ess_power_corrected(t, ess_idx) > 0
                Ebat(t) = Ebat0 - ess_power_corrected(t, ess_idx) * dt / eta_dis;
            else
                Ebat(t) = Ebat0 - ess_power_corrected(t, ess_idx) * dt * eta_ch;
            end
        else
            if ess_power_corrected(t, ess_idx) > 0
                Ebat(t) = Ebat(t-1) - ess_power_corrected(t, ess_idx) * dt / eta_dis;
            else
                Ebat(t) = Ebat(t-1) - ess_power_corrected(t, ess_idx) * dt * eta_ch;
            end
        end
        
        if Ebat(t) > Emax
            penalty_E = penalty_E + 2000 * (Ebat(t) - Emax)^2;
            Ebat(t) = Emax;
        elseif Ebat(t) < Emin
            penalty_E = penalty_E + 2000 * (Emin - Ebat(t))^2;
            Ebat(t) = Emin;
        end
    end
    
    if abs(Ebat(24) - Ebat0) > 0.05
        penalty_E = penalty_E + 1000 * (abs(Ebat(24) - Ebat0))^2;
    end
end
end 