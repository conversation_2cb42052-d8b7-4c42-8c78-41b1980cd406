%% 调试SOC约束对储能调度的影响
% 分析为什么储能在有弃电时不充电

clear; clc; close all;

% 全局变量声明
global mpc_base Pwt Ppv price shift_load cut_load Load_ac Load_dc
global N_SL N_CL Nwt Npv ac acq dc ESS_params

% 储能参数设置
ESS_params.Ce = 400;
ESS_params.Cp = 200;
ESS_params.Cmaint = 100;
ESS_params.ir = 0.015;
ESS_params.dr = 0.09;
ESS_params.Y = 10;
ESS_params.lambda = 0.08;
ESS_params.Cde = 60;
ESS_params.E_max = 15000;
ESS_params.P_max = 5000;

% 加载数据
load_data_v6;

fprintf('=== SOC约束调试分析 ===\n\n');

%% 构造测试解
n_var = 2 + 2 + 2 + N_SL + N_CL;
solution_test = zeros(1, n_var);
solution_test(1:2) = [30, 5];
solution_test(3:4) = [5000, 5000];  % 5MWh each
solution_test(5:6) = [4.0, 4.0];    % 4h ratio

idx = 7;
if N_SL > 0
    solution_test(idx:idx+N_SL-1) = shift_load(:,4)';
    idx = idx + N_SL;
end
if N_CL > 0
    solution_test(idx:idx+N_CL-1) = 0;
end

%% 手动模拟储能调度过程
ESS1_capacity = solution_test(3);
ESS2_capacity = solution_test(4);
ESS1_power = ESS1_capacity / solution_test(5);
ESS2_power = ESS2_capacity / solution_test(6);

ESS1_max_power = ESS1_power / 1000;  % MW
ESS2_max_power = ESS2_power / 1000;  % MW
total_ess_max_power = ESS1_max_power + ESS2_max_power;

fprintf('储能配置:\n');
fprintf('ESS1: %.0f kWh, %.0f kW (%.2f MW)\n', ESS1_capacity, ESS1_power, ESS1_max_power);
fprintf('ESS2: %.0f kWh, %.0f kW (%.2f MW)\n', ESS2_capacity, ESS2_power, ESS2_max_power);
fprintf('总功率: %.2f MW\n\n', total_ess_max_power);

% 风光基准容量
scale = 1.5;
wind_base = [2.16, 1.8, 2.7] * scale;
pv_base = [2.0, 2.5, 2.5] * scale;

%% 逐时刻模拟调度和SOC约束
ess_power_before = zeros(24, 2);  % 约束前的储能功率
ess_power_after = zeros(24, 2);   % 约束后的储能功率
soc1_trace = zeros(24, 1);        % ESS1的SOC轨迹
soc2_trace = zeros(24, 1);        % ESS2的SOC轨迹
curtailment_trace = zeros(24, 1); % 弃电轨迹

% SOC参数
eta_ch = 0.92;
eta_dis = 0.92;
soc1 = 0.10;  % 初始SOC 10%
soc2 = 0.10;
soc_min = 0.01;
soc_max = 0.99;

fprintf('时刻  负荷   可再生  盈余   调度前功率  SOC1  SOC2  调度后功率  弃电量  原因\n');
fprintf('----  ----   ------  ----   ----------  ----  ----  ----------  ------  ----\n');

for t = 1:24
    % 计算基础数据
    total_load = Load_ac(t) + Load_dc(t);
    wind_forecast = sum(Pwt(t,:) .* wind_base);
    pv_forecast = sum(Ppv(t,:) .* pv_base);
    total_renewable = wind_forecast + pv_forecast;
    renewable_surplus = total_renewable - total_load;
    
    % 时段特征判断
    is_peak_hour = (t >= 18 && t <= 21);
    is_valley_hour = (t >= 1 && t <= 6) || (t >= 23 && t <= 24);
    is_high_price = price(t) >= 0.6;
    is_low_price = price(t) <= 0.4;
    
    % 储能调度策略（模拟原始逻辑）
    ess_power_before(t,1) = 0;
    ess_power_before(t,2) = 0;
    
    if total_ess_max_power > 0
        % 策略1：最大充电（消纳优先）
        charge_total_max = total_ess_max_power * 0.9;
        ratio1 = ESS1_max_power / total_ess_max_power;
        ess_power_strategy1 = [-charge_total_max * ratio1, -charge_total_max * (1-ratio1)];
        
        % 策略2：根据时段特征调度
        if is_peak_hour && renewable_surplus < -2.0
            discharge_total = min(-renewable_surplus * 0.8, total_ess_max_power * 0.8);
            ratio1 = ESS1_max_power / total_ess_max_power;
            ess_power_strategy2 = [discharge_total * ratio1, discharge_total * (1-ratio1)];
        elseif is_high_price && renewable_surplus < -1.0
            discharge_total = total_ess_max_power * 0.5;
            ratio1 = ESS1_max_power / total_ess_max_power;
            ess_power_strategy2 = [discharge_total * ratio1, discharge_total * (1-ratio1)];
        elseif is_valley_hour || is_low_price || renewable_surplus > 0.1
            charge_total = total_ess_max_power * 0.6;
            ratio1 = ESS1_max_power / total_ess_max_power;
            ess_power_strategy2 = [-charge_total * ratio1, -charge_total * (1-ratio1)];
        else
            ess_power_strategy2 = [0, 0];
        end
        
        % 策略3：待机
        ess_power_strategy3 = [0, 0];
        
        % 评估每种策略的弃电量
        strategies = {ess_power_strategy1, ess_power_strategy2, ess_power_strategy3};
        curtailments = zeros(1, 3);
        
        for s = 1:3
            total_ess_power_s = sum(strategies{s});
            net_balance_s = total_load - total_renewable - total_ess_power_s;
            if net_balance_s < 0
                curtailments(s) = -net_balance_s;
            else
                curtailments(s) = 0;
            end
        end
        
        % 选择弃电最少的策略
        [~, best_strategy_idx] = min(curtailments);
        best_strategy = strategies{best_strategy_idx};
        
        ess_power_before(t,1) = best_strategy(1);
        ess_power_before(t,2) = best_strategy(2);
    end
    
    % 应用SOC约束
    ess_power_after(t,:) = ess_power_before(t,:);
    
    % ESS1 SOC约束检查
    if ess_power_after(t,1) > 0  % 放电
        new_soc1 = soc1 - ess_power_after(t,1) / (ESS1_capacity/1000) / eta_dis;
    else  % 充电
        new_soc1 = soc1 - ess_power_after(t,1) / (ESS1_capacity/1000) * eta_ch;
    end
    
    if new_soc1 > soc_max
        % SOC超上限，减少充电功率
        if ess_power_after(t,1) < 0
            excess_energy = new_soc1 - soc_max;
            required_power = excess_energy * (ESS1_capacity/1000) / eta_ch;
            ess_power_after(t,1) = ess_power_after(t,1) + required_power;
        end
        soc1 = soc_max;
    elseif new_soc1 < soc_min
        % SOC低于下限，减少放电功率
        if ess_power_after(t,1) > 0
            deficit_energy = soc_min - new_soc1;
            required_power = deficit_energy * (ESS1_capacity/1000) * eta_dis;
            ess_power_after(t,1) = ess_power_after(t,1) - required_power;
        end
        soc1 = soc_min;
    else
        soc1 = new_soc1;
    end
    
    % ESS2 SOC约束检查（类似）
    if ess_power_after(t,2) > 0  % 放电
        new_soc2 = soc2 - ess_power_after(t,2) / (ESS2_capacity/1000) / eta_dis;
    else  % 充电
        new_soc2 = soc2 - ess_power_after(t,2) / (ESS2_capacity/1000) * eta_ch;
    end
    
    if new_soc2 > soc_max
        if ess_power_after(t,2) < 0
            excess_energy = new_soc2 - soc_max;
            required_power = excess_energy * (ESS2_capacity/1000) / eta_ch;
            ess_power_after(t,2) = ess_power_after(t,2) + required_power;
        end
        soc2 = soc_max;
    elseif new_soc2 < soc_min
        if ess_power_after(t,2) > 0
            deficit_energy = soc_min - new_soc2;
            required_power = deficit_energy * (ESS2_capacity/1000) * eta_dis;
            ess_power_after(t,2) = ess_power_after(t,2) - required_power;
        end
        soc2 = soc_min;
    else
        soc2 = new_soc2;
    end
    
    % 记录SOC
    soc1_trace(t) = soc1;
    soc2_trace(t) = soc2;
    
    % 计算最终弃电量
    total_ess_power_final = sum(ess_power_after(t,:));
    net_balance_final = total_load - total_renewable - total_ess_power_final;
    if net_balance_final < 0
        curtailment_trace(t) = -net_balance_final;
    else
        curtailment_trace(t) = 0;
    end
    
    % 分析原因
    reason = '';
    if curtailment_trace(t) > 0.01
        if abs(sum(ess_power_before(t,:))) > 0.01 && abs(sum(ess_power_after(t,:))) < 0.01
            if soc1 >= 0.98 || soc2 >= 0.98
                reason = 'SOC上限';
            else
                reason = '其他约束';
            end
        elseif abs(sum(ess_power_before(t,:))) < 0.01
            reason = '调度策略';
        else
            reason = '功率限制';
        end
    end
    
    fprintf('%02d:00 %5.2f  %6.2f  %5.2f  %5.2f,%5.2f  %4.0f%% %4.0f%%  %5.2f,%5.2f  %6.3f  %s\n', ...
        t-1, total_load, total_renewable, renewable_surplus, ...
        ess_power_before(t,1), ess_power_before(t,2), soc1*100, soc2*100, ...
        ess_power_after(t,1), ess_power_after(t,2), curtailment_trace(t), reason);
end

%% 统计分析
fprintf('\n=== 统计分析 ===\n');

% 弃电统计
total_curtailment = sum(curtailment_trace);
curtailment_hours = find(curtailment_trace > 0.01);
fprintf('总弃电量: %.2f MWh\n', total_curtailment);
fprintf('弃电时段数: %d 小时\n', length(curtailment_hours));

% SOC约束影响统计
soc_limited_hours = 0;
strategy_limited_hours = 0;

for t = 1:24
    if curtailment_trace(t) > 0.01
        if abs(sum(ess_power_before(t,:))) > 0.01 && abs(sum(ess_power_after(t,:))) < 0.01
            if soc1_trace(t) >= 0.98 || soc2_trace(t) >= 0.98
                soc_limited_hours = soc_limited_hours + 1;
            end
        elseif abs(sum(ess_power_before(t,:))) < 0.01
            strategy_limited_hours = strategy_limited_hours + 1;
        end
    end
end

fprintf('SOC约束导致的弃电时段: %d 小时\n', soc_limited_hours);
fprintf('调度策略导致的弃电时段: %d 小时\n', strategy_limited_hours);

%% 解决方案建议
fprintf('\n=== 解决方案建议 ===\n');

if soc_limited_hours > 0
    fprintf('1. SOC约束问题:\n');
    fprintf('   - 降低初始SOC到5%%，增加充电空间\n');
    fprintf('   - 提高SOC上限到99.5%%\n');
    fprintf('   - 增加储能容量，减少SOC变化幅度\n');
end

if strategy_limited_hours > 0
    fprintf('2. 调度策略问题:\n');
    fprintf('   - 强化弃电消纳优先级\n');
    fprintf('   - 简化策略选择逻辑\n');
    fprintf('   - 直接基于功率平衡进行调度\n');
end

fprintf('\n最大SOC: ESS1=%.1f%%, ESS2=%.1f%%\n', max(soc1_trace)*100, max(soc2_trace)*100);
fprintf('最小SOC: ESS1=%.1f%%, ESS2=%.1f%%\n', min(soc1_trace)*100, min(soc2_trace)*100);

fprintf('\n=== 调试完成 ===\n');
