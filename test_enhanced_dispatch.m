%% 测试增强后的储能调度逻辑
% 验证大容量储能对弃电消纳的改善效果

clear; clc; close all;

fprintf('=== 测试增强后的储能调度逻辑 ===\n');

% 加载数据
load_data_v6;

% 测试三种配置
configs = {
    struct('name', '无储能', 'ESS1_cap', 0, 'ESS2_cap', 0, 'ESS1_pow', 0, 'ESS2_pow', 0);
    struct('name', '小容量储能', 'ESS1_cap', 800, 'ESS2_cap', 800, 'ESS1_pow', 150, 'ESS2_pow', 150);
    struct('name', '大容量储能', 'ESS1_cap', 3000, 'ESS2_cap', 3000, 'ESS1_pow', 1000, 'ESS2_pow', 1000);
};

results = cell(length(configs), 1);

for i = 1:length(configs)
    config = configs{i};
    fprintf('\n--- 测试配置：%s ---\n', config.name);
    
    % 构造决策变量
    if config.ESS1_cap == 0
        % 无储能配置
        test_X = [12, 5, 1, 1, 5, 5, zeros(1, 200)]; % 最小容量避免除零
    else
        test_X = [12, 5, config.ESS1_cap, config.ESS2_cap, ...
                  config.ESS1_cap/config.ESS1_pow, config.ESS2_cap/config.ESS2_pow, ...
                  zeros(1, 200)];
    end
    
    try
        % 调用目标函数
        [f1, f2, cost_details] = ESS_objective_v6(test_X);
        
        fprintf('储能容量: %.1f + %.1f = %.1f MWh\n', ...
            config.ESS1_cap/1000, config.ESS2_cap/1000, (config.ESS1_cap+config.ESS2_cap)/1000);
        fprintf('储能功率: %.1f + %.1f = %.1f MW\n', ...
            config.ESS1_pow/1000, config.ESS2_pow/1000, (config.ESS1_pow+config.ESS2_pow)/1000);
        fprintf('系统运行成本: %.2f 万元\n', f2/10000);
        
        if isfield(cost_details, 'renewable_stats')
            stats = cost_details.renewable_stats;
            fprintf('弃风弃光率: %.1f%%\n', 100 - stats.wind_utilization_rate);
            fprintf('总购电量: %.2f MWh\n', stats.total_purchase_mwh);
            
            % 计算弃电量
            total_curtail = (stats.wind_forecast_mwh + stats.pv_forecast_mwh) - ...
                           (stats.wind_actual_mwh + stats.pv_actual_mwh);
            fprintf('总弃电量: %.2f MWh\n', total_curtail);
        end
        
        if isfield(cost_details, 'ess_utilization')
            util = cost_details.ess_utilization;
            fprintf('储能利用率: %.1f%% / %.1f%%\n', ...
                util.ess1_rate*100, util.ess2_rate*100);
        end
        
        % 保存结果
        results{i} = struct('config', config, 'f1', f1, 'f2', f2, 'details', cost_details);
        
    catch ME
        fprintf('错误: %s\n', ME.message);
        results{i} = struct('config', config, 'error', ME.message);
    end
end

% 对比分析
fprintf('\n=== 对比分析 ===\n');
fprintf('配置\t\t\t运行成本(万元)\t弃电率(%%)\t储能利用率(%%)\n');
fprintf('------------------------------------------------------------\n');

for i = 1:length(results)
    if isfield(results{i}, 'f2')
        config_name = results{i}.config.name;
        run_cost = results{i}.f2 / 10000;
        
        if isfield(results{i}.details, 'renewable_stats')
            curtail_rate = 100 - results{i}.details.renewable_stats.wind_utilization_rate;
        else
            curtail_rate = 0;
        end
        
        if isfield(results{i}.details, 'ess_utilization')
            util_avg = (results{i}.details.ess_utilization.ess1_rate + ...
                       results{i}.details.ess_utilization.ess2_rate) / 2 * 100;
        else
            util_avg = 0;
        end
        
        fprintf('%-15s\t%.2f\t\t%.1f\t\t%.1f\n', config_name, run_cost, curtail_rate, util_avg);
    end
end

% 绘制对比图
if length(results) >= 2 && isfield(results{end}, 'details')
    figure('Name', '储能调度效果对比');
    
    % 充放电功率对比
    subplot(2,2,1);
    if isfield(results{end}.details, 'hourly_data')
        ess_power = results{end}.details.hourly_data.ess_power;
        bar(1:24, [ess_power(:,1), ess_power(:,2)]);
        title('大容量储能充放电功率');
        xlabel('时间(h)'); ylabel('功率(MW)');
        legend('ESS1', 'ESS2');
        grid on;
    end
    
    % 弃电购电对比
    subplot(2,2,2);
    if isfield(results{end}.details, 'hourly_data')
        P_curtail = results{end}.details.hourly_data.P_curtail;
        P_import = results{end}.details.hourly_data.P_import;
        bar(1:24, [P_curtail, P_import]);
        title('弃电量与购电量');
        xlabel('时间(h)'); ylabel('功率(MW)');
        legend('弃电量', '购电量');
        grid on;
    end
    
    % 成本对比
    subplot(2,2,3);
    costs = [];
    labels = {};
    for i = 1:length(results)
        if isfield(results{i}, 'f2')
            costs(end+1) = results{i}.f2 / 10000;
            labels{end+1} = results{i}.config.name;
        end
    end
    bar(costs);
    set(gca, 'XTickLabel', labels);
    title('系统运行成本对比');
    ylabel('成本(万元)');
    grid on;
    
    % 弃电率对比
    subplot(2,2,4);
    curtail_rates = [];
    for i = 1:length(results)
        if isfield(results{i}, 'details') && isfield(results{i}.details, 'renewable_stats')
            curtail_rates(end+1) = 100 - results{i}.details.renewable_stats.wind_utilization_rate;
        else
            curtail_rates(end+1) = 0;
        end
    end
    bar(curtail_rates);
    set(gca, 'XTickLabel', labels);
    title('弃风弃光率对比');
    ylabel('弃电率(%)');
    grid on;
end

fprintf('\n=== 测试完成 ===\n'); 