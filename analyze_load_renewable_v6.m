%% 分析负荷和可再生能源数据，优化储能调度策略
% 深入分析24小时负荷曲线和风光出力特性

clear; clc; close all;

% 全局变量声明
global mpc_base Pwt Ppv price shift_load cut_load Load_ac Load_dc
global N_SL N_CL Nwt Npv ac acq dc ESS_params

% 加载数据
load_data_v6;

% 风光基准容量
scale = 1.5;
wind_base = [2.16, 1.8, 2.7] * scale;
pv_base = [2.0, 2.5, 2.5] * scale;

fprintf('=== 负荷和可再生能源数据分析 ===\n\n');

%% 计算24小时数据
total_load = zeros(24, 1);
wind_forecast = zeros(24, 1);
pv_forecast = zeros(24, 1);
renewable_surplus = zeros(24, 1);

for t = 1:24
    % 总负荷
    total_load(t) = Load_ac(t) + Load_dc(t);
    
    % 可再生能源预测
    wind_forecast(t) = sum(Pwt(t,:) .* wind_base);
    pv_forecast(t) = sum(Ppv(t,:) .* pv_base);
    
    % 可再生能源盈余
    renewable_surplus(t) = wind_forecast(t) + pv_forecast(t) - total_load(t);
end

%% 数据统计分析
fprintf('=== 24小时数据统计 ===\n');
fprintf('负荷峰值: %.2f MW (时刻: %02d:00)\n', max(total_load), find(total_load == max(total_load))-1);
fprintf('负荷谷值: %.2f MW (时刻: %02d:00)\n', min(total_load), find(total_load == min(total_load))-1);
fprintf('负荷峰谷差: %.2f MW\n', max(total_load) - min(total_load));

fprintf('\n风电出力峰值: %.2f MW (时刻: %02d:00)\n', max(wind_forecast), find(wind_forecast == max(wind_forecast))-1);
fprintf('光伏出力峰值: %.2f MW (时刻: %02d:00)\n', max(pv_forecast), find(pv_forecast == max(pv_forecast))-1);

fprintf('\n可再生盈余最大: %.2f MW (时刻: %02d:00)\n', max(renewable_surplus), find(renewable_surplus == max(renewable_surplus))-1);
fprintf('可再生缺口最大: %.2f MW (时刻: %02d:00)\n', min(renewable_surplus), find(renewable_surplus == min(renewable_surplus))-1);

%% 分时段分析
fprintf('\n=== 分时段特征分析 ===\n');

% 定义时段
valley_hours = [1:6, 23:24];      % 低谷时段
peak_hours = [9:12, 17:21];       % 高峰时段
normal_hours = setdiff(1:24, [valley_hours, peak_hours]); % 平时段

fprintf('低谷时段 (01-06, 23-24):\n');
fprintf('  平均负荷: %.2f MW\n', mean(total_load(valley_hours)));
fprintf('  平均可再生: %.2f MW\n', mean(wind_forecast(valley_hours) + pv_forecast(valley_hours)));
fprintf('  平均盈余: %.2f MW\n', mean(renewable_surplus(valley_hours)));

fprintf('\n高峰时段 (09-12, 17-21):\n');
fprintf('  平均负荷: %.2f MW\n', mean(total_load(peak_hours)));
fprintf('  平均可再生: %.2f MW\n', mean(wind_forecast(peak_hours) + pv_forecast(peak_hours)));
fprintf('  平均盈余: %.2f MW\n', mean(renewable_surplus(peak_hours)));

fprintf('\n平时段:\n');
fprintf('  平均负荷: %.2f MW\n', mean(total_load(normal_hours)));
fprintf('  平均可再生: %.2f MW\n', mean(wind_forecast(normal_hours) + pv_forecast(normal_hours)));
fprintf('  平均盈余: %.2f MW\n', mean(renewable_surplus(normal_hours)));

%% 电价分析
fprintf('\n=== 电价分析 ===\n');
high_price_hours = find(price >= 0.6);
low_price_hours = find(price <= 0.4);
mid_price_hours = find(price > 0.4 & price < 0.6);

fprintf('高电价时段 (≥0.6元/kWh): ');
for i = 1:length(high_price_hours)
    fprintf('%02d:00 ', high_price_hours(i)-1);
end
fprintf('\n');

fprintf('低电价时段 (≤0.4元/kWh): ');
for i = 1:length(low_price_hours)
    fprintf('%02d:00 ', low_price_hours(i)-1);
end
fprintf('\n');

%% 储能调度机会分析
fprintf('\n=== 储能调度机会分析 ===\n');

% 充电机会：低电价且有盈余，或低谷时段
charge_opportunities = [];
for t = 1:24
    is_low_price = price(t) <= 0.4;
    is_valley = ismember(t, valley_hours);
    has_surplus = renewable_surplus(t) > 0.1;
    
    if (is_low_price || is_valley || has_surplus) && ~ismember(t, peak_hours)
        charge_opportunities = [charge_opportunities, t];
    end
end

fprintf('充电机会时段: ');
for i = 1:length(charge_opportunities)
    fprintf('%02d:00 ', charge_opportunities(i)-1);
end
fprintf('\n');

% 放电机会：高电价或高峰时段，或有缺口
discharge_opportunities = [];
for t = 1:24
    is_high_price = price(t) >= 0.6;
    is_peak = ismember(t, peak_hours);
    has_deficit = renewable_surplus(t) < -0.1;
    
    if is_high_price || is_peak || has_deficit
        discharge_opportunities = [discharge_opportunities, t];
    end
end

fprintf('放电机会时段: ');
for i = 1:length(discharge_opportunities)
    fprintf('%02d:00 ', discharge_opportunities(i)-1);
end
fprintf('\n');

%% 详细时刻分析
fprintf('\n=== 详细时刻分析 ===\n');
fprintf('时刻  负荷   风电   光伏   总RE   盈余   电价   建议策略\n');
fprintf('----  ----   ----   ----   ----   ----   ----   --------\n');

for t = 1:24
    total_re = wind_forecast(t) + pv_forecast(t);
    surplus = renewable_surplus(t);
    
    % 确定建议策略
    strategy = '待机';
    if ismember(t, charge_opportunities)
        if surplus > 0.5
            strategy = '强充电';
        else
            strategy = '充电';
        end
    elseif ismember(t, discharge_opportunities)
        if ismember(t, peak_hours) && surplus > 0
            strategy = '强放电';
        else
            strategy = '放电';
        end
    end
    
    fprintf('%02d:00 %5.2f  %5.2f  %5.2f  %5.2f  %5.2f  %4.2f   %s\n', ...
        t-1, total_load(t), wind_forecast(t), pv_forecast(t), total_re, surplus, price(t), strategy);
end

%% 储能容量需求分析
fprintf('\n=== 储能容量需求分析 ===\n');

% 计算理想的储能充放电功率
ideal_ess_power = zeros(24, 1);
for t = 1:24
    if ismember(t, charge_opportunities)
        if renewable_surplus(t) > 0.5
            ideal_ess_power(t) = -min(renewable_surplus(t) * 0.8, 2.0); % 充电
        else
            ideal_ess_power(t) = -0.5; % 小功率充电
        end
    elseif ismember(t, discharge_opportunities)
        if ismember(t, peak_hours) && renewable_surplus(t) > 0
            ideal_ess_power(t) = 1.5; % 强放电
        else
            ideal_ess_power(t) = 1.0; % 放电
        end
    end
end

% 计算所需储能容量
charge_energy = sum(max(-ideal_ess_power, 0)); % 充电总量
discharge_energy = sum(max(ideal_ess_power, 0)); % 放电总量
required_capacity = max(charge_energy, discharge_energy) * 1.2; % 考虑安全裕度

fprintf('理想充电总量: %.2f MWh\n', charge_energy);
fprintf('理想放电总量: %.2f MWh\n', discharge_energy);
fprintf('建议储能容量: %.2f MWh\n', required_capacity);
fprintf('建议储能功率: %.2f MW\n', max(abs(ideal_ess_power)) * 1.2);

%% 绘制分析图
figure('Position', [100, 100, 1400, 800]);

% 子图1：负荷和可再生能源
subplot(2,2,1);
hours = 1:24;
plot(hours, total_load, 'b-o', 'LineWidth', 2, 'DisplayName', '负荷');
hold on;
plot(hours, wind_forecast, 'g-s', 'LineWidth', 1.5, 'DisplayName', '风电');
plot(hours, pv_forecast, 'r-^', 'LineWidth', 1.5, 'DisplayName', '光伏');
plot(hours, wind_forecast + pv_forecast, 'k--', 'LineWidth', 2, 'DisplayName', '总可再生');
xlabel('时间 (h)');
ylabel('功率 (MW)');
title('负荷与可再生能源出力');
legend('Location', 'best');
grid on;

% 子图2：可再生能源盈余
subplot(2,2,2);
bar(hours, renewable_surplus, 'FaceColor', [0.7, 0.7, 0.7]);
hold on;
yline(0, 'k-', 'LineWidth', 1);
xlabel('时间 (h)');
ylabel('盈余功率 (MW)');
title('可再生能源盈余/缺口');
grid on;

% 子图3：电价
subplot(2,2,3);
plot(hours, price, 'r-o', 'LineWidth', 2);
hold on;
yline(0.6, 'r--', '高电价线', 'LineWidth', 1);
yline(0.4, 'b--', '低电价线', 'LineWidth', 1);
xlabel('时间 (h)');
ylabel('电价 (元/kWh)');
title('分时电价');
grid on;

% 子图4：理想储能调度
subplot(2,2,4);
bar(hours, ideal_ess_power, 'FaceColor', [0.2, 0.6, 0.8]);
hold on;
yline(0, 'k-', 'LineWidth', 1);
xlabel('时间 (h)');
ylabel('储能功率 (MW)');
title('理想储能调度策略');
grid on;

fprintf('\n=== 分析完成 ===\n');
