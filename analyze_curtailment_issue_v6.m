%% 分析弃风弃光率高的原因并提出解决方案
% 目标：将弃风弃光率从17.1%降低到5%

clear; clc; close all;

% 全局变量声明
global mpc_base Pwt Ppv price shift_load cut_load Load_ac Load_dc
global N_SL N_CL Nwt Npv ac acq dc ESS_params

% 当前储能参数设置
ESS_params.Ce = 700;        % 储能单位容量成本 (元/kWh)
ESS_params.Cp = 400;        % 储能单位功率成本 (元/kW)
ESS_params.Cmaint = 180;    % 年运行维护成本 (元/kW·年)
ESS_params.ir = 0.015;      % 通胀膨胀率
ESS_params.dr = 0.09;       % 贴现率
ESS_params.Y = 10;          % 储能寿命 (年)
ESS_params.lambda = 0.08;   % 净报废成本系数
ESS_params.Cde = 60;        % 单位容量报废处置成本 (元/kWh)
ESS_params.E_max = 8000;    % 最大安装容量 8 MWh
ESS_params.P_max = 2500;    % 最大充放电功率 2.5 MW

% 加载数据
load_data_v6;

% 风光基准容量
scale = 1.5;
wind_base = [2.16, 1.8, 2.7] * scale;
pv_base = [2.0, 2.5, 2.5] * scale;

fprintf('=== 弃风弃光率高的原因分析 ===\n\n');

%% 计算理论最优储能需求
total_wind_forecast = sum(sum(Pwt .* wind_base));
total_pv_forecast = sum(sum(Ppv .* pv_base));
total_renewable_forecast = total_wind_forecast + total_pv_forecast;

fprintf('可再生能源装机情况:\n');
fprintf('风电总装机: %.2f MW\n', sum(wind_base));
fprintf('光伏总装机: %.2f MW\n', sum(pv_base));
fprintf('总可再生装机: %.2f MW\n', sum(wind_base) + sum(pv_base));
fprintf('日发电量预测: %.2f MWh\n', total_renewable_forecast);

%% 分析24小时可再生能源盈余分布
renewable_surplus = zeros(24, 1);
total_load = zeros(24, 1);
wind_forecast = zeros(24, 1);
pv_forecast = zeros(24, 1);

for t = 1:24
    total_load(t) = Load_ac(t) + Load_dc(t);
    wind_forecast(t) = sum(Pwt(t,:) .* wind_base);
    pv_forecast(t) = sum(Ppv(t,:) .* pv_base);
    renewable_surplus(t) = wind_forecast(t) + pv_forecast(t) - total_load(t);
end

% 统计盈余情况
surplus_hours = find(renewable_surplus > 0);
deficit_hours = find(renewable_surplus < 0);
total_surplus = sum(max(renewable_surplus, 0));
total_deficit = sum(max(-renewable_surplus, 0));

fprintf('\n可再生能源盈余分析:\n');
fprintf('有盈余时段数: %d 小时\n', length(surplus_hours));
fprintf('有缺口时段数: %d 小时\n', length(deficit_hours));
fprintf('总盈余量: %.2f MWh\n', total_surplus);
fprintf('总缺口量: %.2f MWh\n', total_deficit);
fprintf('理论最大弃电量: %.2f MWh\n', total_surplus);

%% 计算达到5%弃风弃光率所需的储能容量
target_curtailment_rate = 0.05;  % 目标弃风弃光率5%
current_curtailment_rate = 0.171; % 当前弃风弃光率17.1%

% 当前弃电量
current_curtailment = total_renewable_forecast * current_curtailment_rate;
target_curtailment = total_renewable_forecast * target_curtailment_rate;
additional_absorption_needed = current_curtailment - target_curtailment;

fprintf('\n弃风弃光率目标分析:\n');
fprintf('当前弃电量: %.2f MWh (%.1f%%)\n', current_curtailment, current_curtailment_rate*100);
fprintf('目标弃电量: %.2f MWh (%.1f%%)\n', target_curtailment, target_curtailment_rate*100);
fprintf('需额外消纳: %.2f MWh\n', additional_absorption_needed);

%% 分析当前储能配置的理论消纳能力
% 从结果推算当前储能配置
% 储能投资成本294.18万元 = (E1+E2)*700 + (P1+P2)*400
% 假设功率容量配比为3.5小时，则P = E/3.5
% 294.18*10000 = E*700 + E/3.5*400 = E*(700 + 400/3.5) = E*814.3
current_total_capacity = 294.18*10000 / 814.3; % kWh
current_total_power = current_total_capacity / 3.5; % kW

fprintf('\n当前储能配置估算:\n');
fprintf('估算总容量: %.0f kWh (%.1f MWh)\n', current_total_capacity, current_total_capacity/1000);
fprintf('估算总功率: %.0f kW (%.1f MW)\n', current_total_power, current_total_power/1000);

% 理论日消纳能力（假设每天充放电1次）
theoretical_daily_absorption = current_total_capacity / 1000; % MWh
fprintf('理论日消纳能力: %.2f MWh\n', theoretical_daily_absorption);

%% 计算所需储能容量
% 方法1：基于额外消纳需求
required_additional_capacity = additional_absorption_needed; % MWh
total_required_capacity = current_total_capacity/1000 + required_additional_capacity; % MWh

% 方法2：基于盈余时段分析
% 分析盈余时段的储能需求
surplus_by_hour = max(renewable_surplus, 0);
max_continuous_surplus = 0;
current_surplus = 0;
for t = 1:24
    if surplus_by_hour(t) > 0
        current_surplus = current_surplus + surplus_by_hour(t);
    else
        max_continuous_surplus = max(max_continuous_surplus, current_surplus);
        current_surplus = 0;
    end
end
max_continuous_surplus = max(max_continuous_surplus, current_surplus);

fprintf('\n储能容量需求分析:\n');
fprintf('方法1 - 基于消纳缺口: %.1f MWh\n', total_required_capacity);
fprintf('方法2 - 基于最大连续盈余: %.1f MWh\n', max_continuous_surplus);
fprintf('建议储能容量: %.1f MWh\n', max(total_required_capacity, max_continuous_surplus));

%% 分析储能功率需求
% 分析最大盈余功率
max_surplus_power = max(renewable_surplus);
avg_surplus_power = mean(surplus_by_hour(surplus_by_hour > 0));

fprintf('\n储能功率需求分析:\n');
fprintf('最大盈余功率: %.2f MW\n', max_surplus_power);
fprintf('平均盈余功率: %.2f MW\n', avg_surplus_power);
fprintf('建议储能功率: %.1f MW\n', max(max_surplus_power * 1.2, 3.0));

%% 优化建议
fprintf('\n=== 优化建议 ===\n');

% 建议1：增加储能容量
recommended_capacity = max(total_required_capacity, max_continuous_surplus) * 1.2; % 增加20%裕度
recommended_power = max(max_surplus_power * 1.2, 3.0);

fprintf('1. 储能容量优化:\n');
fprintf('   建议总容量: %.1f MWh (当前约%.1f MWh)\n', recommended_capacity, current_total_capacity/1000);
fprintf('   建议总功率: %.1f MW (当前约%.1f MW)\n', recommended_power, current_total_power/1000);
fprintf('   容量增加: %.1f MWh (%.0f%%)\n', recommended_capacity - current_total_capacity/1000, ...
    (recommended_capacity - current_total_capacity/1000)/(current_total_capacity/1000)*100);

% 建议2：优化调度策略
fprintf('\n2. 调度策略优化:\n');
fprintf('   - 提高弃电成本到2.0元/kWh，强化消纳激励\n');
fprintf('   - 优先在盈余时段充电，即使不是低电价时段\n');
fprintf('   - 减少不必要的放电，保留容量用于消纳\n');

% 建议3：参数调整
fprintf('\n3. 参数调整建议:\n');
fprintf('   ESS_params.E_max = %.0f; %% 提高最大容量限制\n', recommended_capacity*1000);
fprintf('   ESS_params.P_max = %.0f; %% 提高最大功率限制\n', recommended_power*1000);
fprintf('   curtail_cost_rate = 2.0; %% 提高弃电成本\n');

%% 经济性分析
additional_investment = (recommended_capacity*1000 - current_total_capacity) * ESS_params.Ce + ...
                       (recommended_power*1000 - current_total_power) * ESS_params.Cp;
annual_curtailment_saving = additional_absorption_needed * 365 * 1.2; % 元/年

fprintf('\n=== 经济性分析 ===\n');
fprintf('额外投资成本: %.1f 万元\n', additional_investment/10000);
fprintf('年弃电成本节省: %.1f 万元\n', annual_curtailment_saving/10000);
if annual_curtailment_saving > 0
    payback_period = additional_investment / annual_curtailment_saving;
    fprintf('投资回收期: %.1f 年\n', payback_period);
else
    fprintf('需要重新评估经济性\n');
end

%% 绘制分析图
figure('Position', [100, 100, 1400, 800]);

% 子图1：24小时可再生能源盈余
subplot(2,2,1);
hours = 1:24;
bar(hours, renewable_surplus, 'FaceColor', [0.3, 0.7, 0.3]);
hold on;
yline(0, 'k-', 'LineWidth', 1);
xlabel('时间 (h)');
ylabel('盈余功率 (MW)');
title('24小时可再生能源盈余分布');
grid on;

% 子图2：储能容量需求对比
subplot(2,2,2);
categories = {'当前容量', '建议容量', '理论需求'};
values = [current_total_capacity/1000, recommended_capacity, total_surplus];
bar(values, 'FaceColor', [0.2, 0.6, 0.8]);
set(gca, 'XTickLabel', categories);
ylabel('容量 (MWh)');
title('储能容量需求对比');
grid on;

% 子图3：弃风弃光率对比
subplot(2,2,3);
curtailment_rates = [current_curtailment_rate*100, target_curtailment_rate*100];
bar(curtailment_rates, 'FaceColor', [0.8, 0.4, 0.2]);
set(gca, 'XTickLabel', {'当前', '目标'});
ylabel('弃风弃光率 (%)');
title('弃风弃光率对比');
grid on;

% 子图4：投资效益分析
subplot(2,2,4);
costs = [additional_investment/10000, annual_curtailment_saving/10000*10]; % 10年总节省
bar(costs, 'FaceColor', [0.6, 0.3, 0.7]);
set(gca, 'XTickLabel', {'额外投资', '10年节省'});
ylabel('成本 (万元)');
title('投资效益分析');
grid on;

fprintf('\n=== 分析完成 ===\n');
fprintf('主要结论：需要增加储能容量约%.1f MWh，功率约%.1f MW\n', ...
    recommended_capacity - current_total_capacity/1000, recommended_power - current_total_power/1000);
