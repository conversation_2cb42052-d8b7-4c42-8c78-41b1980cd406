function [f1, f2, cost_details] = ESS_objective(X)
% 储能选址定容双目标函数 - 修正版 v4
% 输入: X - 决策变量向量
% 输出: f1 - 储能全寿命周期成本 (元)
%       f2 - 系统运行成本 (元)
%       cost_details - 成本详细信息结构体

global mpc_base Pwt Ppv price shift_load cut_load
global N_SL N_CL Nwt Npv ac acq dc ESS_params

% 风光基准容量定义 (MW) - 与新配电网结构中的发电机额定容量一致
wind_base = [0.5, 0.5, 0.5]; % 各风电场额定容量 (节点5,24,29)
pv_base = [0.25, 0.25, 0.25]; % 各光伏电站额定容量 (节点10,17,22)

%% 解析决策变量
ESS1_node = round(X(1));
ESS2_node = round(X(2));
ESS1_capacity = X(3); % kWh
ESS2_capacity = X(4); % kWh
ESS1_power = X(5); % kW
ESS2_power = X(6); % kW

% 修正5: 功率-容量配比惩罚(基于投资成本)
ratio_min_h = 2;   % 最小配比: 2h
ratio_max_h = 10;  % 最大配比: 10h
penalty_multiplier = 1.5; % 惩罚乘数，使其略高于直接投资
capacity_power_penalty = 0;

% 储能1配比检查
if ESS1_power > 1e-3 % 避免除以0
    ratio1 = ESS1_capacity / ESS1_power;
    if ratio1 < ratio_min_h
        % 功率过大(容量不足)，惩罚等于补足容量的投资成本
        deficit_C = ratio_min_h * ESS1_power - ESS1_capacity;
        capacity_power_penalty = capacity_power_penalty + penalty_multiplier * ESS_params.Ce * deficit_C;
    elseif ratio1 > ratio_max_h
        % 功率过小(功率不足)，惩罚等于补足功率的投资成本
        deficit_P = ESS1_capacity / ratio_max_h - ESS1_power;
        capacity_power_penalty = capacity_power_penalty + penalty_multiplier * ESS_params.Cp * deficit_P;
    end
end

% 储能2配比检查
if ESS2_power > 1e-3 % 避免除以0
    ratio2 = ESS2_capacity / ESS2_power;
    if ratio2 < ratio_min_h
        % 功率过大(容量不足)
        deficit_C = ratio_min_h * ESS2_power - ESS2_capacity;
        capacity_power_penalty = capacity_power_penalty + penalty_multiplier * ESS_params.Ce * deficit_C;
    elseif ratio2 > ratio_max_h
        % 功率过小(功率不足)
        deficit_P = ESS2_capacity / ratio_max_h - ESS2_power;
        capacity_power_penalty = capacity_power_penalty + penalty_multiplier * ESS_params.Cp * deficit_P;
    end
end

% 风光实际上网功率解析
wind_actual = reshape(X(7:150), 24, 6);  % 24×6矩阵
pv_actual = wind_actual(:,4:6);           % 光伏实际上网功率 (MW)
wind_actual = wind_actual(:,1:3);         % 风电实际上网功率 (MW)

% 修正4: 增加最低可再生消纳率硬约束（弃电率≤20%）
min_wind_ratio = 0.5; % 最低风电消纳率 50% (软约束)
min_pv_ratio = 0.5;   % 最低光伏消纳率 50% (软约束)

% 新增: 最低消纳率软约束罚函数参数
penalty_min_utilization = 0;  % 罚函数累计量
penalty_coef_util = 10000;    % 每 MWh 不足惩罚 1 万元，为弃电成本的一半

violate_utilization = false;

for t = 1:24
    for i = 1:3
        % 风电出力约束：实际上网功率不能超过预测出力
        wind_available = Pwt(t,i) * wind_base(i);  % 风电预测出力 (MW)
        if wind_actual(t,i) > wind_available
            violate_utilization = true;
            wind_actual(t,i) = wind_available; % 强制约束
        end
        
        % 修正4: 最低风电消纳率约束
        if wind_available > 0 && wind_actual(t,i) < min_wind_ratio * wind_available
            violate_utilization = true;
        end
        
        % 光伏出力约束：实际上网功率不能超过预测出力
        pv_available = Ppv(t,i) * pv_base(i);      % 光伏预测出力 (MW)
        if pv_actual(t,i) > pv_available
            violate_utilization = true;
            pv_actual(t,i) = pv_available; % 强制约束
        end
        
        % 修正4: 最低光伏消纳率约束
        if pv_available > 0 && pv_actual(t,i) < min_pv_ratio * pv_available
            violate_utilization = true;
        end
        
        % 确保实际上网功率非负
        if wind_actual(t,i) < 0
            violate_utilization = true;
            wind_actual(t,i) = 0;
        end
        if pv_actual(t,i) < 0
            violate_utilization = true;
            pv_actual(t,i) = 0;
        end

        %================ 新增: 计算最低消纳率罚函数 ================
        deficit_w = max(0, min_wind_ratio * wind_available - wind_actual(t,i));
        penalty_min_utilization = penalty_min_utilization + penalty_coef_util * deficit_w;
        if pv_available > 0
            deficit_p = max(0, min_pv_ratio * pv_available - pv_actual(t,i));
            penalty_min_utilization = penalty_min_utilization + penalty_coef_util * deficit_p;
        end
        %===========================================================
    end
end

% 若风光最低利用率被破坏，已改为软约束，故不再提前返回
% if violate_utilization
%     f1 = 1e8; f2 = 1e8; 
%     if nargout > 2
%         cost_details = struct('C_i', 0, 'C_m', 0, 'C_d', 0, 'grid_cost', 0, ...
%                              'curtail_cost', 0, 'curtail_cost_rate', 20.0, 'loss_cost', 0, 'C_SL', 0, 'C_CL', 0, ...
%                              'penalty_total', 0, 'penalty_E', 0, 'capacity_power_penalty', 0, 'min_utilization_penalty', 0);
%     end
%     return;
% end

% 储能充放电功率原始值 (24×2, MW)
ess_power_raw = reshape(X(151:198), 24, 2);

%--------------- 储能约束检查 ---------------
% 确保功率、容量、SOC 等满足物理约束，并返回修正后的功率曲线
[ess_power, penalty_E] = ESS_constraint_check_fixed(ess_power_raw, [ESS1_capacity, ESS2_capacity], [ESS1_power, ESS2_power]);

% 平移负荷时间
shift_time_vars = X(199:199+N_SL-1);

% 削减负荷量和开关
cut_amount = reshape(X(199+N_SL:199+N_SL+95), 24, N_CL);      % 24×4矩阵
cut_switch = reshape(X(199+N_SL+96:end), 24, N_CL);          % 24×4矩阵
cut_switch = sigmoid(cut_switch);  % 应用sigmoid函数

%% 修正3: 统一成本参数 - 使用全局ESS_params
% 储能投资成本 (元)
C_e = ESS_params.Ce;    % 储能容量成本 (元/kWh)
C_p = ESS_params.Cp;    % 储能功率成本 (元/kW)
C_i = (ESS1_capacity + ESS2_capacity) * C_e + (ESS1_power + ESS2_power) * C_p;

% 运维成本 (元)
C_maint = ESS_params.Cmaint; % 年运维成本系数 (元/kW/年)
i_r = ESS_params.ir;   % 通胀率
d_r = ESS_params.dr;   % 折现率 
Y = ESS_params.Y;      % 储能寿命 (年)

C_m = 0;
for y = 1:Y
    C_m = C_m + C_maint * (ESS1_power + ESS2_power) * (1 + i_r)^y / (1 + d_r)^y;
end

% 报废折旧成本 (元)
lambda = ESS_params.lambda; % 报废折旧率
C_d = lambda * (ESS1_capacity + ESS2_capacity) * C_e / (1 + d_r)^Y;

f1 = C_i + C_m + C_d + capacity_power_penalty;

%% 目标函数2: 系统运行成本
% 构建修改后的配电网模型
mpc_modified = cell(24,1);
for t = 1:24
    mpc_modified{t} = mpc_base;
    
    % 修改负荷
    mpc_modified{t}.bus(:,3) = ac(t,:)';
    mpc_modified{t}.bus(:,4) = acq(t,:)';
    mpc_modified{t}.bus(:,5) = dc(t,:)';
    
    % 风电注入 (MW) - 直接使用实际上网功率
    mpc_modified{t}.bus([5,24,29],3) = mpc_modified{t}.bus([5,24,29],3) - wind_actual(t,:)';
    mpc_modified{t}.bus([5,24,29],4) = mpc_modified{t}.bus([5,24,29],4) - wind_actual(t,:)' .* tan(acos(0.95));
    
    % 光伏注入 (MW) - 直接使用实际上网功率
    mpc_modified{t}.bus([10,17,22],5) = mpc_modified{t}.bus([10,17,22],5) - pv_actual(t,:)';
    
    % 修正6: 储能注入考虑功率因数 (储能充放电 - 转换为MW)
    if ESS1_node <= 33 && ESS1_node >= 2
        if mpc_modified{t}.bus(ESS1_node, 9) == 1 % DC节点
            mpc_modified{t}.bus(ESS1_node, 5) = mpc_modified{t}.bus(ESS1_node, 5) + ess_power(t,1);
        else % AC节点 - 添加无功功率
            mpc_modified{t}.bus(ESS1_node, 3) = mpc_modified{t}.bus(ESS1_node, 3) + ess_power(t,1);
            mpc_modified{t}.bus(ESS1_node, 4) = mpc_modified{t}.bus(ESS1_node, 4) + ess_power(t,1) * tan(acos(0.95)); % 功率因数0.95
        end
    end
    
    if ESS2_node <= 33 && ESS2_node >= 2
        if mpc_modified{t}.bus(ESS2_node, 9) == 1 % DC节点
            mpc_modified{t}.bus(ESS2_node, 5) = mpc_modified{t}.bus(ESS2_node, 5) + ess_power(t,2);
        else % AC节点 - 添加无功功率
            mpc_modified{t}.bus(ESS2_node, 3) = mpc_modified{t}.bus(ESS2_node, 3) + ess_power(t,2);
            mpc_modified{t}.bus(ESS2_node, 4) = mpc_modified{t}.bus(ESS2_node, 4) + ess_power(t,2) * tan(acos(0.95)); % 功率因数0.95
        end
    end
    
    % 平移负荷
    shift_time = time_SL(shift_time_vars, shift_load);
    for SL = 1:N_SL
        if t == shift_time(SL,1) || t == shift_time(SL,2)
            mpc_modified{t}.bus(shift_load(SL,1),3) = mpc_modified{t}.bus(shift_load(SL,1),3) + shift_load(SL,2);
            mpc_modified{t}.bus(shift_load(SL,1),4) = mpc_modified{t}.bus(shift_load(SL,1),4) + shift_load(SL,3);
        end
    end
    
    % 修正5: 削减负荷 - 修正cut_switch判断逻辑
    for i = 1:N_CL
        % 使用 >0.5 判断开关状态
        switch_state = cut_switch(t,i) > 0.5;
        reduction = cut_amount(t,i) * switch_state;
        mpc_modified{t}.bus(cut_load(i,1),3) = mpc_modified{t}.bus(cut_load(i,1),3) + cut_load(i,t+1) - reduction;
    end
end

% 计算系统运行成本
% 日度成本初始化（单位：元/天）
total_grid_cost = 0;     % 电网购电
total_curtail_cost = 0;  % 弃风弃光
total_loss_cost = 0;     % 网损
penalty_total = 0;       % 潮流/约束惩罚
convergence_flag = 0;

% 修正3: 统一弃风弃光成本 - 使用20元/kWh提高储能使用激励
curtail_cost_rate = 20.0; % 元/kWh (提高弃电罚金)

for t = 1:24
    % 潮流计算
    res = ac_dcpowerflow(mpc_modified{t});
    
    if res.gen == 100000 % 潮流不收敛
        convergence_flag = 1;
        break;
    end
    
    % 修正3: 电网交互成本 - 提高系数以鼓励储能使用
    grid_cost = price(t) * res.gen * 1000 * 1.5; % 提高至1.5倍
    total_grid_cost = total_grid_cost + grid_cost;
    
    % 修正3: 弃风弃光成本计算 - 统一使用20.0元/kWh
    wind_curtailed = 0;
    pv_curtailed = 0;
    for i = 1:3
        wind_available = Pwt(t,i) * wind_base(i);  % 风电预测出力 (MW)
        pv_available = Ppv(t,i) * pv_base(i);      % 光伏预测出力 (MW)
        
        % 弃风弃光量 = 预测出力 - 实际上网功率
        wind_curtailed = wind_curtailed + max(0, wind_available - wind_actual(t,i));
        pv_curtailed = pv_curtailed + max(0, pv_available - pv_actual(t,i));
    end
    % 弃风弃光惩罚成本：20.0元/kWh = 20000元/MWh
    curtail_cost = curtail_cost_rate * (wind_curtailed + pv_curtailed) * 1000; % 元
    total_curtail_cost = total_curtail_cost + curtail_cost;
    
    % 网损成本
    loss_cost = price(t) * res.loss * 1000 * 1.5; % 提高至1.5倍
    total_loss_cost = total_loss_cost + loss_cost;
    
    % 惩罚函数
    penalty_total = penalty_total + calculate_penalty(res, mpc_modified{t});
end

if convergence_flag == 1
    f1 = 1e8; f2 = 1e8; % 大惩罚值
    if nargout > 2
        cost_details = struct('C_i', 0, 'C_m', 0, 'C_d', 0, 'grid_cost', 0, ...
                             'curtail_cost', 0, 'curtail_cost_rate', 20.0, 'loss_cost', 0, 'C_SL', 0, 'C_CL', 0, ...
                             'penalty_total', 0, 'penalty_E', 0, 'capacity_power_penalty', 0, 'min_utilization_penalty', 0);
    end
    return;
end

% 柔性负荷补偿成本
% 平移负荷补偿成本
Tp = round(shift_time_vars) ~= shift_load(:,4)';
C_SL = 0.3 * 1000 * sum(Tp' .* (shift_load(:,2) .* shift_load(:,6)));

% 修正5: 削减负荷补偿成本 - 使用>0.5判断
C_CL = 0;
for i = 1:N_CL
    % 检查削减约束
    continuous_count = 0;
    total_reductions = sum(cut_switch(:,i) > 0.5); % 修正判断逻辑
    is_valid = true;
    
    for t = 1:24
        if cut_switch(t,i) > 0.5 % 修正判断逻辑
            continuous_count = continuous_count + 1;
            if continuous_count > 5
                is_valid = false;
                break;
            end
        else
            continuous_count = 0;
        end
    end
    
    if total_reductions > 15
        is_valid = false;
    end
    
    if is_valid
        % 使用修正后的开关状态
        switch_states = cut_switch(:,i) > 0.5;
        C_CL = C_CL + sum(cut_amount(:,i) .* switch_states) * 0.4 * 1000;
    else
        C_CL = C_CL + 10000; % 惩罚
    end
end

%=========================== 目标函数 2 ===========================
% 保持为单日 (24h) 运行成本，不进行生命周期放大
f2 = total_grid_cost + total_curtail_cost + total_loss_cost + C_SL + C_CL + penalty_total + penalty_E + penalty_min_utilization;

% 返回成本详细信息
if nargout > 2
    cost_details.C_i = C_i;                    % 储能投资成本
    cost_details.C_m = C_m;                    % 储能运维成本
    cost_details.C_d = C_d;                    % 储能报废成本
    cost_details.grid_cost = total_grid_cost;  % 电网交互成本
    cost_details.curtail_cost = total_curtail_cost; % 弃风弃光成本
    cost_details.curtail_cost_rate = curtail_cost_rate; % 弃风弃光成本费率
    cost_details.loss_cost = total_loss_cost;  % 网损成本
    cost_details.C_SL = C_SL;                  % 平移负荷补偿成本
    cost_details.C_CL = C_CL;                  % 削减负荷补偿成本
    cost_details.penalty_total = penalty_total; % 约束惩罚成本
    cost_details.penalty_E = penalty_E;        % 储能约束惩罚成本
    cost_details.capacity_power_penalty = capacity_power_penalty; % 功率容量配比惩罚
    cost_details.min_utilization_penalty = penalty_min_utilization;
end

end

%% 辅助函数
function penalty = calculate_penalty(res, mpc)
% 计算惩罚函数
P1 = 500; P2 = 1000; P4 = 200;
Sbase = 10;

% 平衡节点功率约束
Pgslack = res.gen;
if Pgslack > 40
    penalty_slack = 100 + P1 * ((Pgslack - 40) / Sbase)^2;
elseif Pgslack < 0
    penalty_slack = 100 + P1 * ((0 - Pgslack) / Sbase)^2;
else
    penalty_slack = 0;
end

% 电压约束
V = res.bus(:,7);
penalty_V = 0;
for i = 1:size(mpc.bus,1)
    if res.bus(i,2) == 1
        if V(i) > 1.1
            penalty_V = penalty_V + 100 + P2 * (V(i) - 1.1)^2;
        elseif V(i) < 0.9
            penalty_V = penalty_V + 100 + P2 * (0.9 - V(i))^2;
        end
    end
end

% 修正6: VSC约束 - 放宽限制以提高可再生送出能力
penalty_M = 0;
if ~isempty(res.S)
    for i = 1:length(res.S)
        if res.S(i) > 0.5 % 从0.4放宽到0.5
            penalty_M = penalty_M + 100 + P4 * (res.S(i) - 0.5)^2;
        end
    end
end

penalty = penalty_slack + penalty_V + penalty_M;
end

function [ess_power_corrected, penalty_E] = ESS_constraint_check_fixed(ess_power, capacities, max_powers)
% 修正1: 储能约束检查和修正 - 修正SOC更新方向和效率
ess_power_corrected = ess_power;
penalty_E = 0;

eta_ch = 0.9;   % 充电效率
eta_dis = 0.9;  % 放电效率
dt = 1;         % 时间步长 (小时)

for ess_idx = 1:2
    Ebat0 = 0.4 * capacities(ess_idx) / 1000; % 初始SOC 40%, 转换为MWh
    Emin = 0.16 * capacities(ess_idx) / 1000; % 最小SOC 16%
    Emax = 0.72 * capacities(ess_idx) / 1000; % 最大SOC 72%
    Pmax = max_powers(ess_idx) / 1000; % 转换为MW
    
    % 严格约束储能功率不能超过额定功率
    for t = 1:24
        if abs(ess_power_corrected(t, ess_idx)) > Pmax
            % 添加严重惩罚
            penalty_E = penalty_E + 10000 * (abs(ess_power_corrected(t, ess_idx)) - Pmax)^2;
            % 强制限制在额定功率范围内
            if ess_power_corrected(t, ess_idx) > Pmax
                ess_power_corrected(t, ess_idx) = Pmax;
            elseif ess_power_corrected(t, ess_idx) < -Pmax
                ess_power_corrected(t, ess_idx) = -Pmax;
            end
        end
    end
    
    Ebat = zeros(24,1);
    
    for t = 1:24
        % 修正1: SOC更新 - 正确的方向和效率处理
        if t == 1
            if ess_power_corrected(t, ess_idx) > 0 % 正功率 = 放电 (向系统注入)
                Ebat(t) = Ebat0 - ess_power_corrected(t, ess_idx) * dt / eta_dis;
            else % 负功率 = 充电 (从系统吸收)
                Ebat(t) = Ebat0 - ess_power_corrected(t, ess_idx) * dt * eta_ch; % P为负值
            end
        else
            if ess_power_corrected(t, ess_idx) > 0 % 放电
                Ebat(t) = Ebat(t-1) - ess_power_corrected(t, ess_idx) * dt / eta_dis;
            else % 充电
                Ebat(t) = Ebat(t-1) - ess_power_corrected(t, ess_idx) * dt * eta_ch; % P为负值
            end
        end
        
        % SOC边界约束
        if Ebat(t) > Emax
            penalty_E = penalty_E + 1000 * (Ebat(t) - Emax)^2;
            % 修正功率以满足SOC约束
            if t == 1
                if ess_power_corrected(t, ess_idx) > 0
                    ess_power_corrected(t, ess_idx) = (Ebat0 - Emax) * eta_dis / dt;
                else
                    ess_power_corrected(t, ess_idx) = (Ebat0 - Emax) / (eta_ch * dt);
                end
            else
                if ess_power_corrected(t, ess_idx) > 0
                    ess_power_corrected(t, ess_idx) = (Ebat(t-1) - Emax) * eta_dis / dt;
                else
                    ess_power_corrected(t, ess_idx) = (Ebat(t-1) - Emax) / (eta_ch * dt);
                end
            end
            Ebat(t) = Emax;
        elseif Ebat(t) < Emin
            penalty_E = penalty_E + 1000 * (Emin - Ebat(t))^2;
            % 修正功率以满足SOC约束
            if t == 1
                if ess_power_corrected(t, ess_idx) > 0
                    ess_power_corrected(t, ess_idx) = (Ebat0 - Emin) * eta_dis / dt;
                else
                    ess_power_corrected(t, ess_idx) = (Ebat0 - Emin) / (eta_ch * dt);
                end
            else
                if ess_power_corrected(t, ess_idx) > 0
                    ess_power_corrected(t, ess_idx) = (Ebat(t-1) - Emin) * eta_dis / dt;
                else
                    ess_power_corrected(t, ess_idx) = (Ebat(t-1) - Emin) / (eta_ch * dt);
                end
            end
            Ebat(t) = Emin;
        end
    end
    
    % 末时刻回归约束
    if abs(Ebat(24) - Ebat0) > 0.01
        penalty_E = penalty_E + abs(Ebat(24) - Ebat0) * 100000;
    end
end
end 