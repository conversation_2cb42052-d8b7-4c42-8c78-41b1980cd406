function plot_dispatch_results(best_solution)
% 绘制储能选址定容调度结果
global mpc_base Pwt Ppv price shift_load cut_load
global N_SL N_CL ac acq dc ESS_params

% 风光基准容量定义 (MW)
wind_base = [1.2, 1.0, 1.5]; % 各风电场额定容量
pv_base = [0.8, 1.0, 1.0];   % 各光伏电站额定容量

%% 解析最优解
ESS1_node = round(best_solution(1));
ESS2_node = round(best_solution(2));
ESS1_capacity = best_solution(3); % kWh
ESS2_capacity = best_solution(4); % kWh
ESS1_power = best_solution(5); % kW
ESS2_power = best_solution(6); % kW

% 风光削减量
idx = 7;
wind_cut = zeros(24, 3);
pv_cut = zeros(24, 3);
for t = 1:24
    wind_cut(t,:) = best_solution(idx:idx+2);
    pv_cut(t,:) = best_solution(idx+3:idx+5);
    idx = idx + 6;
end

% 储能充放电功率
ess_power = zeros(24, 2);
for t = 1:24
    ess_power(t,:) = best_solution(idx:idx+1);
    idx = idx + 2;
end

% 储能约束检查
[ess_power_corrected, ~] = ESS_constraint_check(ess_power, [ESS1_capacity, ESS2_capacity], [ESS1_power, ESS2_power]);

%% 计算各时段的电源出力和负荷
time_hours = 0:23;
total_load = zeros(24,1);
wind_output = zeros(24,1);
pv_output = zeros(24,1);
ess1_output = zeros(24,1);
ess2_output = zeros(24,1);
grid_power = zeros(24,1);

% 计算储能SOC
ess1_soc = zeros(24,1);
ess2_soc = zeros(24,1);
ess1_energy = 0.4 * ESS1_capacity / 1000; % 初始SOC 40%, MWh
ess2_energy = 0.4 * ESS2_capacity / 1000; % 初始SOC 40%, MWh

for t = 1:24
    % 总负荷计算
    total_load(t) = sum(ac(t,:)) + sum(dc(t,:));
    
    % 风光实际出力
    wind_available = sum(Pwt(t,:) .* wind_base);
    pv_available = sum(Ppv(t,:) .* pv_base);
    wind_output(t) = wind_available * (1 - mean(wind_cut(t,:)));
    pv_output(t) = pv_available * (1 - mean(pv_cut(t,:)));
    
    % 储能出力 (负值为充电，正值为放电)
    ess1_output(t) = -ess_power_corrected(t,1); % 注意符号：放电为正
    ess2_output(t) = -ess_power_corrected(t,2);
    
    % 计算储能SOC
    if t == 1
        if ess_power_corrected(t,1) > 0 % 充电
            ess1_energy = ess1_energy + ess_power_corrected(t,1) * 0.9;
        else % 放电
            ess1_energy = ess1_energy + ess_power_corrected(t,1) / 0.9;
        end
        
        if ess_power_corrected(t,2) > 0 % 充电
            ess2_energy = ess2_energy + ess_power_corrected(t,2) * 0.9;
        else % 放电
            ess2_energy = ess2_energy + ess_power_corrected(t,2) / 0.9;
        end
    else
        if ess_power_corrected(t,1) > 0 % 充电
            ess1_energy = ess1_energy + ess_power_corrected(t,1) * 0.9;
        else % 放电
            ess1_energy = ess1_energy + ess_power_corrected(t,1) / 0.9;
        end
        
        if ess_power_corrected(t,2) > 0 % 充电
            ess2_energy = ess2_energy + ess_power_corrected(t,2) * 0.9;
        else % 放电
            ess2_energy = ess2_energy + ess_power_corrected(t,2) / 0.9;
        end
    end
    
    ess1_soc(t) = ess1_energy / (ESS1_capacity / 1000) * 100;
    ess2_soc(t) = ess2_energy / (ESS2_capacity / 1000) * 100;
    
    % 电网交互功率 (正值为购电)
    grid_power(t) = total_load(t) - wind_output(t) - pv_output(t) - ess1_output(t) - ess2_output(t);
end

%% 绘制调度结果图 - 堆叠柱状图格式
figure(2);
clf;

% 准备堆叠数据
% 电源侧：风电、光伏、储能1放电、储能2放电、购电
% 负荷侧：总负荷、储能1充电、储能2充电

supply_data = zeros(24, 5);
demand_data = zeros(24, 3);

for t = 1:24
    % 电源侧数据
    supply_data(t,1) = max(0, wind_output(t));     % 风电
    supply_data(t,2) = max(0, pv_output(t));       % 光伏
    supply_data(t,3) = max(0, ess1_output(t));     % 储能1放电
    supply_data(t,4) = max(0, ess2_output(t));     % 储能2放电
    supply_data(t,5) = max(0, grid_power(t));      % 购电
    
    % 负荷侧数据
    demand_data(t,1) = total_load(t);              % 总负荷
    demand_data(t,2) = max(0, -ess1_output(t));    % 储能1充电
    demand_data(t,3) = max(0, -ess2_output(t));    % 储能2充电
end

% 绘制堆叠柱状图
bar_width = 0.8;
x_pos = 1:24;

% 绘制电源侧（正值）
h_supply = bar(x_pos, supply_data, bar_width, 'stacked');

% 设置颜色 - 按用户要求修改
colors = [
    1.0, 1.0, 0.0;  % 风电 - 黄色
    0.0, 1.0, 1.0;  % 光伏 - 青色
    0.6, 0.3, 0.0;  % 储能1放电 - 棕色
    1.0, 0.5, 0.0;  % 储能2放电 - 橙色
    0.0, 0.8, 0.0;  % 购电 - 绿色
];

for i = 1:5
    set(h_supply(i), 'FaceColor', colors(i,:));
end

hold on;

% 绘制负荷侧（负值）
demand_data_neg = -demand_data;
h_demand = bar(x_pos, demand_data_neg, bar_width, 'stacked');

% 设置负荷侧颜色 - 充电部分使用相同颜色但稍暗
demand_colors = [
    0.8, 0.8, 0.8;  % 总负荷 - 灰色
    0.4, 0.2, 0.0;  % 储能1充电 - 深棕色
    0.8, 0.3, 0.0;  % 储能2充电 - 深橙色
];

for i = 1:3
    set(h_demand(i), 'FaceColor', demand_colors(i,:));
end

% 绘制负荷曲线
h_load = plot(x_pos, total_load, 'b-', 'LineWidth', 2, 'Marker', 'o', 'MarkerSize', 4);

% 图表设置
xlabel('时间 (h)');
ylabel('功率 (MW)');
title('交直流柔性配电网储能优化调度结果');
grid on;

% 设置x轴标签
xticks(1:2:24);
xticklabels(arrayfun(@(x) sprintf('%02d:00', x-1), 1:2:24, 'UniformOutput', false));

% 修正图例 - 只显示主要组件，更新颜色对应
legend([h_supply(1), h_supply(2), h_supply(3), h_supply(4), h_supply(5), h_load], ...
       {'风电', '光伏', sprintf('ESS1放电 (节点%d)', ESS1_node), sprintf('ESS2放电 (节点%d)', ESS2_node), '购电', '负荷'}, ...
       'Location', 'eastoutside');

% 添加储能信息文本框
info_text = sprintf(['储能配置信息:\n' ...
    'ESS1: 节点%d, %.1f kWh, %.1f kW\n' ...
    'ESS2: 节点%d, %.1f kWh, %.1f kW'], ...
    ESS1_node, ESS1_capacity, ESS1_power, ...
    ESS2_node, ESS2_capacity, ESS2_power);

text(0.02, 0.98, info_text, 'Units', 'normalized', ...
    'VerticalAlignment', 'top', 'BackgroundColor', 'white', ...
    'EdgeColor', 'black', 'FontSize', 9);

%% 绘制储能SOC变化曲线
figure(3);
clf;

h1 = plot(time_hours, ess1_soc, 'b-o', 'LineWidth', 2, 'MarkerSize', 6);
hold on;
h2 = plot(time_hours, ess2_soc, 'r-s', 'LineWidth', 2, 'MarkerSize', 6);

% SOC约束线
h3 = yline(72, 'g--', 'LineWidth', 1.5);
h4 = yline(16, 'g--', 'LineWidth', 1.5);

xlabel('时间 (h)');
ylabel('SOC (%)');
title('储能系统SOC变化曲线');
legend([h1, h2, h4, h3], ...
       {sprintf('储能1 (节点%d)', ESS1_node), sprintf('储能2 (节点%d)', ESS2_node), '最小SOC', '最大SOC'}, ...
       'Location', 'best');
grid on;
xlim([0 23]);
ylim([10 80]);

%% 输出调度结果统计
fprintf('\n=== 调度结果分析 ===\n');
fprintf('储能1: 节点%d, 容量%.1f kWh, 功率%.1f kW\n', ESS1_node, ESS1_capacity, ESS1_power);
fprintf('储能2: 节点%d, 容量%.1f kWh, 功率%.1f kW\n', ESS2_node, ESS2_capacity, ESS2_power);

% 计算各项统计数据
total_wind = sum(wind_output);
total_pv = sum(pv_output);
total_grid = sum(max(0, grid_power));

% 储能统计 - 删除利用率计算
ess1_charge_total = sum(max(0, ess_power_corrected(:,1))); % 总充电量 MWh
ess1_discharge_total = sum(max(0, -ess_power_corrected(:,1))); % 总放电量 MWh
ess2_charge_total = sum(max(0, ess_power_corrected(:,2)));
ess2_discharge_total = sum(max(0, -ess_power_corrected(:,2)));

fprintf('总风电出力: %.2f MWh\n', total_wind);
fprintf('总光伏出力: %.2f MWh\n', total_pv);
fprintf('总购电量: %.2f MWh\n', total_grid);
fprintf('储能1总充电量: %.2f MWh\n', ess1_charge_total);
fprintf('储能1总放电量: %.2f MWh\n', ess1_discharge_total);
fprintf('储能2总充电量: %.2f MWh\n', ess2_charge_total);
fprintf('储能2总放电量: %.2f MWh\n', ess2_discharge_total);

end

%% 辅助函数
function [ess_power_corrected, penalty_E] = ESS_constraint_check(ess_power, capacities, max_powers)
% 储能约束检查和修正
ess_power_corrected = ess_power;
penalty_E = 0;

for ess_idx = 1:2
    Ebat0 = 0.4 * capacities(ess_idx) / 1000; % 初始SOC 40%, 转换为MWh
    Emin = 0.16 * capacities(ess_idx) / 1000; % 最小SOC 16%
    Emax = 0.72 * capacities(ess_idx) / 1000; % 最大SOC 72%
    Pmax = max_powers(ess_idx) / 1000; % 转换为MW
    
    % 严格约束储能功率
    for t = 1:24
        if abs(ess_power_corrected(t, ess_idx)) > Pmax
            if ess_power_corrected(t, ess_idx) > Pmax
                ess_power_corrected(t, ess_idx) = Pmax;
            elseif ess_power_corrected(t, ess_idx) < -Pmax
                ess_power_corrected(t, ess_idx) = -Pmax;
            end
        end
    end
    
    Ebat = zeros(24,1);
    
    for t = 1:24
        % SOC约束
        if t == 1
            if ess_power_corrected(t, ess_idx) > 0
                Ebat(t) = Ebat0 + ess_power_corrected(t, ess_idx) * 0.9;
            else
                Ebat(t) = Ebat0 + ess_power_corrected(t, ess_idx) / 0.9;
            end
        else
            if ess_power_corrected(t, ess_idx) > 0
                Ebat(t) = Ebat(t-1) + ess_power_corrected(t, ess_idx) * 0.9;
            else
                Ebat(t) = Ebat(t-1) + ess_power_corrected(t, ess_idx) / 0.9;
            end
        end
        
        % SOC边界约束
        if Ebat(t) > Emax
            if t == 1
                ess_power_corrected(t, ess_idx) = (Emax - Ebat0) / 0.9;
            else
                ess_power_corrected(t, ess_idx) = (Emax - Ebat(t-1)) / 0.9;
            end
            Ebat(t) = Emax;
        elseif Ebat(t) < Emin
            if t == 1
                ess_power_corrected(t, ess_idx) = (Emin - Ebat0) * 0.9;
            else
                ess_power_corrected(t, ess_idx) = (Emin - Ebat(t-1)) * 0.9;
            end
            Ebat(t) = Emin;
        end
    end
    
    % 末时刻回归约束
    if abs(Ebat(24) - Ebat0) > 0.01
        penalty_E = penalty_E + abs(Ebat(24) - Ebat0) * 100000;
    end
end
end 