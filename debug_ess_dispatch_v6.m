%% 调试储能调度逻辑 - 详细分析每个时刻的调度决策
% 找出为什么储能在负荷高峰期没有放电

clear; clc; close all;

% 全局变量声明
global mpc_base Pwt Ppv price shift_load cut_load Load_ac Load_dc
global N_SL N_CL Nwt Npv ac acq dc ESS_params

% 储能参数设置
ESS_params.Ce = 700;
ESS_params.Cp = 400;
ESS_params.Cmaint = 180;
ESS_params.ir = 0.015;
ESS_params.dr = 0.09;
ESS_params.Y = 10;
ESS_params.lambda = 0.08;
ESS_params.Cde = 60;
ESS_params.E_max = 8000;
ESS_params.P_max = 2500;

% 加载数据
load_data_v6;

% 风光基准容量
scale = 1.5;
wind_base = [2.16, 1.8, 2.7] * scale;
pv_base = [2.0, 2.5, 2.5] * scale;

fprintf('=== 储能调度逻辑调试 ===\n\n');

%% 构造测试解
n_var = 2 + 2 + 2 + N_SL + N_CL;
solution_test = zeros(1, n_var);
solution_test(1:2) = [30, 5];      % 储能安装节点
solution_test(3:4) = [2500, 2000]; % 储能容量
solution_test(5:6) = [3.5, 3.2];   % 功率配比

% 平移负荷时间设置
idx = 7;
if N_SL > 0
    solution_test(idx:idx+N_SL-1) = shift_load(:,4)';
    idx = idx + N_SL;
end

% 削减负荷开关设置
if N_CL > 0
    solution_test(idx:idx+N_CL-1) = 0;
end

%% 模拟调度逻辑
ESS1_capacity = solution_test(3);
ESS2_capacity = solution_test(4);
ESS1_power = ESS1_capacity / solution_test(5);
ESS2_power = ESS2_capacity / solution_test(6);

ESS1_max_power = ESS1_power / 1000;  % kW -> MW
ESS2_max_power = ESS2_power / 1000;  % kW -> MW
total_ess_max_power = ESS1_max_power + ESS2_max_power;

fprintf('储能配置:\n');
fprintf('ESS1: %.1f kWh, %.1f kW (%.2f MW)\n', ESS1_capacity, ESS1_power, ESS1_max_power);
fprintf('ESS2: %.1f kWh, %.1f kW (%.2f MW)\n', ESS2_capacity, ESS2_power, ESS2_max_power);
fprintf('总功率: %.2f MW\n\n', total_ess_max_power);

%% 逐时刻分析调度决策
fprintf('时刻  负荷   风电   光伏   总RE   盈余   电价  时段特征        调度决策      储能功率\n');
fprintf('----  ----   ----   ----   ----   ----   ----  ----------      --------      --------\n');

ess_power_debug = zeros(24, 2);

for t = 1:24
    % 计算基础数据
    total_load = Load_ac(t) + Load_dc(t);
    wind_forecast = sum(Pwt(t,:) .* wind_base);
    pv_forecast = sum(Ppv(t,:) .* pv_base);
    total_renewable = wind_forecast + pv_forecast;
    renewable_surplus = total_renewable - total_load;
    
    % 时段特征判断
    is_peak_hour = (t >= 18 && t <= 21);
    is_valley_hour = (t >= 1 && t <= 6) || (t >= 23 && t <= 24);
    is_high_price = price(t) >= 0.6;
    is_low_price = price(t) <= 0.4;
    is_charge_opportunity = is_valley_hour || is_low_price || (renewable_surplus > 0.5);
    is_discharge_opportunity = is_peak_hour || is_high_price || (renewable_surplus < -1.0);
    
    % 时段特征描述
    features = {};
    if is_peak_hour, features{end+1} = '高峰'; end
    if is_valley_hour, features{end+1} = '低谷'; end
    if is_high_price, features{end+1} = '高价'; end
    if is_low_price, features{end+1} = '低价'; end
    if renewable_surplus > 0.5, features{end+1} = '大盈余'; end
    if renewable_surplus < -1.0, features{end+1} = '大缺口'; end
    feature_str = strjoin(features, ',');
    if isempty(feature_str), feature_str = '平时'; end
    
    % 调度决策
    ess_power_debug(t,1) = 0;
    ess_power_debug(t,2) = 0;
    decision = '待机';
    
    if total_ess_max_power > 0
        if is_discharge_opportunity
            % 放电策略
            if is_peak_hour && renewable_surplus < -3.0
                discharge_total = total_ess_max_power * 0.8;
                decision = '最大放电';
            elseif is_high_price
                discharge_total = total_ess_max_power * 0.6;
                decision = '大功率放电';
            elseif renewable_surplus < -1.0
                deficit = -renewable_surplus;
                discharge_total = min(deficit * 0.7, total_ess_max_power * 0.5);
                decision = '适度放电';
            else
                discharge_total = total_ess_max_power * 0.3;
                decision = '小功率放电';
            end
            
            if total_ess_max_power > 1e-6
                ratio1 = ESS1_max_power / total_ess_max_power;
                discharge1 = discharge_total * ratio1;
                discharge2 = discharge_total - discharge1;
                
                ess_power_debug(t,1) = discharge1;
                ess_power_debug(t,2) = discharge2;
            end
            
        elseif is_charge_opportunity
            % 充电策略
            if renewable_surplus > 0.5
                charge_total = min(renewable_surplus * 0.9, total_ess_max_power * 0.7);
                decision = '消纳充电';
            elseif is_valley_hour && is_low_price
                charge_total = total_ess_max_power * 0.6;
                decision = '大功率充电';
            elseif is_valley_hour || is_low_price
                charge_total = total_ess_max_power * 0.4;
                decision = '适度充电';
            else
                charge_total = total_ess_max_power * 0.2;
                decision = '小功率充电';
            end
            
            if total_ess_max_power > 1e-6
                ratio1 = ESS1_max_power / total_ess_max_power;
                charge1 = charge_total * ratio1;
                charge2 = charge_total - charge1;
                
                ess_power_debug(t,1) = -charge1;
                ess_power_debug(t,2) = -charge2;
            end
        end
        
        % 功率限制
        ess_power_debug(t,1) = max(-ESS1_max_power, min(ESS1_max_power, ess_power_debug(t,1)));
        ess_power_debug(t,2) = max(-ESS2_max_power, min(ESS2_max_power, ess_power_debug(t,2)));
    end
    
    total_ess_power = sum(ess_power_debug(t, :));
    
    fprintf('%02d:00 %5.2f  %5.2f  %5.2f  %5.2f  %5.2f  %4.2f  %-12s    %-12s  %6.3f MW\n', ...
        t-1, total_load, wind_forecast, pv_forecast, total_renewable, renewable_surplus, ...
        price(t), feature_str, decision, total_ess_power);
end

%% 分析问题
fprintf('\n=== 问题分析 ===\n');

% 检查负荷高峰期
peak_hours = 18:21;
fprintf('负荷高峰期 (17-20:00) 储能行为:\n');
for i = 1:length(peak_hours)
    t = peak_hours(i);
    total_ess_power = sum(ess_power_debug(t, :));
    renewable_surplus = sum(Pwt(t,:) .* wind_base) + sum(Ppv(t,:) .* pv_base) - (Load_ac(t) + Load_dc(t));
    
    fprintf('  %02d:00: 储能功率=%.3f MW, 盈余=%.2f MW, 电价=%.2f元/kWh\n', ...
        t-1, total_ess_power, renewable_surplus, price(t));
    
    if total_ess_power <= 0.01
        fprintf('    ⚠️  问题：负荷高峰期储能未放电！\n');
        
        % 分析原因
        is_peak_hour = (t >= 18 && t <= 21);
        is_high_price = price(t) >= 0.6;
        is_discharge_opportunity = is_peak_hour || is_high_price || (renewable_surplus < -1.0);
        
        fprintf('    调试信息: is_peak_hour=%d, is_high_price=%d, is_discharge_opportunity=%d\n', ...
            is_peak_hour, is_high_price, is_discharge_opportunity);
    end
end

% 检查充电时段
charge_hours = find(sum(ess_power_debug, 2) < -0.01);
fprintf('\n充电时段: ');
for i = 1:length(charge_hours)
    fprintf('%02d:00 ', charge_hours(i)-1);
end
fprintf('\n');

% 检查放电时段
discharge_hours = find(sum(ess_power_debug, 2) > 0.01);
fprintf('放电时段: ');
for i = 1:length(discharge_hours)
    fprintf('%02d:00 ', discharge_hours(i)-1);
end
fprintf('\n');

%% 储能利用率分析
total_charge = sum(max(-sum(ess_power_debug, 2), 0));
total_discharge = sum(max(sum(ess_power_debug, 2), 0));
total_capacity = (ESS1_capacity + ESS2_capacity) / 1000; % MWh

fprintf('\n=== 储能利用率分析 ===\n');
fprintf('总充电量: %.2f MWh\n', total_charge);
fprintf('总放电量: %.2f MWh\n', total_discharge);
fprintf('储能总容量: %.2f MWh\n', total_capacity);
fprintf('充电利用率: %.1f%%\n', total_charge / total_capacity * 100);
fprintf('放电利用率: %.1f%%\n', total_discharge / total_capacity * 100);

%% 建议优化方案
fprintf('\n=== 优化建议 ===\n');
fprintf('1. 负荷高峰期 (17-20:00) 应强制放电，即使电价不高\n');
fprintf('2. 可再生能源缺口大于1MW时应立即放电\n');
fprintf('3. 低谷时段 (00-06:00) 应积极充电\n');
fprintf('4. 考虑SOC约束，避免过度充放电\n');

fprintf('\n=== 调试完成 ===\n');
