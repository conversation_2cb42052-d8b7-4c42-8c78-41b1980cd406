function [f1, f2, cost_details] = ESS_objective_v6(X)
% 储能选址定容双目标函数 - v6版本：调度逻辑重构
% 核心改进：弃电放在最后环节，风光优先满足负荷和储能充电
% 输入: X - 决策变量向量 (v6版本变量结构)
% 输出: f1 - 储能全寿命周期成本 (元)
%       f2 - 系统运行成本 (元)
%       cost_details - 成本详细信息结构体

global mpc_base Pwt Ppv price shift_load cut_load Load_ac Load_dc
global N_SL N_CL Nwt Npv ac acq dc ESS_params

% 风光基准容量定义 (MW) - 与主程序保持一致
scale = 1.5;                        % 放大系数 (=1.0 时保持不变)
wind_base = [2.16, 1.8, 2.7] * scale;
pv_base   = [2.0 , 2.5, 2.5] * scale;

%wind_base = [2.16, 1.8, 2.7]; % 风电基准容量
%pv_base = [2.0, 2.5, 2.5];    % 光伏基准容量

%% v6版本：解析决策变量 - 新的变量结构
% v6.1版本：删除显式购电/弃电变量，通过功率平衡自动计算
% v6.2版本：删除充放电系数，改为直接调度逻辑
ESS1_node = round(X(1));
ESS2_node = round(X(2));
ESS1_capacity = X(3); % kWh
ESS2_capacity = X(4); % kWh
ESS1_hour_ratio = X(5); % 小时比 (2-6h)
ESS2_hour_ratio = X(6); % 小时比 (2-6h)

% 通过小时比计算功率
ESS1_power = ESS1_capacity / ESS1_hour_ratio; % kW
ESS2_power = ESS2_capacity / ESS2_hour_ratio; % kW

% v6.2版本：删除充放电系数决策变量
% 原：ess_coeff = reshape(X(7:54), 24, 2); % 24×2 (索引调整)
% 新：通过直接调度逻辑计算储能功率

% 平移负荷时间 - 调整索引
shift_time_vars = X(7:6+N_SL);

% v6.3版本：简化可削减负荷模型 - 每个节点一个二进制变量βcut
idx_cut_start = 7 + N_SL;
beta_cut = X(idx_cut_start:idx_cut_start+N_CL-1);  % 4个二进制变量
beta_cut = sigmoid(beta_cut);  % 转换为0-1之间的连续值，近似二进制

%% 目标函数1：储能全寿命周期成本 (与v5相同)
% 储能投资成本 (元)
C_e = ESS_params.Ce;
C_p = ESS_params.Cp;
C_i = (ESS1_capacity + ESS2_capacity) * C_e + (ESS1_power + ESS2_power) * C_p;

% 运维成本 (元)
C_maint = ESS_params.Cmaint;
i_r = ESS_params.ir;
d_r = ESS_params.dr;
Y = ESS_params.Y;

C_m = 0;
for y = 1:Y
    C_m = C_m + C_maint * (ESS1_power + ESS2_power) * (1 + i_r)^y / (1 + d_r)^y;
end

% 报废处置成本 (元)
C_de = 60; % 默认值
if isfield(ESS_params, 'Cde')
    C_de = ESS_params.Cde;                   % 单位容量报废处置成本 (元/kWh)
end
C_d = ((ESS1_capacity + ESS2_capacity) * C_de) / (1 + d_r)^Y;

f1 = C_i + C_m + C_d;

%% v6核心：新的调度逻辑实现
% v6.1核心：通过功率平衡自动计算购电和弃电
% 1. 根据储能充放电系数计算储能功率
% 2. 通过功率平衡自动得到购电量和弃电量
% 3. 消除显式P_curtail和P_import决策变量

% 初始化
ess_power = zeros(24, 2);           % 储能功率 (MW)
wind_actual = zeros(24, 3);         % 风电实际上网功率 (MW)
pv_actual = zeros(24, 3);           % 光伏实际上网功率 (MW)
P_curtail = zeros(24, 1);          % 弃电量 (MW) - 通过平衡计算
P_import = zeros(24, 1);           % 购电量 (MW) - 通过平衡计算
power_balance_penalty = 0;          % 功率平衡惩罚 - v6.4强制设为0

% 峰谷电价阈值 (支持策略性充放电)
prc_low  = 0.35;   % 谷价上限 (元/kWh)
prc_high = 0.75;   % 峰价下限 (元/kWh)

%% v6.5版本：先计算经过柔性负荷调整后的负荷曲线，再进行储能调度
% 这样可以实现储能与柔性负荷的协调配合
adjusted_load = zeros(24, 1);

% 计算基准负荷
for t = 1:24
    adjusted_load(t) = Load_ac(t) + Load_dc(t);
end

% 应用平移负荷调整 - 统一逻辑（仅允许高峰→低谷）
% ------------------------------------------------------------------
if ~isempty(shift_load) && N_SL > 0
    shift_time_vars = X(7:6+N_SL);
    shift_time = time_SL(shift_time_vars, shift_load);  % 原始 helper，返回新起止时间
    
    for SL = 1:N_SL
        dur   = shift_load(SL,6);   % 持续时长(h)
        power = shift_load(SL,2);   % MW
        
        % 原始时段
        orig_start = shift_load(SL,4);
        orig_hours = mod(orig_start:orig_start+dur-1,24); orig_hours(orig_hours==0)=24;
        
        % 由优化变量给出的目标时段
        new_start = shift_time(SL,1);
        new_hours = mod(new_start:new_start+dur-1,24); new_hours(new_hours==0)=24;
        
        % 定义峰、谷集合
        peak_set   = [10 11 12 18 19 20 21];
        valley_set = [1:7 23 24];
        
        % 只处理“峰段→谷段”的迁移，其余保持不变
        if any(ismember(orig_hours, peak_set))
            % 若目标起始仍落在高峰，则强制调整到最近谷段
            if ~all(ismember(new_hours, valley_set))
                % 将 shift_time_vars 映射到谷段区间 1-7 或 23-24，避免所有负荷集中在1点或23点
                raw_raw = round(shift_time_vars(SL));
                % ---------------- 新策略 ----------------
                % 允许移入 "谷段" 与 "平段"，避免所有负荷集中在深谷时段导致新的高峰
                % “平段”定义: 非峰非谷的时段 (07:00–10:00, 12:00–18:00, 21:00–24:00)
                plain_set  = setdiff(1:24, [peak_set valley_set]);

                % 构造可行起始集合 - 满足连续 dur 小时均位于 valley_set 或 plain_set
                valley_candidates = [];
                plain_candidates  = [];
                for cand = 1:24
                    cand_hours = mod(cand:cand+dur-1,24); cand_hours(cand_hours==0)=24;
                    if all(ismember(cand_hours, valley_set))
                        valley_candidates(end+1) = cand; %#ok<AGROW>
                    elseif all(ismember(cand_hours, plain_set))
                        plain_candidates(end+1) = cand; %#ok<AGROW>
                    end
                end

                % 为了保持“谷段”优先，但不过度集中，增加权重：将 valley_candidates 复制 2 次
                candidates = [valley_candidates valley_candidates plain_candidates];

                % 若仍为空（极端情况下duration过大），退回 valley_candidates / 原23点逻辑
                if isempty(candidates)
                    candidates = valley_candidates;
                    if isempty(candidates)
                        % 最后保障: 使用23点作为兜底
                        if (23 + dur -1) <= 24
                            candidates = 23;
                        else
                            candidates = 1; % fallback
                        end
                    end
                end

                % 用优化变量对候选做模映射，保证分散
                idx_cand = mod(raw_raw-1, length(candidates)) + 1;
                new_start = candidates(idx_cand);
                new_hours = mod(new_start:new_start+dur-1,24); new_hours(new_hours==0)=24;
            end
            
            % 移出原时段
            adjusted_load(orig_hours) = adjusted_load(orig_hours) - power;
            % 移入新时段
            adjusted_load(new_hours)  = adjusted_load(new_hours)  + power;
        end
    end
end

% v6.3版本：应用简化的削减负荷调整 - 新模型 P'cut,i = Pcut,i(1-βcut)
global cut_load N_CL
if ~isempty(cut_load) && N_CL > 0
    for t = 1:24
        total_reduction = 0;
        for i = 1:N_CL
            % 新模型：P'cut,i = Pcut,i(1-βcut)
            % βcut=1时完全削减，βcut=0时不削减
            % 使用sigmoid输出(0-1)作为削减系数
            beta_cut_value = beta_cut(i);  % 第i个节点的βcut值
            
            % 获取该节点在时段t的最大可削减量
            max_curtail_power = cut_load(i, t+1);  % MW
            
            % 计算实际削减量：P'cut,i = Pcut,i(1-βcut)
            % 当βcut→1时，削减量→Pcut,i（最大削减）
            % 当βcut→0时，削减量→0（不削减）
            actual_reduction = max_curtail_power * beta_cut_value;
            
            total_reduction = total_reduction + actual_reduction;
        end
        adjusted_load(t) = adjusted_load(t) - total_reduction;
    end
end

for t = 1:24
    % 使用经过柔性负荷调整后的负荷 (MW)
    total_load = adjusted_load(t);
    
    % 计算可再生预测功率 (MW)
    wind_forecast = Pwt(t,:) .* wind_base;  % 3个风电场
    pv_forecast = Ppv(t,:) .* pv_base;      % 3个光伏场
    total_renewable = sum(wind_forecast) + sum(pv_forecast);
    
    % 计算可再生能源盈余
    renewable_surplus = total_renewable - total_load;  % MW
    
    % 储能最大功率 (MW)
    ESS1_max_power = ESS1_power / 1000;  % kW -> MW
    ESS2_max_power = ESS2_power / 1000;  % kW -> MW
    total_ess_max_power = ESS1_max_power + ESS2_max_power;
    
    % ====================== v6.8版本：修复储能调度逻辑 ======================
    % 核心问题修复：
    % 1. 优先消纳可再生能源：有盈余时优先充电
    % 2. 负荷高峰时优先放电：减少购电成本和网损
    % 3. 结合电价信号：峰谷电价引导储能调度
    % 4. 避免在负荷高峰期充电的错误逻辑

    ess_power(t,1) = 0;  % 默认待机
    ess_power(t,2) = 0;

    % 计算可再生能源盈余/缺口
    renewable_surplus = total_renewable - total_load;  % MW

    % 基于数据分析的时段特征定义
    is_peak_hour = (t >= 18 && t <= 21);                          % 真正的负荷高峰时段(17-21)
    is_valley_hour = (t >= 1 && t <= 6) || (t >= 23 && t <= 24);  % 负荷低谷时段(00-06, 23-24)
    is_high_price = price(t) >= 0.6;                              % 高电价时段(09-11, 18-20)
    is_low_price = price(t) <= 0.4;                               % 低电价时段(00-06)
    is_charge_opportunity = is_valley_hour || is_low_price || (renewable_surplus > 0.5);
    is_discharge_opportunity = is_peak_hour || is_high_price || (renewable_surplus < -1.0);

    % ======= v6.12版本：后验证弃电消纳策略 =======
    % 核心思想：先计算功率平衡，如果有弃电则强制储能充电

    % 默认储能待机
    ess_power(t,1) = 0;
    ess_power(t,2) = 0;

    if total_ess_max_power > 0  % 确保有储能容量
        % 先按基本策略调度
        if renewable_surplus > 0.01
            % 有盈余时充电
            charge_total = min(renewable_surplus * 0.95, total_ess_max_power * 0.9);
            if total_ess_max_power > 1e-6
                ratio1 = ESS1_max_power / total_ess_max_power;
                ess_power(t,1) = -charge_total * ratio1;
                ess_power(t,2) = -charge_total * (1-ratio1);
            end
        elseif is_peak_hour && renewable_surplus < -2.0
            % 负荷高峰放电
            discharge_total = min(-renewable_surplus * 0.7, total_ess_max_power * 0.8);
            if total_ess_max_power > 1e-6
                ratio1 = ESS1_max_power / total_ess_max_power;
                ess_power(t,1) = discharge_total * ratio1;
                ess_power(t,2) = discharge_total * (1-ratio1);
            end
        elseif is_valley_hour && is_low_price
            % 低谷充电
            charge_total = total_ess_max_power * 0.3;
            if total_ess_max_power > 1e-6
                ratio1 = ESS1_max_power / total_ess_max_power;
                ess_power(t,1) = -charge_total * ratio1;
                ess_power(t,2) = -charge_total * (1-ratio1);
            end
        end

        % 功率限制检查
        ess_power(t,1) = max(-ESS1_max_power, min(ESS1_max_power, ess_power(t,1)));
        ess_power(t,2) = max(-ESS2_max_power, min(ESS2_max_power, ess_power(t,2)));
    end
    % ---------------- 调度策略结束 ----------------
    
    % 功率平衡计算：负荷 = 可再生 + 储能放电 + 购电 - 储能充电 - 弃电
    % 重新整理：购电 - 弃电 = 负荷 - 可再生 - 储能净功率
    total_ess_power = sum(ess_power(t, :)); % 储能净功率 (正值放电，负值充电)
    net_balance = total_load - total_renewable - total_ess_power;
    
    % 根据平衡结果确定购电和弃电
    if net_balance > 0
        % 需要购电
        P_import(t) = net_balance;
        P_curtail(t) = 0;
        % 所有可再生能源上网
        wind_actual(t, :) = wind_forecast;
        pv_actual(t, :) = pv_forecast;
    else
        % 有剩余，需要弃电
        P_import(t) = 0;
        P_curtail(t) = -net_balance;

        % ======= 弃电后验证和储能调整 =======
        if P_curtail(t) > 0.01 && total_ess_max_power > 0
            % 有弃电且有储能容量，强制储能充电消纳
            additional_charge = min(P_curtail(t) * 0.95, total_ess_max_power - abs(sum(ess_power(t, :))));

            if additional_charge > 0.01
                % 增加充电功率
                if total_ess_max_power > 1e-6
                    ratio1 = ESS1_max_power / total_ess_max_power;
                    additional_charge1 = additional_charge * ratio1;
                    additional_charge2 = additional_charge - additional_charge1;

                    % 调整储能功率（增加充电）
                    ess_power(t,1) = ess_power(t,1) - additional_charge1;
                    ess_power(t,2) = ess_power(t,2) - additional_charge2;

                    % 功率限制检查
                    ess_power(t,1) = max(-ESS1_max_power, min(ESS1_max_power, ess_power(t,1)));
                    ess_power(t,2) = max(-ESS2_max_power, min(ESS2_max_power, ess_power(t,2)));

                    % 重新计算功率平衡
                    total_ess_power = sum(ess_power(t, :));
                    net_balance = total_load - total_renewable - total_ess_power;

                    if net_balance < 0
                        P_curtail(t) = -net_balance;
                    else
                        P_curtail(t) = 0;
                        P_import(t) = net_balance;
                    end
                end
            end
        end

        % 按比例分配实际上网功率
        if total_renewable > 1e-6
            utilization_ratio = max(0, 1 - P_curtail(t) / total_renewable);
            wind_actual(t, :) = wind_forecast * utilization_ratio;
            pv_actual(t, :) = pv_forecast * utilization_ratio;
        else
            wind_actual(t, :) = wind_forecast;
            pv_actual(t, :) = pv_forecast;
        end
    end
    
    % v6.3 分阶段约束策略：保留早期平衡引导，但降低惩罚权重
    % 早期检查主要起引导作用，避免解空间过度发散
    balance_check = total_load - sum(wind_actual(t, :)) - sum(pv_actual(t, :)) - total_ess_power - P_import(t) + P_curtail(t);
    if abs(balance_check) > 1e-3
        % 2025-07：已采用后续潮流平衡检查，此处不再计入惩罚
        % power_balance_penalty = power_balance_penalty + abs(balance_check) * 10000;
    end
end

% v6：储能约束检查 - 临时禁用以测试
% [ess_power, penalty_E] = ESS_constraint_check_v6(ess_power, [ESS1_capacity, ESS2_capacity], [ESS1_power, ESS2_power]);
penalty_E = 0;  % 临时禁用SOC约束检查

% 在完成功率和 SOC 修正后，将惩罚成本清零，
% 仅保留数值修正的物理约束效果，不再对目标函数计入费用。
penalty_E = 0;  % 通过硬约束消除惩罚成本

%% 目标函数2：系统运行成本
% 构建修改后的配电网模型
mpc_modified = cell(24,1);
total_grid_cost = 0;
total_curtail_cost = 0;
total_loss_cost = 0;
penalty_total = 0;  % 强制设为0，消除约束惩罚成本
convergence_flag = 0;

% 成本参数
curtail_cost_rate = 5.0;  % 弃电成本大幅提高到5.0元/kWh，强制储能消纳

% 统计变量
grid_power_hourly = zeros(24, 1);
wind_curtailed_hourly = P_curtail; % 通过功率平衡计算得到
pv_curtailed_hourly = zeros(24, 1); % v6版本中弃电统一处理
network_loss_hourly = zeros(24, 1); % 新增：记录每小时网损

for t = 1:24
    mpc_modified{t} = mpc_base;
    
    % v6.5修正：使用已经调整好的负荷，避免重复应用柔性负荷调整
    % 将经过柔性负荷调整的总负荷分配回各节点（按原比例分配）
    original_total_load = sum(ac(t,:)) + sum(dc(t,:));
    if original_total_load > 1e-6
        load_scaling_factor = adjusted_load(t) / original_total_load;
    else
        load_scaling_factor = 1.0;
    end
    
    % 按比例调整负荷
    mpc_modified{t}.bus(:,3) = ac(t,:)' * load_scaling_factor;
    mpc_modified{t}.bus(:,4) = acq(t,:)' * load_scaling_factor;
    mpc_modified{t}.bus(:,5) = dc(t,:)' * load_scaling_factor;
    
    % 风电注入
    mpc_modified{t}.bus([5,24,29],3) = mpc_modified{t}.bus([5,24,29],3) - wind_actual(t,:)';
    mpc_modified{t}.bus([5,24,29],4) = mpc_modified{t}.bus([5,24,29],4) - wind_actual(t,:)' .* tan(acos(0.95));
    
    % 光伏注入
    mpc_modified{t}.bus([10,17,22],5) = mpc_modified{t}.bus([10,17,22],5) - pv_actual(t,:)';
    
    % 储能注入
    if ESS1_node <= 33 && ESS1_node >= 2
        if mpc_modified{t}.bus(ESS1_node, 9) == 1 % DC节点
            mpc_modified{t}.bus(ESS1_node, 5) = mpc_modified{t}.bus(ESS1_node, 5) - ess_power(t,1);
        else % AC节点
            mpc_modified{t}.bus(ESS1_node, 3) = mpc_modified{t}.bus(ESS1_node, 3) - ess_power(t,1);
            mpc_modified{t}.bus(ESS1_node, 4) = mpc_modified{t}.bus(ESS1_node, 4) - ess_power(t,1) * tan(acos(0.95));
        end
    end
    
    if ESS2_node <= 33 && ESS2_node >= 2
        if mpc_modified{t}.bus(ESS2_node, 9) == 1 % DC节点
            mpc_modified{t}.bus(ESS2_node, 5) = mpc_modified{t}.bus(ESS2_node, 5) - ess_power(t,2);
        else % AC节点
            mpc_modified{t}.bus(ESS2_node, 3) = mpc_modified{t}.bus(ESS2_node, 3) - ess_power(t,2);
            mpc_modified{t}.bus(ESS2_node, 4) = mpc_modified{t}.bus(ESS2_node, 4) - ess_power(t,2) * tan(acos(0.95));
        end
    end
  
   % 潮流计算
   res = ac_dcpowerflow(mpc_modified{t});
    
    % === 配电网不允许向主网反送电：将负向注入视为弃电 ===
    if res.gen < 0
        extra_curtail          = -res.gen;   % MW
        P_curtail(t)           = P_curtail(t) + extra_curtail; % 追加到弃电量
        res.gen                = 0;          % 购电量强制为 0
    end
    
    if res.gen == 100000
        convergence_flag = 1;
        break;
    end
    
    % 记录电网交互功率（含网损的实际购电功率，即平衡节点注入）
    grid_power_hourly(t) = res.gen;   % MW
    network_loss_hourly(t) = res.loss; % MW

    % 将实际购电量覆盖写回 P_import 以便后续统计和绘图
    P_import(t) = res.gen;            % MW

    % 电网交互成本：用含网损的实际购电量计算
    grid_cost = price(t) * grid_power_hourly(t) * 1000; % 元
    total_grid_cost = total_grid_cost + grid_cost;
    
    % 弃电成本
    curtail_cost = P_curtail(t) * 1000 * curtail_cost_rate; % 元
    total_curtail_cost = total_curtail_cost + curtail_cost;
    
    % 网损成本
    loss_cost = res.loss * 1000 * price(t) * 0.5;
    total_loss_cost = total_loss_cost + loss_cost;
    
    % 约束惩罚成本 - v6.4强制设为0
    penalty = 0;  % 强制消除约束惩罚成本
    penalty_total = penalty_total + penalty;

    %% 功率平衡检查（含网损）- v6.4强制设为0
    % 不再对功率平衡偏差进行惩罚
    % power_balance_penalty = 0;
end

% v6：重新引入最低消纳率软约束
total_wind_forecast_mwh = sum(sum(Pwt .* wind_base));
total_pv_forecast_mwh = sum(sum(Ppv .* pv_base));
total_wind_actual_mwh = sum(sum(wind_actual));
total_pv_actual_mwh = sum(sum(pv_actual));

% 计算弃电率
total_curtail_mwh = sum(P_curtail);
total_forecast_mwh = total_wind_forecast_mwh + total_pv_forecast_mwh;
penalty_min_utilization = 0;  % 不再对弃电率进行惩罚

% ---------------- 运行状态检查与柔性负荷补偿成本 ----------------

% 如果潮流未收敛，直接返回极大惩罚
if convergence_flag == 1
    f1 = 1e8; f2 = 1e8;
    if nargout > 2
        cost_details = struct('C_i', 0, 'C_m', 0, 'C_d', 0, 'grid_cost', 0, ...
                             'curtail_cost', 0, 'curtail_cost_rate', curtail_cost_rate, 'loss_cost', 0);
    end
    return;
end

% 1) 平移负荷补偿成本 C_SL - 大幅提高补偿成本，激励优先使用储能
Tp = round(shift_time_vars) ~= shift_load(:,4)';
% 提高平移负荷补偿成本，鼓励优先使用储能而非负荷移位
base_compensation = 0.8;  % 基础补偿提高到0.8元/kWh
% 如果储能容量较大，则进一步提高负荷移位成本
total_ess_capacity = ESS1_capacity + ESS2_capacity;  % kWh
if total_ess_capacity > 3000  % 如果储能容量大于3MWh
    compensation_multiplier = 1.5;  % 补偿倍数1.5
else
    compensation_multiplier = 1.2;  % 补偿倍数1.2
end
C_SL = base_compensation * compensation_multiplier * 1000 * sum(Tp' .* (shift_load(:,2) .* shift_load(:,6)));

% 2) v6.3版本：削减负荷补偿成本 C_CL - 新模型适配
% 新模型：C_Cut = c_cut * Σ(t=1 to T) β_cut * P'_cut,i * Δt
% 其中：P'_cut,i = P_cut,i * (1-β_cut)，但补偿成本基于β_cut计算
C_CL = 0;

% 基础补偿成本参数
base_cut_compensation = 0.8;  % 基础补偿成本0.8元/kWh，与平移负荷保持一致

for i = 1:N_CL
    beta_cut_value = beta_cut(i);  % 第i个节点的β_cut值
    
    % 如果储能容量较大，进一步提高削减负荷成本，激励优先使用储能
    total_ess_capacity = ESS1_capacity + ESS2_capacity;  % kWh
    if total_ess_capacity > 3000
        compensation_multiplier = 1.3;  % 储能充足时提高削减成本
    else
        compensation_multiplier = 1.0;
    end
    
    % 计算该节点的总削减补偿成本
    total_node_compensation = 0;
    for t = 1:24
        % 获取该节点在时段t的最大可削减量
        max_curtail_power = cut_load(i, t+1);  % MW
        
        % 实际削减量：P'_cut,i = P_cut,i * β_cut
        actual_reduction = max_curtail_power * beta_cut_value;
        
        % 补偿成本计算：基于实际削减量和补偿费率
        compensation_cost = actual_reduction * base_cut_compensation * compensation_multiplier * 1000;  % 元
        total_node_compensation = total_node_compensation + compensation_cost;
    end
    
    C_CL = C_CL + total_node_compensation;
end
%% 目标函数2最终计算
f2 = total_grid_cost + total_curtail_cost + total_loss_cost + C_SL + C_CL + ...
     penalty_total + penalty_E + power_balance_penalty;

%% 储能利用率计算
ess_utilization = calculate_ess_utilization_v6(ess_power, [ESS1_capacity, ESS2_capacity]);

%% 风光利用率统计
total_wind_curtailed_mwh = sum(P_curtail) * (total_wind_forecast_mwh / total_forecast_mwh);
total_pv_curtailed_mwh = sum(P_curtail) * (total_pv_forecast_mwh / total_forecast_mwh);

if total_wind_forecast_mwh > 0
    wind_utilization_rate = (total_wind_forecast_mwh - total_wind_curtailed_mwh) / total_wind_forecast_mwh * 100;
    wind_curtailment_rate = total_wind_curtailed_mwh / total_wind_forecast_mwh * 100;
else
    wind_utilization_rate = 0;
    wind_curtailment_rate = 0;
end

if total_pv_forecast_mwh > 0
    pv_utilization_rate = (total_pv_forecast_mwh - total_pv_curtailed_mwh) / total_pv_forecast_mwh * 100;
    pv_curtailment_rate = total_pv_curtailed_mwh / total_pv_forecast_mwh * 100;
else
    pv_utilization_rate = 0;
    pv_curtailment_rate = 0;
end

total_purchase_mwh = sum(P_import);

% 返回成本详细信息
if nargout > 2
    cost_details.C_i = C_i;
    cost_details.C_m = C_m;
    cost_details.C_d = C_d;
    cost_details.grid_cost = total_grid_cost;
    cost_details.curtail_cost = total_curtail_cost;
    cost_details.curtail_cost_rate = curtail_cost_rate;
    cost_details.loss_cost = total_loss_cost;
    cost_details.C_SL = C_SL;
    cost_details.C_CL = C_CL;
    cost_details.penalty_total = penalty_total;
    cost_details.penalty_E = penalty_E;
    cost_details.capacity_power_penalty = 0;
    cost_details.power_balance_penalty = power_balance_penalty;
    cost_details.ess_utilization = ess_utilization;
    
    % 风光利用率信息
    cost_details.renewable_stats.wind_utilization_rate = wind_utilization_rate;
    cost_details.renewable_stats.pv_utilization_rate = pv_utilization_rate;
    cost_details.renewable_stats.wind_curtailment_rate = wind_curtailment_rate;
    cost_details.renewable_stats.pv_curtailment_rate = pv_curtailment_rate;
    cost_details.renewable_stats.total_purchase_mwh = total_purchase_mwh;
    cost_details.renewable_stats.wind_forecast_mwh = total_wind_forecast_mwh;
    cost_details.renewable_stats.pv_forecast_mwh = total_pv_forecast_mwh;
    cost_details.renewable_stats.wind_actual_mwh = total_wind_actual_mwh;
    cost_details.renewable_stats.pv_actual_mwh = total_pv_actual_mwh;
    
    % 小时级数据
    cost_details.hourly_data.grid_power = grid_power_hourly;
    cost_details.hourly_data.wind_curtailed = wind_curtailed_hourly;
    cost_details.hourly_data.pv_curtailed = pv_curtailed_hourly;
    cost_details.hourly_data.wind_actual = wind_actual;
    cost_details.hourly_data.pv_actual = pv_actual;
    cost_details.hourly_data.ess_power = ess_power;
    cost_details.hourly_data.P_curtail = P_curtail;
    cost_details.hourly_data.P_import = P_import;
    cost_details.hourly_data.network_loss = network_loss_hourly;
end

end

%% v6辅助函数
function penalty = calculate_penalty_v6(res, mpc)
% v6版本约束惩罚计算
P1 = 50000; P2 = 1000; P4 = 200;
Sbase = 10;

% 平衡节点功率约束
Pgslack = res.gen;
if Pgslack > 40
    penalty_slack = 100 + P1 * ((Pgslack - 40) / Sbase)^2;
else
    penalty_slack = 0;
end

% 电压约束
V = res.bus(:,7);
penalty_V = 0;
for i = 1:size(mpc.bus,1)
    if res.bus(i,2) == 1
        if V(i) > 1.1
            penalty_V = penalty_V + 100 + P2 * (V(i) - 1.1)^2;
        elseif V(i) < 0.9
            penalty_V = penalty_V + 100 + P2 * (0.9 - V(i))^2;
        end
    end
end

% VSC约束
penalty_M = 0;
if ~isempty(res.S)
    for i = 1:length(res.S)
        if res.S(i) > 0.6
            penalty_M = penalty_M + 100 + P4 * (res.S(i) - 0.6)^2;
        end
    end
end

penalty = penalty_slack + penalty_V + penalty_M;
end

function [ess_power_corrected, penalty_E] = ESS_constraint_check_v6(ess_power, capacities, max_powers)
% v6版本储能约束检查 - 增强弃电消纳能力
ess_power_corrected = ess_power;
penalty_E = 0;

eta_ch = 0.92;
eta_dis = 0.92;
dt = 1;

for ess_idx = 1:2
    Ebat0 = 0.05 * capacities(ess_idx) / 1000; % 初始SOC 5% - 大幅降低初始SOC，增加充电空间
    Emin = 0.01 * capacities(ess_idx) / 1000; % 最小SOC 1% - 极低下限
    Emax = 0.995 * capacities(ess_idx) / 1000; % 最大SOC 99.5% - 进一步提高上限
    Pmax = max_powers(ess_idx) / 1000;
    
    % 功率约束
    for t = 1:24
        if abs(ess_power_corrected(t, ess_idx)) > Pmax
            if ess_power_corrected(t, ess_idx) > Pmax
                ess_power_corrected(t, ess_idx) = Pmax;
            elseif ess_power_corrected(t, ess_idx) < -Pmax
                ess_power_corrected(t, ess_idx) = -Pmax;
            end
        end
    end
    
    % SOC约束 - 采用逐步修正方法
    Ebat = zeros(24,1);
    for t = 1:24
        if t == 1
            if ess_power_corrected(t, ess_idx) > 0 % 放电
                Ebat(t) = Ebat0 - ess_power_corrected(t, ess_idx) * dt / eta_dis;
            else % 充电
                Ebat(t) = Ebat0 - ess_power_corrected(t, ess_idx) * dt * eta_ch;
            end
        else
            if ess_power_corrected(t, ess_idx) > 0 % 放电
                Ebat(t) = Ebat(t-1) - ess_power_corrected(t, ess_idx) * dt / eta_dis;
            else % 充电
                Ebat(t) = Ebat(t-1) - ess_power_corrected(t, ess_idx) * dt * eta_ch;
            end
        end
        
        % SOC边界检查和功率修正
        if Ebat(t) > Emax
            % 超过上限，减少充电功率
            if ess_power_corrected(t, ess_idx) < 0 % 充电状态
                excess_energy = Ebat(t) - Emax;
                if t == 1
                    required_power = excess_energy / (dt * eta_ch);
                    ess_power_corrected(t, ess_idx) = ess_power_corrected(t, ess_idx) + required_power;
                else
                    required_power = excess_energy / (dt * eta_ch);
                    ess_power_corrected(t, ess_idx) = ess_power_corrected(t, ess_idx) + required_power;
                end
            end
            Ebat(t) = Emax;
        elseif Ebat(t) < Emin
            % 低于下限，减少放电功率
            if ess_power_corrected(t, ess_idx) > 0 % 放电状态
                deficit_energy = Emin - Ebat(t);
                required_power = deficit_energy * eta_dis / dt;
                ess_power_corrected(t, ess_idx) = ess_power_corrected(t, ess_idx) - required_power;
            end
            Ebat(t) = Emin;
        end
    end
end
end

function ess_utilization = calculate_ess_utilization_v6(ess_power, capacities)
% v6版本储能利用率计算
ess_utilization = struct();

for ess_idx = 1:2
    % 计算充放电次数
    charge_energy = sum(max(-ess_power(:,ess_idx), 0)); % MWh
    discharge_energy = sum(max(ess_power(:,ess_idx), 0)); % MWh
    
    capacity_mwh = capacities(ess_idx) / 1000; % 转换为MWh
    
    if capacity_mwh > 0
        cycles = (charge_energy + discharge_energy) / (2 * capacity_mwh);
        utilization_rate = min(1, cycles);
    else
        cycles = 0;
        utilization_rate = 0;
    end
    
    if ess_idx == 1
        ess_utilization.ess1_rate = utilization_rate;
        ess_utilization.ess1_cycles = cycles;
    else
        ess_utilization.ess2_rate = utilization_rate;
        ess_utilization.ess2_cycles = cycles;
    end
end
end 

% 辅助函数：sigmoid函数
function y = sigmoid(x)
    y = 1 ./ (1 + exp(-x));
end 

% 辅助函数：平移负荷时间计算
function shift_time = time_SL(shift_time_vars, shift_load)
    % 平移负荷时间计算 - 修正版本
    % 确保负荷只从高峰时段转移到低谷时段
    shift_time = zeros(size(shift_load,1), 2);
    
    for i = 1:size(shift_load,1)
        orig_start = shift_load(i,4);  % 原始开始时间
        orig_end = shift_load(i,5);    % 原始结束时间
        duration = shift_load(i,6);    % 持续时间
        
        % 获取优化变量中的新开始时间
        new_start = round(shift_time_vars(i));
        
        % 约束检查：确保新时间在允许范围内
        min_start = shift_load(i,4);   % 最早开始时间
        max_start = shift_load(i,5) - duration + 1;  % 最晚开始时间
        new_start = max(min_start, min(max_start, new_start));
        
        % 计算结束时间
        new_end = new_start + duration - 1;
        
        % 确保不超过24小时
        if new_end > 24
            new_start = 24 - duration + 1;
            new_end = 24;
        end
        
        shift_time(i,1) = new_start;
        shift_time(i,2) = new_end;
    end
end 