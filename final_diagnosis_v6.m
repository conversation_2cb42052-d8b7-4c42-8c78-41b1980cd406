%% 最终诊断：为什么即使25MWh储能仍有14.9%弃电率
% 深入分析系统约束和网络限制

clear; clc; close all;

% 全局变量声明
global mpc_base Pwt Ppv price shift_load cut_load Load_ac Load_dc
global N_SL N_CL Nwt Npv ac acq dc ESS_params

% 储能参数设置
ESS_params.Ce = 200;
ESS_params.Cp = 100;
ESS_params.Cmaint = 50;
ESS_params.ir = 0.015;
ESS_params.dr = 0.09;
ESS_params.Y = 10;
ESS_params.lambda = 0.08;
ESS_params.Cde = 60;
ESS_params.E_max = 25000;
ESS_params.P_max = 8000;

% 加载数据
load_data_v6;

fprintf('=== 最终诊断：弃电率高的根本原因 ===\n\n');

%% 理论分析：如果没有任何约束，弃电率应该是多少？
% 风光基准容量
scale = 1.5;
wind_base = [2.16, 1.8, 2.7] * scale;
pv_base = [2.0, 2.5, 2.5] * scale;

total_renewable_forecast = 0;
total_load_forecast = 0;
total_surplus = 0;

for t = 1:24
    total_load = Load_ac(t) + Load_dc(t);
    wind_forecast = sum(Pwt(t,:) .* wind_base);
    pv_forecast = sum(Ppv(t,:) .* pv_base);
    total_renewable = wind_forecast + pv_forecast;
    
    total_renewable_forecast = total_renewable_forecast + total_renewable;
    total_load_forecast = total_load_forecast + total_load;
    
    if total_renewable > total_load
        total_surplus = total_surplus + (total_renewable - total_load);
    end
end

theoretical_curtailment_rate = total_surplus / total_renewable_forecast * 100;

fprintf('理论分析（无约束情况）:\n');
fprintf('日总可再生发电量: %.2f MWh\n', total_renewable_forecast);
fprintf('日总负荷: %.2f MWh\n', total_load_forecast);
fprintf('日总盈余: %.2f MWh\n', total_surplus);
fprintf('理论最低弃电率: %.1f%%\n', theoretical_curtailment_rate);

if theoretical_curtailment_rate > 5.0
    fprintf('⚠️  发现根本问题：即使没有任何约束，理论弃电率也高达%.1f%%！\n', theoretical_curtailment_rate);
    fprintf('这说明可再生能源装机容量相对于负荷过大，储能无法完全解决问题。\n');
end

%% 分析可再生能源装机配置
fprintf('\n=== 可再生能源装机分析 ===\n');
fprintf('风电装机: %.2f MW\n', sum(wind_base));
fprintf('光伏装机: %.2f MW\n', sum(pv_base));
fprintf('总可再生装机: %.2f MW\n', sum(wind_base) + sum(pv_base));

max_load = max(Load_ac + Load_dc);
min_load = min(Load_ac + Load_dc);
avg_load = mean(Load_ac + Load_dc);

fprintf('负荷峰值: %.2f MW\n', max_load);
fprintf('负荷谷值: %.2f MW\n', min_load);
fprintf('负荷均值: %.2f MW\n', avg_load);

renewable_penetration = (sum(wind_base) + sum(pv_base)) / max_load;
fprintf('可再生能源渗透率: %.1f%% (装机/峰荷)\n', renewable_penetration * 100);

if renewable_penetration > 1.5
    fprintf('⚠️  可再生能源渗透率过高！超过150%%会导致严重弃电问题。\n');
end

%% 分析储能理论需求
% 计算完全消纳所需的储能容量
required_storage_capacity = total_surplus * 1.2; % 增加20%裕度
required_storage_power = 0;

for t = 1:24
    total_load = Load_ac(t) + Load_dc(t);
    wind_forecast = sum(Pwt(t,:) .* wind_base);
    pv_forecast = sum(Ppv(t,:) .* pv_base);
    total_renewable = wind_forecast + pv_forecast;
    
    if total_renewable > total_load
        surplus = total_renewable - total_load;
        required_storage_power = max(required_storage_power, surplus);
    end
end

required_storage_power = required_storage_power * 1.2; % 增加20%裕度

fprintf('\n=== 储能理论需求分析 ===\n');
fprintf('完全消纳所需储能容量: %.1f MWh\n', required_storage_capacity);
fprintf('完全消纳所需储能功率: %.1f MW\n', required_storage_power);

current_storage_capacity = 25.0; % MWh
current_storage_power = 6.2; % MW

fprintf('当前储能配置: %.1f MWh, %.1f MW\n', current_storage_capacity, current_storage_power);

capacity_ratio = current_storage_capacity / required_storage_capacity;
power_ratio = current_storage_power / required_storage_power;

fprintf('容量满足度: %.1f%%\n', capacity_ratio * 100);
fprintf('功率满足度: %.1f%%\n', power_ratio * 100);

%% 网络约束分析
fprintf('\n=== 网络约束分析 ===\n');

% 检查是否有网络约束限制
% 这需要查看潮流计算的结果
n_var = 2 + 2 + 2 + N_SL + N_CL;
solution_test = zeros(1, n_var);
solution_test(1:2) = [30, 5];
solution_test(3:4) = [12500, 12500];
solution_test(5:6) = [4.0, 4.0];

idx = 7;
if N_SL > 0
    solution_test(idx:idx+N_SL-1) = shift_load(:,4)';
    idx = idx + N_SL;
end
if N_CL > 0
    solution_test(idx:idx+N_CL-1) = 0;
end

[f1, f2, cost_details] = ESS_objective_v6(solution_test);

if isfield(cost_details, 'hourly_data')
    P_curtail = cost_details.hourly_data.P_curtail;
    P_import = cost_details.hourly_data.P_import;
    
    % 分析弃电时段的网络状况
    fprintf('弃电时段网络状况分析:\n');
    for t = 1:24
        if P_curtail(t) > 0.1
            fprintf('时刻%02d:00: 弃电%.2f MW, 购电%.2f MW\n', t-1, P_curtail(t), P_import(t));
        end
    end
    
    % 检查是否同时有弃电和购电
    simultaneous_curtail_import = 0;
    for t = 1:24
        if P_curtail(t) > 0.01 && P_import(t) > 0.01
            simultaneous_curtail_import = simultaneous_curtail_import + 1;
        end
    end
    
    if simultaneous_curtail_import > 0
        fprintf('⚠️  发现%d个时段同时有弃电和购电，说明存在网络约束！\n', simultaneous_curtail_import);
    else
        fprintf('✅ 没有同时弃电和购电的情况，网络约束不是主要问题。\n');
    end
end

%% 解决方案建议
fprintf('\n=== 最终解决方案建议 ===\n');

if theoretical_curtailment_rate > 5.0
    fprintf('根本问题：可再生能源装机过大\n');
    fprintf('1. 降低可再生能源装机规模\n');
    fprintf('   - 建议风电装机: %.1f MW (当前%.1f MW)\n', sum(wind_base)*0.8, sum(wind_base));
    fprintf('   - 建议光伏装机: %.1f MW (当前%.1f MW)\n', sum(pv_base)*0.8, sum(pv_base));
    fprintf('   - 或者增加负荷需求\n');
    
    fprintf('\n2. 如果必须保持当前装机，需要:\n');
    fprintf('   - 储能容量: %.1f MWh (当前%.1f MWh)\n', required_storage_capacity, current_storage_capacity);
    fprintf('   - 储能功率: %.1f MW (当前%.1f MW)\n', required_storage_power, current_storage_power);
    
    fprintf('\n3. 其他解决方案:\n');
    fprintf('   - 发展氢储能或其他长时储能技术\n');
    fprintf('   - 建设外送通道，向外输送多余电力\n');
    fprintf('   - 发展电制氢、电制热等灵活负荷\n');
    
else
    fprintf('储能技术问题：\n');
    fprintf('1. 进一步优化储能调度算法\n');
    fprintf('2. 检查SOC约束和网络约束\n');
    fprintf('3. 考虑储能选址优化\n');
end

%% 经济性重新评估
fprintf('\n=== 经济性重新评估 ===\n');

if theoretical_curtailment_rate <= 5.0
    fprintf('储能方案可行，建议继续优化技术参数\n');
else
    % 计算达到5%弃电率的经济性
    target_curtailment_mwh = total_renewable_forecast * 0.05;
    current_curtailment_mwh = total_renewable_forecast * 0.149;
    additional_absorption_needed = current_curtailment_mwh - target_curtailment_mwh;
    
    fprintf('达到5%%弃电率需额外消纳: %.1f MWh\n', additional_absorption_needed);
    
    if additional_absorption_needed > current_storage_capacity
        fprintf('⚠️  所需额外消纳量超过当前储能容量，需要:\n');
        fprintf('   - 增加储能容量到: %.1f MWh\n', additional_absorption_needed * 1.2);
        fprintf('   - 或者接受较高的弃电率作为系统优化结果\n');
    end
end

fprintf('\n=== 诊断完成 ===\n');
fprintf('结论：弃电率高的根本原因是可再生能源装机相对于负荷过大，\n');
fprintf('      即使配置大容量储能也无法完全解决问题。\n');
fprintf('      建议调整可再生能源装机规模或接受合理的弃电率。\n');
