%% 测试修正后的调度结果绘图函数
% 这个脚本用于验证 plot_dispatch_results_v6.m 的修正结果

clear; clc; close all;

% 加载必要的全局变量
global mpc_base Pwt Ppv price shift_load cut_load Load_ac Load_dc
global N_SL N_CL ac dc

% 运行数据加载
try
    load_data_v6;
    fprintf('数据加载完成\n');
catch
    fprintf('警告：无法加载load_data_v6，使用模拟数据\n');
    
    % 创建模拟数据
    shift_load = [
        6	0.08	0.04	9	15	2
        9	0.07	0.035	10	16	2
        11	0.09	0.045	11	15	1
        12	0.08	0.04	19	24	1
    ];
    
    cut_load = [
        25 0.021*ones(1,24)
        33 0.017*ones(1,24)
        11 0.021*ones(1,24)
        27 0.014*ones(1,24)
    ];
    
    N_SL = size(shift_load, 1);
    N_CL = size(cut_load, 1);
    
    ac = rand(24, 33) * 0.1;  % 模拟AC负荷
    dc = rand(24, 33) * 0.05; % 模拟DC负荷
end

% 创建模拟的决策变量向量
X_test = zeros(1, 6 + N_SL + 48*N_CL);

% 储能配置
X_test(1:6) = [15, 25, 3000, 2500, 3, 4];  % 节点15+25, 容量3+2.5MWh, 配比3+4h

% 平移负荷时间（保持原始时间）
X_test(7:6+N_SL) = shift_load(:,4)';

% 削减负荷量和开关（随机生成）
idx = 7 + N_SL;
for t = 1:24
    for i = 1:N_CL
        X_test(idx) = rand() * cut_load(i, t+1);  % 削减量
        idx = idx + 1;
    end
end

for t = 1:24
    for i = 1:N_CL
        X_test(idx) = randn() * 2;  % 开关变量（sigmoid前）
        idx = idx + 1;
    end
end

% 创建模拟的成本详情
cost_details.hourly_data.wind_actual = rand(24, 3) * 2;
cost_details.hourly_data.pv_actual = rand(24, 3) * 1.5;
cost_details.hourly_data.ess_power = [
    randn(24, 1) * 1.5,  % ESS1功率
    randn(24, 1) * 1.2   % ESS2功率
];
cost_details.hourly_data.P_curtail = rand(24, 1) * 0.5;
cost_details.hourly_data.P_import = rand(24, 1) * 3 + 2;

% 模拟ESS参数
ESS_params.Ce = 700;
ESS_params.Cp = 400;

fprintf('=== 测试调度结果绘图 ===\n');
fprintf('储能1: 节点%d, 容量%.1fMWh\n', X_test(1), X_test(3)/1000);
fprintf('储能2: 节点%d, 容量%.1fMWh\n', X_test(2), X_test(4)/1000);
fprintf('平移负荷数量: %d\n', N_SL);
fprintf('削减负荷数量: %d\n', N_CL);

% 调用绘图函数
try
    plot_dispatch_results_v6(X_test, cost_details, ESS_params);
    fprintf('\n绘图完成！应该生成以下图表：\n');
    fprintf('1. 调度结果图：堆叠柱形图，光伏在底部，储能在顶部\n');
    fprintf('2. 储能充放电功率图：ESS1和ESS2的功率曲线\n');
    fprintf('3. VSC1功率图：VSC1的有功和无功功率曲线（双Y轴）\n');
    fprintf('4. VSC2功率图：VSC2的有功和无功功率曲线（双Y轴）\n');
    fprintf('5. VSC3功率图：VSC3的有功和无功功率曲线（双Y轴）\n');
    fprintf('6. 负荷曲线对比图：原始负荷vs调整后负荷\n');
catch ME
    fprintf('绘图出错: %s\n', ME.message);
    fprintf('错误位置: %s, 行号: %d\n', ME.stack(1).name, ME.stack(1).line);
end 