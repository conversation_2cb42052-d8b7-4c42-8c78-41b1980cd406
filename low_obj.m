function [F1,F2,F3,penalty,convergence]=low_obj(X,mpc1,Pwt,Ppv,price,shift_load,cut_load)


global  Nwt Npv Nm Nmcita Ness Nsvg  N_SL  N_CL ac acq dc
%平移负荷持续时间
shift_time=time_SL(X( 1,Nwt + Npv +Nm+Nmcita+ Ness +Nsvg +1:Nwt + Npv +Nm+Nmcita+ Ness +Nsvg +N_SL),shift_load);

%转化为0-1矩阵
X(:,Nwt + Npv +Nm+Nmcita+ Ness +Nsvg +N_SL+N_CL+1:Nwt + Npv +Nm+Nmcita+ Ness +Nsvg +N_SL+N_CL*2)=sigmoid(X(:,Nwt + Npv +Nm+Nmcita+ Ness +Nsvg +N_SL+N_CL+1:Nwt + Npv +Nm+Nmcita+ Ness +Nsvg +N_SL+N_CL*2));

Sbase=10;
nb=32;
% ng=1;
penalty=zeros(1,24);
F1=zeros(1,24);
F2=zeros(1,24);
Nv=0;
parfor t=1:24
%% Modification of the MATPOWER case struct with solution vector
%新能源发电量
mpc1(t).bus([5,24,29],3)=mpc1(t).bus([5,24,29],3)-Pwt(t,1: 3)';%WT
mpc1(t).bus([5,24,29],4)=mpc1(t).bus([5,24,29],4)-Pwt(t,1: 3)'.*tan(acos(0.95));
mpc1(t).bus([10,17,22],5)=mpc1(t).bus([10,17,22],5)-Ppv(t,1: 3)';%PV
%VSC  M
% branch(M,6)=X(t, Nwt + Npv+1: Nwt + Npv + Nm);
% branch(M,7)=X(t, Nwt + Npv + Nm +1:Nwt + Npv + Nm + Nmcita);
%Stored energy
mpc1(t).bus(:,3)=ac(t,:)';
mpc1(t).bus(:,4)=acq(t,:)';
mpc1(t).bus(:,5)=dc(t,:)';
%储能
mpc1(t).bus(17,5)=mpc1(t).bus(17,5)+X(t,Nwt + Npv + Nm + Nmcita + 1);
mpc1(t).bus(22,5)=mpc1(t).bus(22,5)+X(t,Nwt + Npv + Nm + Nmcita + 2);
% mpc1(t).bus(29,5)=mpc1(t).bus(29,5)+X(t,Nwt + Npv + Nm + Nmcita + 3);
% mpc1(t).bus(33,5)=mpc1(t).bus(33,5)+X(t, Nwt + Npv + Nm + Nmcita + Ness);
%SVG补偿
% bus(7,4)=bus(7,4)-X(t, Nwt + Npv + Nm + Nmcita + Ness + Nsvg-2);
% bus(12,4)=bus(12,4)-X(t, Nwt + Npv + Nm + Nmcita + Ness + Nsvg-1);
% bus(33,4)=bus(33,4)-X(t, Nwt + Npv + Nm + Nmcita + Ness + Nsvg);

%柔性负荷(考虑柔性负荷的调度）
%平移负荷
for SL=1:N_SL
    if t==shift_time(SL,1)
        mpc1(t).bus(shift_load(SL,1),3)=mpc1(t).bus(shift_load(SL,1),3)+shift_load(SL,2);
        mpc1(t).bus(shift_load(SL,1),4)=mpc1(t).bus(shift_load(SL,1),4)+shift_load(SL,3);
    elseif t==shift_time(SL,2)
        mpc1(t).bus(shift_load(SL,1),3)=mpc1(t).bus(shift_load(SL,1),3)+shift_load(SL,2);
        mpc1(t).bus(shift_load(SL,1),4)=mpc1(t).bus(shift_load(SL,1),4)+shift_load(SL,3);
    end
end
%削减负荷
mpc1(t).bus(cut_load(:,1),3)=mpc1(t).bus(cut_load(:,1),3)+cut_load(:,t+1)-X(t,Nwt + Npv + Ness  +N_SL+1:Nwt + Npv + Ness +N_SL+N_CL)'.*X(t,Nwt + Npv + Ness +N_SL+N_CL+1:Nwt + Npv +Nm+Nmcita+ Ness +Nsvg +N_SL+N_CL*2)';

end
convergence=0;
%%成本计算
for t=1:24
[res]=ac_dcpowerflow(mpc1(t));
Pgslack=[];
Pgslack=res.gen;
if Pgslack==100000
    convergence=1;
    break;
end
V=res.bus(:,7);
% Qgen=res.gen(:,3);
S=res.S;
%% Cost Function


loss(t)= res.loss;


%% PENALTY FACTORS惩罚因子
P1=500;
P2=1000;
P3=7000;
P4=200;

%% PENALTY FUNCTIONS

%% Penalty function for slack bus power violation
penalty_slack=[];
Pgslackmin=0;
Pgslackmax=40;

if Pgslack>Pgslackmax
         penalty_slack=100+P1*(((Pgslack-Pgslackmax)/Sbase)^2);
        
    elseif Pgslack<Pgslackmin
         penalty_slack=100+P1*(((Pgslackmin-Pgslack)/Sbase)^2);
        
     else
         penalty_slack=0;
end


%% Penalty function for bus voltage violation

penalty_V=[];
Vmax=mpc1(1).bus(:,10);
Vmin=mpc1(1).bus(:,11);
   for i=1:nb
     if res.bus(i,2)==1
       if V(i)>Vmax(i)
             penalty_V(i)=100+P2*(V(i)-Vmax(i))^2;
            
        elseif V(i)<Vmin(i)
             penalty_V(i)=100+P2*(Vmin(i)-V(i))^2;
            
         else
             penalty_V(i)=0;
       end
    end
   end
    penalty_V=sum(penalty_V);
   
   
%% Penalty function for reactive power generation violation
% penalty_Qgen=[];
% Qmax=gen(:,5);
% Qmin=gen(:,6);
%    for i=1:ng
%         if Qgen(i)>Qmax(i)
%              penalty_Qgen(i)=P3*(((Qgen(i)-Qmax(i))/Sbase)^2);
%               
%         elseif Qgen(i)<Qmin(i)
%              penalty_Qgen(i)=P3*(((Qmin(i)-Qgen(i))/Sbase)^2);
%              
%          else
%              penalty_Qgen(i)=0;
%         end
%    end
%     penalty_Qgen=sum(penalty_Qgen);
   penalty_Qgen=0;
   
%% Penalty function for VSC M
M_Smax=0.4;
M_Smin=0;
penalty_M=[];
M=find(mpc1(t).branch(:, 6) ~= 0);
   for i=1:size(M,1)
        if S(i)>M_Smax
             penalty_M(i)=100+P4*(S(i)-M_Smax)^2;
              
        elseif S(i)<M_Smin
             penalty_M(i)=100+P4*(S(i)-M_Smin)^2;
             
         else
             penalty_M(i)=0;
        end
   end
    penalty_M=sum(penalty_M);
    

%% CUMULATIVE PENALTY FUNTION累积惩罚函数

 penalty(t)=penalty_slack+penalty_V+penalty_Qgen+penalty_M;
% penalty(t)=penalty_slack+penalty_V+penalty_M;


F1(t) = loss(t);

cgrid=price(t)*Pgslack*1000;%电网交互成本
cdg=0.065*sum(Pwt(t,:))*1000+0.055*sum(Ppv(t,:))*1000;%风光发电成本
cpdg=1.58*(sum(Pwt(t,:)-X(t,1:3))+sum(Ppv(t,:)-X(t,4:6)))*1000;%弃风弃光成本
cess=0.05*sum(abs(X(t,Nwt + Npv + 1:Nwt + Npv +Nm+Nmcita+ Ness)))*1000;%储能维护成本
% cwpp=0.2*(sum(date(t,1:2)) - sum(X(t,Nv+1:Nv + Nwt+Npv)))*1000;%弃风弃光惩罚
% F2(t)=cgrid+cdg+cess+cwpp;
F2(t)=cgrid+cess+price(t)*F1(t)+cdg+cpdg;
F3(t)=sum(abs((V.*V)));

end

if convergence==0
    %平移负荷的补偿成本
    Tp=round(X(1,Nwt + Npv +Nm+Nmcita+ Ness +Nsvg +1:Nwt + Npv +Nm+Nmcita+ Ness +Nsvg +N_SL))~=shift_load(:,4)';
    C_SL=0.3*1000*sum(Tp'.*(shift_load(:,2).*shift_load(:,6)));

    %可削减负荷校验与补偿成本
    parfor i = 1:N_CL
        continuous_count = 0; % 记录连续削减次数
        total_reductions = 0; % 记录总削减次数
        is_valid = true; % 有效性标志的初始化
        for t=1:24
            if X(t,Nwt + Npv +Nm+Nmcita+ Ness +Nsvg +N_SL+N_CL+i) == 1
                continuous_count = continuous_count + 1; % 增加连续削减次数

                % 检查是否超过最大连续削减次数
                if continuous_count > 5
                    is_valid = false; % 标记为无效
                    break;
                end
            else
                continuous_count = 0; % 重置连续次数
            end
        end

        % 检查总削减次数是否超过最大限制
        total_reductions = sum(X(:,Nwt + Npv +Nm+Nmcita+ Ness +Nsvg +N_SL+N_CL+i)); % 增加总削减次数
        if total_reductions > 15
            is_valid = false;
        end

        % 计算总成本
        if is_valid
            C_CL(i) = sum(X(:,Nwt + Npv + Ness +Nsvg +N_SL+i).*X(:,Nwt + Npv +Nm+Nmcita+ Ness +Nsvg +N_SL+N_CL+i) * 0.4*1000);
        else
            C_CL(i) = 10000; % 如果无效，成本设为10000
        end
    end
    C_CL=sum(C_CL);

    F1=sum(F1);%网损
    F2=sum(F2)+C_SL+C_CL;%运行成本
    F3=sum(F3);%电压偏差
    penalty=sum(penalty);
else
    F1=0;%网损
    F2=0;%运行成本
    F3=0;%电压偏差
    penalty=0;
end
end
