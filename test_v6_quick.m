%% 快速测试v6修复效果
clear; clc; close all;

% 加载全局变量
global mpc_base Pwt Ppv price shift_load cut_load Load_ac Load_dc
global N_SL N_CL Nwt Npv ac acq ESS_params

% 加载数据
load_data_v6;

% 设置储能参数
ESS_params.Ce = 700;
ESS_params.Cp = 400;
ESS_params.Cmaint = 60;
ESS_params.ir = 0.015;
ESS_params.dr = 0.09;
ESS_params.Y = 10;
ESS_params.lambda = 0.08;
ESS_params.Cde = 60;
ESS_params.E_max = 8000;
ESS_params.P_max = 2000;

% 测试配置
X_test = zeros(1, 2 + 2 + 2 + N_SL + 24*N_CL + 24*N_CL);

% 储能配置 - 使用较大容量测试
X_test(1) = 7;      % ESS1节点
X_test(2) = 33;     % ESS2节点
X_test(3) = 3000;   % ESS1容量 3MWh
X_test(4) = 2000;   % ESS2容量 2MWh
X_test(5) = 4.0;    % ESS1功率配比 4h
X_test(6) = 4.0;    % ESS2功率配比 4h

% 平移负荷时间
idx = 7;
for i = 1:N_SL
    X_test(idx) = shift_load(i,4); % 原始时间，测试峰→谷逻辑
    idx = idx + 1;
end

% 削减负荷量
for t = 1:24
    for i = 1:N_CL
        is_peak = (t >= 10 && t <= 12) || (t >= 18 && t <= 21);
        if is_peak
            X_test(idx) = cut_load(i,t+1) * 0.3; % 高峰期削减30%
        else
            X_test(idx) = 0; % 非高峰期不削减
        end
        idx = idx + 1;
    end
end

% 削减负荷开关
for t = 1:24
    for i = 1:N_CL
        is_peak = (t >= 10 && t <= 12) || (t >= 18 && t <= 21);
        X_test(idx) = is_peak ? 3 : -3; % 高峰期开启，非高峰期关闭
        idx = idx + 1;
    end
end

fprintf('=== 快速测试v6修复效果 ===\n');

% 计算目标函数
[f1, f2, cost_details] = ESS_objective_v6(X_test);

fprintf('\n=== 结果分析 ===\n');
fprintf('储能全寿命周期成本: %.2f 万元\n', f1/10000);
fprintf('系统运行成本: %.2f 万元\n', f2/10000);

if isfield(cost_details, 'renewable_stats')
    fprintf('\n=== 风光利用率 ===\n');
    fprintf('风电利用率: %.1f%% (弃风率: %.1f%%)\n', ...
        cost_details.renewable_stats.wind_utilization_rate, ...
        cost_details.renewable_stats.wind_curtailment_rate);
    fprintf('光伏利用率: %.1f%% (弃光率: %.1f%%)\n', ...
        cost_details.renewable_stats.pv_utilization_rate, ...
        cost_details.renewable_stats.pv_curtailment_rate);
    
    if cost_details.renewable_stats.wind_curtailment_rate < 15
        fprintf('✅ 弃风弃光率显著改善！\n');
    else
        fprintf('❌ 弃风弃光率仍需优化\n');
    end
end

% 检查储能调度
if isfield(cost_details, 'hourly_data')
    ess_power = cost_details.hourly_data.ess_power;
    
    % 检查高峰期是否放电
    peak_hours = [10:12, 18:21];
    peak_discharge = 0;
    for t = peak_hours
        peak_discharge = peak_discharge + sum(max(ess_power(t,:), 0));
    end
    
    if peak_discharge > 1.0
        fprintf('✅ 高峰期储能正常放电！\n');
    else
        fprintf('❌ 高峰期储能放电不足\n');
    end
    
    % 检查光伏期间是否充电
    pv_hours = 8:16;
    pv_charge = 0;
    for t = pv_hours
        pv_charge = pv_charge + sum(max(-ess_power(t,:), 0));
    end
    
    if pv_charge > 2.0
        fprintf('✅ 光伏期间储能积极充电！\n');
    else
        fprintf('❌ 光伏期间储能充电不足\n');
    end
end

% 绘制结果
try
    plot_dispatch_results_v6(X_test, cost_details, ESS_params);
    fprintf('\n✅ 图表生成成功\n');
catch ME
    fprintf('❌ 绘图失败: %s\n', ME.message);
end

fprintf('\n=== 测试完成 ===\n'); 