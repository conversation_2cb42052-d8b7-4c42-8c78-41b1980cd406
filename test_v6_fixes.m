%% 测试v6修复效果
% 验证高峰期放电、凌晨购电、弃电率等问题的修复情况

clear; clc; close all;

% 加载全局变量
global mpc_base Pwt Ppv price shift_load cut_load Load_ac Load_dc
global N_SL N_CL Nwt Npv ac acq ESS_params

% 加载数据
load_data_v6;

% 设置储能参数
ESS_params.Ce = 700;
ESS_params.Cp = 400;
ESS_params.Cmaint = 60;
ESS_params.ir = 0.015;
ESS_params.dr = 0.09;
ESS_params.Y = 10;
ESS_params.lambda = 0.08;
ESS_params.Cde = 60;
ESS_params.E_max = 8000;
ESS_params.P_max = 2000;

% 使用上次优化的最优解进行测试
X_test = zeros(1, 2 + 2 + 2 + N_SL + 24*N_CL + 24*N_CL);

% 储能配置 (节点4+28, 容量2291.57+1563.95 kWh)
X_test(1) = 4;      % ESS1节点
X_test(2) = 28;     % ESS2节点
X_test(3) = 2291.57; % ESS1容量
X_test(4) = 1563.95; % ESS2容量
X_test(5) = 5.52;   % ESS1功率配比
X_test(6) = 5.81;   % ESS2功率配比

% 平移负荷时间（使用原始时间，测试峰→谷逻辑）
idx = 7;
for i = 1:N_SL
    X_test(idx) = shift_load(i,4); % 使用原始开始时间
    idx = idx + 1;
end

% 削减负荷量（高峰期适度削减）
for t = 1:24
    for i = 1:N_CL
        is_peak = (t >= 10 && t <= 12) || (t >= 18 && t <= 21);
        if is_peak
            X_test(idx) = cut_load(i,t+1) * 0.5; % 高峰期削减50%
        else
            X_test(idx) = cut_load(i,t+1) * 0.05; % 非高峰期削减5%
        end
        idx = idx + 1;
    end
end

% 削减负荷开关（高峰期开启，非高峰期关闭）
for t = 1:24
    for i = 1:N_CL
        is_peak = (t >= 10 && t <= 12) || (t >= 18 && t <= 21);
        if is_peak
            X_test(idx) = 2; % 高峰期开启
        else
            X_test(idx) = -2; % 非高峰期关闭
        end
        idx = idx + 1;
    end
end

fprintf('=== 测试v6修复效果 ===\n');
fprintf('决策变量维度: %d\n', length(X_test));

% 计算目标函数
[f1, f2, cost_details] = ESS_objective_v6(X_test);

fprintf('\n=== 成本分析 ===\n');
fprintf('储能全寿命周期成本: %.2f 万元\n', f1/10000);
fprintf('系统运行成本: %.2f 万元\n', f2/10000);
fprintf('弃风弃光成本: %.2f 万元\n', cost_details.curtail_cost/10000);

if isfield(cost_details, 'renewable_stats')
    fprintf('\n=== 风光利用率 ===\n');
    fprintf('风电利用率: %.1f%% (弃风率: %.1f%%)\n', ...
        cost_details.renewable_stats.wind_utilization_rate, ...
        cost_details.renewable_stats.wind_curtailment_rate);
    fprintf('光伏利用率: %.1f%% (弃光率: %.1f%%)\n', ...
        cost_details.renewable_stats.pv_utilization_rate, ...
        cost_details.renewable_stats.pv_curtailment_rate);
end

% 分析储能调度
if isfield(cost_details, 'hourly_data')
    ess_power = cost_details.hourly_data.ess_power;
    P_import = cost_details.hourly_data.P_import;
    
    fprintf('\n=== 储能调度分析 ===\n');
    
    % 高峰期储能功率
    morning_peak = 10:12;
    evening_peak = 18:21;
    
    fprintf('上午高峰期(10-12h)储能功率:\n');
    for t = morning_peak
        total_ess = sum(ess_power(t,:));
        fprintf('  时刻%02d: %.3f MW (%s)\n', t, total_ess, ...
            total_ess > 0 ? '放电' : '充电');
    end
    
    fprintf('晚间高峰期(18-21h)储能功率:\n');
    for t = evening_peak
        total_ess = sum(ess_power(t,:));
        fprintf('  时刻%02d: %.3f MW (%s)\n', t, total_ess, ...
            total_ess > 0 ? '放电' : '充电');
    end
    
    % 凌晨购电分析
    early_hours = 1:3;
    fprintf('\n凌晨购电分析(1-3h):\n');
    for t = early_hours
        fprintf('  时刻%02d: %.3f MW\n', t, P_import(t));
    end
    
    % 总体统计
    total_charge_energy = sum(max(-ess_power(:), 0));
    total_discharge_energy = sum(max(ess_power(:), 0));
    total_purchase_energy = sum(P_import);
    
    fprintf('\n=== 能量统计 ===\n');
    fprintf('储能充电总量: %.2f MWh\n', total_charge_energy);
    fprintf('储能放电总量: %.2f MWh\n', total_discharge_energy);
    fprintf('购电总量: %.2f MWh\n', total_purchase_energy);
end

% 绘制结果
try
    plot_dispatch_results_v6(X_test, cost_details, ESS_params);
    fprintf('\n调度结果图表已生成，请检查:\n');
    fprintf('1. 高峰期(10-12h, 18-21h)储能是否放电\n');
    fprintf('2. 凌晨(0-2h)购电量是否合理\n');
    fprintf('3. 负荷曲线与柱形图是否匹配\n');
    fprintf('4. 弃风弃光率是否降低\n');
catch ME
    fprintf('绘图出错: %s\n', ME.message);
end

fprintf('\n=== 测试完成 ===\n'); 