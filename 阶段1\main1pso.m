%参数初始化，初始时主要设置代理数量和最大迭代次数即可，其他算法相关的参数因为和当前迭代次数相关，需要在迭代中设置。
clc;clear;
% population size setting
% m = 50;
date=xlsread('提取的风光数据.xlsx');
Pwt=date(:,1);Ppv=date(:,2);Load_ac=date(:,3);Load_dc=date(:,4);price=date(:,5);
mpc=case33;

%计算负荷比例
ratio=mpc.bus(:,3:5)./sum(mpc.bus(:,3:5));
ratioq=mpc.bus(:,4)./mpc.bus(:,3);
ratioq(isnan(ratioq)) = 0;
ac=[];dc=[];acq=[];
for i=1:24
    ac(i,:)=Load_ac(i)*ratio(:,1);
    %     acq(i,:)=ac(i,:).*ratioq';
    acq(i,:)=ac(i,:).*ratioq';
    dc(i,:)=Load_dc(i)*ratio(:,3);
end

data=mpc;
% ng=length(mpc.gen(:,1));
M=length(find(mpc.branch(:, 6) ~= 0));
% M=0;
% Vg=mpc.gen(1:end,7);

global m runnum  Nv Nwt Npv Nm Nmcita Ness Nsvg d
runnum=10;
m=100;
Max_gen=300;
% Dimensions(未知数个数）发电机的V，新能源的用电量，M，两个储能,3个SVG(静态无功补偿装置)
Nv=3;Nwt=3; Npv=3; Nm=0; Nmcita=0; Ness=2; Nsvg=0;
% d=(Nv + Nwt + Npv + Nm + Nmcita); %+ Ness + Nsvg);
% d=(2*ng-1 +M+2);         % Number of
d=(Nv + Nwt + Npv + Ness);
FE_NM = m;
%%上下限规定
Vg_min=repmat((ones(Nv,1)*1.01)',24,1); %约束上下限%电压
Vg_max=repmat((ones(Nv,1)*1.06)',24,1);

Pwt_min=zeros(24,Nwt);%风光出力
Ppv_min=zeros(24, Npv);
Pwt_max=repmat(Pwt,1,Nwt).*[0.6/1.6 0.5/1.6 0.5/1.6];
Ppv_max=repmat(Ppv,1, Npv).*[0.5/1.3 0.4/1.3 0.4/1.3];

% M_min=repmat((ones(M,1)*0.9)',24,1);%VSC调制比上下限与功率因数角
% M_max=repmat([ones(M,1)]',24,1);
% Mcita_min=repmat(deg2rad(-(ones(M,1)*0)'),24,1);
% Mcita_max=repmat((ones(M,1)*0.4510)',24,1);

P_min=repmat((ones(2,1)*-0.4)',24,1);%储能充放电功率
P_max=repmat((ones(2,1)*0.4)',24,1);

% SVG_min=ones(24,3).*[-0.3 -0.4 -0.4];%SVG补偿
% SVG_max=ones(24,3).*[0.4 0.5 0.5];

MinParValue=[Vg_min Pwt_min Ppv_min P_min];
MaxParValue=[Vg_max Pwt_max Ppv_max P_max];

%%算法参数
c1 = 1.5 ; %　学习因子1
c2 = 1.5 ; %　学习因子2
w = 0.8 ; %　惯性权重
Vmax = (MaxParValue - MinParValue) * 0.2;
Vmin = -Vmax;

% Max_gen = 500;
finalresults = [];lossresults=[];priceresults=[];penaltyresult=[];V_lessresult=[];
% Max_FEs = 50000;
% results = zeros(runnum,Max_gen);
solution = [];
Ebat0=0.4;
% several runs
for run = 1 : runnum
    %%%                      Initialize Population                          %%%
    for i=1:m

        pop(i).Population= MinParValue+ rand(24,d) .* (MaxParValue - MinParValue);
        pop(i).vel = Vmin+ rand(24,d).*(Vmax-Vmin);
        penalty_e=[];
        for j=1:Ness
            [pop(i).Population(:,Nv + Nwt + Npv + Nm + Nmcita+j),penalty_e(j)]=ESS_test(pop(i).Population(:,Nv + Nwt + Npv + Nm + Nmcita+j));
        end
        penalty_E(i)=sum(penalty_e);
        [F1(i),F2(i),F3(i),penalty(i)] = CostFunction(pop(i).Population,data,date,ac,acq,dc);
        pop(i).pBest=pop(i).Population;
    end
    Objective_values=F2+penalty+penalty_E;
%     for t=1:24
%     vel(t,:) = Vmin(t,:)+ rand(1,d).*(Vmax(t,:)-Vmin(t,:));
%     end
    pBestScore = Objective_values; %initialize the pbest and the pbest's fitness value
    [gBestScore,gbestid] = min(pBestScore);
    gBest = pop(gbestid).Population;
    
    %=========================================================================%
    %%%                         BBO Main Loop                               %%%
    %     FES = m;
    gen = 0;
    results = [];
    loss=[];
    price=[];
    V_less=[];
    penalt2=[];
    tic;
    % main loop
    while(gen < Max_gen)

        for i = 1 : m
            for t=1:24
            pop(i).vel(t,:) = w * pop(i).vel(t,:) + c1 * rand(1,d) .* (pop(i).pBest(t,:) - pop(i).Population(t,:)) + c2 * rand(1,d) .* (gBest(t,:) - pop(i).Population(t,:));  % update velocity
            Flag4Vmax = pop(i).vel(t,:) > Vmax(t,:);
            Flag4Vmin = pop(i).vel(t,:) < -Vmax(t,:);
            pop(i).vel(t,:) = (pop(i).vel(t,:) .*(~(Flag4Vmax + Flag4Vmin))) + Vmax(t,:) .* Flag4Vmax + (-Vmax(t,:)) .* Flag4Vmin;
            pop(i).Population(t,:) = pop(i).Population(t,:) + pop(i).vel(t,:);
            Tp= pop(i).Population(t,:)>MaxParValue(t,:);Tm= pop(i).Population(t,:)<MinParValue(t,:);
            pop(i).Population(t,:)=(pop(i).Population(t,:).*(~(Tp+Tm)))+MaxParValue(t,:).*Tp+MinParValue(t,:).*Tm;

                for j=Nv + Nwt + Npv+ Ness-1:Nv + Nwt + Npv+ Ness
                    pop(i).Population(t,j)=round(pop(i).Population(t,j)*1000)/1000;%储能充放电功率进行小数点后3位取舍
                    if pop(i).Population(t,j)>MaxParValue(t,:)
                        pop(i).Population(t,j)=MaxParValue(t,:);
                    end
                    if pop(i).Population(t,j)<MinParValue(t,:)
                        pop(i).Population(t,j)=MinParValue(t,:);
                    end
                end
            end
            %%进行SOC校验，若越限则更新充放电功率
            %计算时刻的SOC
            penalty_e=[];
            for j=1:Ness
            [pop(i).Population(:,Nv + Nwt + Npv + Nm + Nmcita+j),penalty_e(j)]=ESS_test(pop(i).Population(:,Nv + Nwt + Npv + Nm + Nmcita+j));
            end
            penalty_E=sum(penalty_e); 

            [F11,F22,F33,penalty1] = CostFunction(pop(i).Population,data,date,ac,acq,dc);
            Objective_values=F22+penalty1+penalty_E;
            if Objective_values<pBestScore(i)
                pop(i).pBest=pop(i).Population;
                pBestScore(i)=Objective_values;
            end
            if pBestScore(i)<gBestScore
                gBest=pop(i).pBest;
                gBestScore=pBestScore;
            end
        end%更新完所有粒子（ for i = 1 : m）


        MinimumCost = gBestScore;
        bestsolution = gBest;
        % Display Iteration Information
        gen = gen + 1;
        %         results(run,gen) = MinimumCost;
        results = [results; MinimumCost];
        [loss1,price1,V_less1,p1] = CostFunction(bestsolution,mpc,date,ac,acq,dc);
        loss=[loss;loss1];price=[price;price1];
        V_less=[V_less;V_less1];penalt2=[penalt2;p1];
        %         fprintf('Best fitness: %e gen %d\n', MinimumCost,gen);
        %         fprintf('Best fitness: %e and avg:  %e gen %d\n', MinimumCost, mean(Objective_values), gen);

    end
%     xx = round(linspace(1,length(results),200));

    finalresults = [finalresults results];
    lossresults=[lossresults loss];
    priceresults=[priceresults price];
    solution = [solution; bestsolution];
    penaltyresult=[penaltyresult penalt2];
    V_lessresult=[V_lessresult V_less];
    fprintf('Run No.%d Done!\n', run);
    disp(['CPU time: ',num2str(toc)]);
end
xlswrite('losspso.xls', lossresults);
xlswrite('pricepso.xls', priceresults);
xlswrite('penaltypso.xls', penaltyresult);
xlswrite('solutionpso.xls', solution);
xlswrite('V_lesspso.xls', V_lessresult);