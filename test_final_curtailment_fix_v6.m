%% 测试最终弃电率修复效果
% 验证大容量储能配置能否将弃电率降低到5%以下

clear; clc; close all;

% 全局变量声明
global mpc_base Pwt Ppv price shift_load cut_load Load_ac Load_dc
global N_SL N_CL Nwt Npv ac acq dc ESS_params

% 最终修复后的储能参数设置
ESS_params.Ce = 200;        % 储能单位容量成本 (元/kWh) - 极大降低
ESS_params.Cp = 100;        % 储能单位功率成本 (元/kW) - 极大降低
ESS_params.Cmaint = 50;     % 年运行维护成本 (元/kW·年) - 极大降低
ESS_params.ir = 0.015;      % 通胀膨胀率
ESS_params.dr = 0.09;       % 贴现率
ESS_params.Y = 10;          % 储能寿命 (年)
ESS_params.lambda = 0.08;   % 净报废成本系数
ESS_params.Cde = 60;        % 单位容量报废处置成本 (元/kWh)
ESS_params.E_max = 25000;   % 最大安装容量 25 MWh
ESS_params.P_max = 8000;    % 最大充放电功率 8 MW

% 加载数据
load_data_v6;

fprintf('=== 最终弃电率修复效果测试 ===\n\n');

%% 测试大容量储能配置
test_configs = [
    % [ESS1_capacity, ESS2_capacity, ESS1_ratio, ESS2_ratio]
    [8000, 8000, 4.0, 4.0];    % 配置1：16MWh总容量，4MW总功率
    [10000, 10000, 5.0, 5.0];  % 配置2：20MWh总容量，4MW总功率
    [12000, 12000, 4.0, 4.0];  % 配置3：24MWh总容量，6MW总功率
    [15000, 10000, 5.0, 4.0];  % 配置4：25MWh总容量，5MW总功率
];

fprintf('测试大容量储能配置对弃电率的影响:\n');
fprintf('配置  总容量  总功率  弃电率   弃电成本  系统成本  储能成本  投资回收期\n');
fprintf('----  ------  ------  ------   --------  --------  --------  ----------\n');

best_config = [];
best_curtailment_rate = 100;
best_solution = [];

for i = 1:size(test_configs, 1)
    % 构造测试解
    n_var = 2 + 2 + 2 + N_SL + N_CL;
    solution_test = zeros(1, n_var);
    solution_test(1:2) = [30, 5];                           % 储能安装节点
    solution_test(3:4) = test_configs(i, 1:2);              % 储能容量
    solution_test(5:6) = test_configs(i, 3:4);              % 功率配比
    
    % 平移负荷时间设置
    idx = 7;
    if N_SL > 0
        solution_test(idx:idx+N_SL-1) = shift_load(:,4)';
        idx = idx + N_SL;
    end
    
    % 削减负荷开关设置
    if N_CL > 0
        solution_test(idx:idx+N_CL-1) = 0;
    end
    
    % 运行目标函数
    [f1, f2, cost_details] = ESS_objective_v6(solution_test);
    
    % 计算总容量和功率
    total_capacity = sum(test_configs(i, 1:2)) / 1000; % MWh
    total_power = sum(test_configs(i, 1:2) ./ test_configs(i, 3:4)) / 1000; % MW
    
    % 获取弃电率
    curtailment_rate = 0;
    if isfield(cost_details, 'renewable_stats')
        curtailment_rate = cost_details.renewable_stats.wind_curtailment_rate;
    end
    
    % 计算投资回收期
    % 与不配置储能对比
    solution_no_ess = zeros(1, n_var);
    solution_no_ess(1:2) = [10, 15];
    solution_no_ess(3:4) = [0, 0];
    solution_no_ess(5:6) = [3, 3];
    
    idx = 7;
    if N_SL > 0
        solution_no_ess(idx:idx+N_SL-1) = shift_load(:,4)';
        idx = idx + N_SL;
    end
    if N_CL > 0
        solution_no_ess(idx:idx+N_CL-1) = 0;
    end
    
    [f1_no_ess, f2_no_ess, cost_details_no_ess] = ESS_objective_v6(solution_no_ess);
    
    annual_saving = (cost_details_no_ess.curtail_cost - cost_details.curtail_cost) * 365;
    annual_ess_cost = f1 / ESS_params.Y;
    payback_period = 999;
    if annual_saving > 0
        payback_period = annual_ess_cost / annual_saving;
    end
    
    fprintf('%2d    %5.1f   %5.1f   %5.1f%%   %7.2f   %7.2f   %7.2f   %8.1f年\n', ...
        i, total_capacity, total_power, curtailment_rate, ...
        cost_details.curtail_cost/10000, f2/10000, f1/10000, payback_period);
    
    % 记录最佳配置
    if curtailment_rate < best_curtailment_rate
        best_curtailment_rate = curtailment_rate;
        best_config = test_configs(i, :);
        best_solution = solution_test;
    end
end

%% 详细分析最佳配置
if ~isempty(best_config)
    fprintf('\n=== 最佳配置详细分析 ===\n');
    
    [f1_best, f2_best, cost_details_best] = ESS_objective_v6(best_solution);
    
    fprintf('最佳储能配置:\n');
    fprintf('ESS1: %.0f kWh, %.0f kW\n', best_config(1), best_config(1)/best_config(3));
    fprintf('ESS2: %.0f kWh, %.0f kW\n', best_config(2), best_config(2)/best_config(4));
    fprintf('总容量: %.1f MWh\n', sum(best_config(1:2))/1000);
    fprintf('总功率: %.1f MW\n', sum(best_config(1:2)./best_config(3:4))/1000);
    
    fprintf('\n性能指标:\n');
    fprintf('弃风弃光率: %.1f%% (目标: <5%%)\n', best_curtailment_rate);
    fprintf('储能全寿命周期成本: %.2f 万元\n', f1_best/10000);
    fprintf('系统运行成本: %.2f 万元\n', f2_best/10000);
    fprintf('弃风弃光成本: %.2f 万元\n', cost_details_best.curtail_cost/10000);
    
    if isfield(cost_details_best, 'ess_utilization')
        fprintf('储能1利用率: %.1f%%\n', cost_details_best.ess_utilization.ess1_rate*100);
        fprintf('储能2利用率: %.1f%%\n', cost_details_best.ess_utilization.ess2_rate*100);
    end
    
    % 检查是否达到目标
    if best_curtailment_rate <= 5.0
        fprintf('\n🎉 成功！弃电率已降低到%.1f%%，达到5%%以下的目标！\n', best_curtailment_rate);
        
        % 计算达成目标的关键因素
        fprintf('\n成功关键因素:\n');
        fprintf('1. 储能容量充足: %.1f MWh (相比之前的5-10MWh大幅提升)\n', sum(best_config(1:2))/1000);
        fprintf('2. 储能功率充足: %.1f MW (相比之前的2.5MW大幅提升)\n', sum(best_config(1:2)./best_config(3:4))/1000);
        fprintf('3. 成本大幅降低: 容量成本%.0f元/kWh, 功率成本%.0f元/kW\n', ESS_params.Ce, ESS_params.Cp);
        fprintf('4. SOC约束优化: 初始SOC 5%%, 上限99.5%%\n');
        
    else
        fprintf('\n⚠️  弃电率%.1f%%仍高于5%%目标，需要进一步增加储能容量\n', best_curtailment_rate);
    end
    
    %% 分析调度逻辑修复效果
    if isfield(cost_details_best, 'hourly_data')
        P_curtail = cost_details_best.hourly_data.P_curtail;
        ess_power = cost_details_best.hourly_data.ess_power;
        
        fprintf('\n=== 调度逻辑修复效果检查 ===\n');
        logic_issues = 0;
        for t = 1:24
            if P_curtail(t) > 0.01
                total_ess_power = sum(ess_power(t,:));
                if total_ess_power >= 0  % 储能不在充电
                    logic_issues = logic_issues + 1;
                    if logic_issues <= 3  % 只显示前3个问题
                        fprintf('时刻%02d:00: 有弃电%.3f MW，但储能功率%.3f MW\n', ...
                            t-1, P_curtail(t), total_ess_power);
                    end
                end
            end
        end
        
        if logic_issues == 0
            fprintf('✅ 调度逻辑修复成功！有弃电时储能都在充电\n');
        else
            fprintf('❌ 仍有%d个调度逻辑问题\n', logic_issues);
        end
        
        % 统计弃电时段
        curtailment_hours = find(P_curtail > 0.01);
        fprintf('弃电时段数: %d 小时\n', length(curtailment_hours));
        fprintf('总弃电量: %.2f MWh\n', sum(P_curtail));
        
        % 分析SOC使用情况
        if logic_issues == 0 && length(curtailment_hours) > 0
            fprintf('\n剩余弃电原因分析:\n');
            fprintf('- 储能容量已充分利用，剩余弃电可能由网络约束引起\n');
            fprintf('- 或需要进一步增加储能容量\n');
        end
    end
end

%% 经济性分析
fprintf('\n=== 经济性分析 ===\n');
if ~isempty(best_config)
    % 与不配置储能对比
    solution_no_ess = zeros(1, n_var);
    solution_no_ess(1:2) = [10, 15];
    solution_no_ess(3:4) = [0, 0];
    solution_no_ess(5:6) = [3, 3];
    
    idx = 7;
    if N_SL > 0
        solution_no_ess(idx:idx+N_SL-1) = shift_load(:,4)';
        idx = idx + N_SL;
    end
    if N_CL > 0
        solution_no_ess(idx:idx+N_CL-1) = 0;
    end
    
    [f1_no_ess, f2_no_ess, cost_details_no_ess] = ESS_objective_v6(solution_no_ess);
    
    fprintf('不配置储能:\n');
    fprintf('  系统运行成本: %.2f 万元\n', f2_no_ess/10000);
    fprintf('  弃风弃光成本: %.2f 万元\n', cost_details_no_ess.curtail_cost/10000);
    
    if isfield(cost_details_no_ess, 'renewable_stats')
        fprintf('  弃风弃光率: %.1f%%\n', cost_details_no_ess.renewable_stats.wind_curtailment_rate);
    end
    
    fprintf('\n配置储能后:\n');
    fprintf('  储能投资成本: %.2f 万元\n', f1_best/10000);
    fprintf('  系统运行成本: %.2f 万元\n', f2_best/10000);
    fprintf('  弃风弃光成本: %.2f 万元\n', cost_details_best.curtail_cost/10000);
    fprintf('  弃风弃光率: %.1f%%\n', best_curtailment_rate);
    
    % 计算效益
    annual_curtailment_saving = (cost_details_no_ess.curtail_cost - cost_details_best.curtail_cost) * 365;
    annual_ess_cost = f1_best / ESS_params.Y;
    
    fprintf('\n效益分析:\n');
    fprintf('  年弃电成本节省: %.2f 万元\n', annual_curtailment_saving/10000);
    fprintf('  年化储能成本: %.2f 万元\n', annual_ess_cost/10000);
    
    if annual_curtailment_saving > 0
        payback_period = annual_ess_cost / annual_curtailment_saving;
        fprintf('  投资回收期: %.1f 年\n', payback_period);
        
        if payback_period <= ESS_params.Y
            fprintf('  ✅ 经济性良好，投资回收期在储能寿命内\n');
        else
            fprintf('  ⚠️  投资回收期较长，需要考虑其他效益\n');
        end
    end
    
    % 计算弃电率改善效果
    curtailment_improvement = cost_details_no_ess.renewable_stats.wind_curtailment_rate - best_curtailment_rate;
    fprintf('  弃电率改善: %.1f个百分点\n', curtailment_improvement);
end

fprintf('\n=== 测试完成 ===\n');

if best_curtailment_rate <= 5.0
    fprintf('\n🎊 恭喜！弃电率目标达成！\n');
    fprintf('通过大容量储能配置和优化调度策略，成功将弃电率降低到%.1f%%\n', best_curtailment_rate);
else
    fprintf('\n继续优化建议:\n');
    fprintf('1. 进一步增加储能容量到30-40MWh\n');
    fprintf('2. 检查网络约束设置\n');
    fprintf('3. 考虑分布式储能布局\n');
end
