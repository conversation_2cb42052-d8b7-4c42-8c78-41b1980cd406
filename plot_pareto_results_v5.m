function plot_pareto_results_v5(ps, pf, pf_norm, best_solution, best_idx)
% 绘制 v5 版本的 Pareto 解集与前沿
% 输入:
%   ps           - Pareto 解对应的决策变量矩阵 (N×n_var)
%   pf           - Pareto 解的原始目标函数值矩阵 (N×2)
%   pf_norm      - Pareto 解的归一化目标函数值矩阵 (N×2)
%   best_solution- 归一化后距理想点最近的解对应的决策变量
%   best_idx     - best_solution 在 ps / pf 中的索引
%
% 该函数将生成一个两行两列的图:
%   (1,1) 原始目标值散点图  (万元)
%   (1,2) 归一化目标值散点图
%   (2,1) 原始目标值带曲线的前沿 (若存在)
%   (2,2) 归一化目标值带曲线的前沿
% 并用红色五角星标记最优解。

% --- 输入检查 ---
if isempty(pf) || size(pf,2) < 2
    warning('pf 数据为空, 无法绘制 Pareto 结果');
    return;
end

if nargin < 5 || isempty(best_idx)
    best_idx = 1; %#ok<NASGU>
end

% 将单位转换为万元便于阅读
pf_plot = pf / 1e4; % 元 -> 万元

figure('Name','Pareto Results v5','Position',[200, 200, 1200, 800]);

%% (1,1) 原始值散点图
subplot(2,2,1);
scatter(pf_plot(:,1), pf_plot(:,2), 60, 'filled', 'MarkerFaceColor', [0.2 0.6 0.8]);
xlabel('储能全寿命周期成本 / 万元','FontSize',12,'FontWeight','bold');
ylabel('系统运行成本 / 万元','FontSize',12,'FontWeight','bold');
grid on;
title('Pareto 解集 (原始值)');

%% (1,2) 归一化散点图
subplot(2,2,2);
scatter(pf_norm(:,1), pf_norm(:,2), 60, 'filled', 'MarkerFaceColor', [0.2 0.6 0.8]);
hold on;
scatter(0,0,100,'p','filled','MarkerFaceColor','g','MarkerEdgeColor','k');
xlabel('归一化储能成本','FontSize',12,'FontWeight','bold');
ylabel('归一化运行成本','FontSize',12,'FontWeight','bold');
grid on;
legend({'Pareto解','理想点'},'Location','best');
title('Pareto 解集 (归一化)');

%% (2,1) 原始值前沿散点图
subplot(2,2,3);
scatter(pf_plot(:,1), pf_plot(:,2), 60, 'filled', 'MarkerFaceColor', [0.2 0.6 0.8]);
xlabel('储能全寿命周期成本 / 万元','FontSize',12,'FontWeight','bold');
ylabel('系统运行成本 / 万元','FontSize',12,'FontWeight','bold');
title('Pareto 前沿 (原始值)');
grid on;

%% (2,2) 归一化前沿散点图
subplot(2,2,4);
scatter(pf_norm(:,1), pf_norm(:,2), 60, 'filled', 'MarkerFaceColor', [0.2 0.6 0.8]);
hold on;
scatter(0,0,100,'p','filled','MarkerFaceColor','g','MarkerEdgeColor','k');
xlabel('归一化储能成本','FontSize',12,'FontWeight','bold');
ylabel('归一化运行成本','FontSize',12,'FontWeight','bold');
title('Pareto 前沿 (归一化)');
grid on;

sgtitle('v5 Pareto 前沿与解集可视化','FontSize',14,'FontWeight','bold');
end 