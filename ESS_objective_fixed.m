function [f1, f2] = ESS_objective_fixed(X)
% 储能选址定容双目标函数 (修正版)
% 修正风光数据单位处理
% 输入: X - 决策变量向量
% 输出: f1 - 储能全寿命周期成本 (元)
%       f2 - 系统运行成本 (元)

global mpc_base Pwt Ppv price shift_load cut_load
global N_SL N_CL Nwt Npv ac acq dc ESS_params

% 风光基准容量定义 (MW)
wind_base = [1.2, 1.0, 1.5]; % 各风电场额定容量
pv_base = [0.8, 1.0, 1.0];   % 各光伏电站额定容量

%% 解析决策变量
ESS1_node = round(X(1));
ESS2_node = round(X(2));
ESS1_capacity = X(3); % kWh
ESS2_capacity = X(4); % kWh
ESS1_power = X(5); % kW
ESS2_power = X(6); % kW

% 风光削减量 (24*6, p.u.)
idx = 7;
wind_cut = zeros(24, 3);
pv_cut = zeros(24, 3);
for t = 1:24
    wind_cut(t,:) = X(idx:idx+2);
    pv_cut(t,:) = X(idx+3:idx+5);
    idx = idx + 6;
end

% 储能充放电功率 (24*2, MW)
ess_power = zeros(24, 2);
for t = 1:24
    ess_power(t,:) = X(idx:idx+1);
    idx = idx + 2;
end

% 平移负荷时间
shift_time_vars = X(idx:idx+N_SL-1);
idx = idx + N_SL;

% 削减负荷量和开关
cut_amount = zeros(24, N_CL);
cut_switch = zeros(24, N_CL);
for t = 1:24
    cut_amount(t,:) = X(idx:idx+N_CL-1);
    cut_switch(t,:) = sigmoid(X(idx+24*N_CL:idx+24*N_CL+N_CL-1));
    idx = idx + N_CL;
end

%% 目标函数1: 储能全寿命周期成本
% 储能投资成本 (元)
C_i1 = ESS1_capacity * ESS_params.Ce + ESS1_power * ESS_params.Cp;
C_i2 = ESS2_capacity * ESS_params.Ce + ESS2_power * ESS_params.Cp;
C_i = C_i1 + C_i2;

% 运维成本 (元)
C_m = 0;
for y = 1:ESS_params.Y
    annual_maint1 = ESS_params.Cmaint * ESS1_power * (1 + ESS_params.ir)^y / (1 + ESS_params.dr)^y;
    annual_maint2 = ESS_params.Cmaint * ESS2_power * (1 + ESS_params.ir)^y / (1 + ESS_params.dr)^y;
    C_m = C_m + annual_maint1 + annual_maint2;
end

% 报废折旧成本 (元)
C_d1 = 1/(1 + ESS_params.dr)^ESS_params.Y * ESS_params.lambda * ESS1_capacity * ESS_params.Ce;
C_d2 = 1/(1 + ESS_params.dr)^ESS_params.Y * ESS_params.lambda * ESS2_capacity * ESS_params.Ce;
C_d = C_d1 + C_d2;

f1 = C_i + C_m + C_d;

%% 目标函数2: 系统运行成本
% 构建修改后的配电网模型
mpc_modified = cell(24,1);
for t = 1:24
    mpc_modified{t} = mpc_base;
    
    % 修改负荷
    mpc_modified{t}.bus(:,3) = ac(t,:)';
    mpc_modified{t}.bus(:,4) = acq(t,:)';
    mpc_modified{t}.bus(:,5) = dc(t,:)';
    
    % 修正风光出力处理 - 添加基准容量转换
    % 计算实际风光出力 (MW)
    wind_actual = zeros(3,1);
    pv_actual = zeros(3,1);
    for i = 1:3
        wind_actual(i) = max(0, Pwt(t,i) * wind_base(i) - wind_cut(t,i) * wind_base(i));
        pv_actual(i) = max(0, Ppv(t,i) * pv_base(i) - pv_cut(t,i) * pv_base(i));
    end
    
    % 风电注入 (MW转换为p.u.)
    mpc_modified{t}.bus([5,24,29],3) = mpc_modified{t}.bus([5,24,29],3) - wind_actual;
    mpc_modified{t}.bus([5,24,29],4) = mpc_modified{t}.bus([5,24,29],4) - wind_actual .* tan(acos(0.95));
    
    % 光伏注入 (MW转换为p.u.)
    mpc_modified{t}.bus([10,17,22],5) = mpc_modified{t}.bus([10,17,22],5) - pv_actual;
    
    % 储能充放电 (MW)
    if ESS1_node <= 33 && ESS1_node >= 2
        if mpc_modified{t}.bus(ESS1_node, 9) == 1 % DC节点
            mpc_modified{t}.bus(ESS1_node, 5) = mpc_modified{t}.bus(ESS1_node, 5) + ess_power(t,1);
        else % AC节点
            mpc_modified{t}.bus(ESS1_node, 3) = mpc_modified{t}.bus(ESS1_node, 3) + ess_power(t,1);
        end
    end
    
    if ESS2_node <= 33 && ESS2_node >= 2
        if mpc_modified{t}.bus(ESS2_node, 9) == 1 % DC节点
            mpc_modified{t}.bus(ESS2_node, 5) = mpc_modified{t}.bus(ESS2_node, 5) + ess_power(t,2);
        else % AC节点
            mpc_modified{t}.bus(ESS2_node, 3) = mpc_modified{t}.bus(ESS2_node, 3) + ess_power(t,2);
        end
    end
    
    % 平移负荷
    shift_time = time_SL(shift_time_vars, shift_load);
    for SL = 1:N_SL
        if t == shift_time(SL,1) || t == shift_time(SL,2)
            mpc_modified{t}.bus(shift_load(SL,1),3) = mpc_modified{t}.bus(shift_load(SL,1),3) + shift_load(SL,2);
            mpc_modified{t}.bus(shift_load(SL,1),4) = mpc_modified{t}.bus(shift_load(SL,1),4) + shift_load(SL,3);
        end
    end
    
    % 削减负荷
    for i = 1:N_CL
        reduction = cut_amount(t,i) * cut_switch(t,i);
        mpc_modified{t}.bus(cut_load(i,1),3) = mpc_modified{t}.bus(cut_load(i,1),3) + cut_load(i,t+1) - reduction;
    end
end

% 储能约束检查
[ess_power_corrected, penalty_E] = ESS_constraint_check(ess_power, [ESS1_capacity, ESS2_capacity], [ESS1_power, ESS2_power]);

% 计算系统运行成本
total_cost = 0;
penalty_total = 0;
convergence_flag = 0;

for t = 1:24
    % 潮流计算
    res = ac_dcpowerflow(mpc_modified{t});
    
    if res.gen == 100000 % 潮流不收敛
        convergence_flag = 1;
        break;
    end
    
    % 电网交互成本
    grid_cost = price(t) * res.gen * 1000; % 元
    
    % 修正弃风弃光成本计算 - 使用实际削减量(MW)
    wind_curtailed = sum(wind_cut(t,:) .* wind_base); % MW
    pv_curtailed = sum(pv_cut(t,:) .* pv_base); % MW
    curtail_cost = 1.58 * (wind_curtailed + pv_curtailed) * 1000; % 元
    
    % 网损成本
    loss_cost = price(t) * res.loss * 1000; % 元
    
    % 储能维护成本
    ess_maint_cost = 0.05 * sum(abs(ess_power_corrected(t,:))) * 1000; % 元
    
    total_cost = total_cost + grid_cost + curtail_cost + loss_cost + ess_maint_cost;
    
    % 惩罚函数
    penalty_total = penalty_total + calculate_penalty(res, mpc_modified{t});
end

if convergence_flag == 1
    f2 = 1e8; % 大惩罚值
    return;
end

% 柔性负荷补偿成本
% 平移负荷补偿成本
Tp = round(shift_time_vars) ~= shift_load(:,4)';
C_SL = 0.3 * 1000 * sum(Tp' .* (shift_load(:,2) .* shift_load(:,6)));

% 削减负荷补偿成本
C_CL = 0;
for i = 1:N_CL
    % 检查削减约束
    continuous_count = 0;
    total_reductions = sum(cut_switch(:,i));
    is_valid = true;
    
    for t = 1:24
        if cut_switch(t,i) == 1
            continuous_count = continuous_count + 1;
            if continuous_count > 5
                is_valid = false;
                break;
            end
        else
            continuous_count = 0;
        end
    end
    
    if total_reductions > 15
        is_valid = false;
    end
    
    if is_valid
        C_CL = C_CL + sum(cut_amount(:,i) .* cut_switch(:,i)) * 0.4 * 1000;
    else
        C_CL = C_CL + 10000; % 惩罚
    end
end

f2 = total_cost + C_SL + C_CL + penalty_total + penalty_E;

end

%% 辅助函数
function penalty = calculate_penalty(res, mpc)
% 计算惩罚函数
P1 = 500; P2 = 1000; P4 = 200;
Sbase = 10;

% 平衡节点功率约束
Pgslack = res.gen;
if Pgslack > 40
    penalty_slack = 100 + P1 * ((Pgslack - 40) / Sbase)^2;
elseif Pgslack < 0
    penalty_slack = 100 + P1 * ((0 - Pgslack) / Sbase)^2;
else
    penalty_slack = 0;
end

% 电压约束
V = res.bus(:,7);
penalty_V = 0;
for i = 1:size(mpc.bus,1)
    if res.bus(i,2) == 1
        if V(i) > 1.1
            penalty_V = penalty_V + 100 + P2 * (V(i) - 1.1)^2;
        elseif V(i) < 0.9
            penalty_V = penalty_V + 100 + P2 * (0.9 - V(i))^2;
        end
    end
end

% VSC约束
penalty_M = 0;
if ~isempty(res.S)
    for i = 1:length(res.S)
        if res.S(i) > 0.4
            penalty_M = penalty_M + 100 + P4 * (res.S(i) - 0.4)^2;
        end
    end
end

penalty = penalty_slack + penalty_V + penalty_M;
end

function [ess_power_corrected, penalty_E] = ESS_constraint_check(ess_power, capacities, max_powers)
% 储能约束检查和修正
ess_power_corrected = ess_power;
penalty_E = 0;

for ess_idx = 1:2
    Ebat0 = 0.4 * capacities(ess_idx) / 1000; % 初始SOC 40%, 转换为MWh
    Emin = 0.16 * capacities(ess_idx) / 1000; % 最小SOC 16%
    Emax = 0.72 * capacities(ess_idx) / 1000; % 最大SOC 72%
    Pmax = max_powers(ess_idx) / 1000; % 转换为MW
    
    Ebat = zeros(24,1);
    
    for t = 1:24
        % 功率约束
        if ess_power_corrected(t, ess_idx) > Pmax
            ess_power_corrected(t, ess_idx) = Pmax;
        elseif ess_power_corrected(t, ess_idx) < -Pmax
            ess_power_corrected(t, ess_idx) = -Pmax;
        end
        
        % SOC约束
        if t == 1
            if ess_power_corrected(t, ess_idx) > 0
                Ebat(t) = Ebat0 + ess_power_corrected(t, ess_idx) * 0.9;
            else
                Ebat(t) = Ebat0 + ess_power_corrected(t, ess_idx) / 0.9;
            end
        else
            if ess_power_corrected(t, ess_idx) > 0
                Ebat(t) = Ebat(t-1) + ess_power_corrected(t, ess_idx) * 0.9;
            else
                Ebat(t) = Ebat(t-1) + ess_power_corrected(t, ess_idx) / 0.9;
            end
        end
        
        if Ebat(t) > Emax
            Ebat(t) = Emax;
            penalty_E = penalty_E + 1000;
        elseif Ebat(t) < Emin
            Ebat(t) = Emin;
            penalty_E = penalty_E + 1000;
        end
    end
    
    % 末时刻SOC回归约束
    if abs(Ebat(24) - Ebat0) > 0.05 * capacities(ess_idx) / 1000
        penalty_E = penalty_E + 5000;
    end
end
end

function y = sigmoid(x)
% Sigmoid函数
y = 1 ./ (1 + exp(-x));
end

function shift_time = time_SL(shift_time_vars, shift_load)
% 平移负荷时间计算
shift_time = zeros(size(shift_load,1), 2);
for i = 1:size(shift_load,1)
    start_time = round(shift_time_vars(i));
    shift_time(i,1) = start_time;
    shift_time(i,2) = start_time + shift_load(i,6) - 1;
end
end 