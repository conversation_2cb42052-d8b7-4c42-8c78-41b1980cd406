%% v6版本：储能选址定容多目标优化 - 调度逻辑重构
% 核心改进：弃电放在最后环节，风光优先满足负荷和储能充电
% 删除风光实际出力决策变量，改为弃电量和购电量变量

clear; clc; close all;

% 全局变量声明
global mpc_base Pwt Ppv price shift_load cut_load Load_ac Load_dc
global N_SL N_CL Nwt Npv ac acq dc 
global ESS_params % 储能参数

% 储能参数设置 - v6版本：极大降低成本，激励超大容量配置
ESS_params.Ce = 700;        % 储能单位容量成本 (元/kWh) 
ESS_params.Cp = 500;        % 储能单位功率成本 (元/kW) 
ESS_params.Cmaint = 180;     % 年运行维护成本 (元/kW·年)
ESS_params.ir = 0.015;      % 通胀膨胀率
ESS_params.dr = 0.09;       % 贴现率
ESS_params.Y = 10;          % 储能寿命 (年)
ESS_params.lambda = 0.08;   % 净报废成本系数
ESS_params.Cde = 60;        % 单位容量报废处置成本 (元/kWh) new parameter
ESS_params.E_max = 5000;   % 最大安装容量 5 MWh，根本性解决容量不足问题
ESS_params.P_max = 2000;    % 最大充放电功率 2 MW，大幅提高峰谷调节能力

% 加载数据
load_data_v6;

% v6新增：风光基准容量 - 与目标函数保持一致
wind_base_new = [2.16, 1.8, 2.7]; % 风电基准容量 (MW)
pv_base_new = [2.0, 2.5, 2.5];    % 光伏基准容量 (MW)

%% v6版本：决策变量重新定义
% v6.3版本：简化可削减负荷模型为二进制变量
% 可削减负荷模型更改：P'cut,i = Pcut,i(1-βcut)，βcut ∈ {0,1}

n_obj = 2;  % 目标函数数

% 决策变量构成 (v6.3版本 - 可削减负荷模型简化)：
% 1-2:   储能1,2安装节点
% 3-4:   储能1,2额定容量 (kWh)
% 5-6:   储能1,2功率容量配比 (小时数)
% 7-...: 平移负荷时间
% 最后4个: 可削减负荷二进制开关 βcut (每个节点一个)
% 注：删除24×4×2=192个时变削减量和开关变量，改为4个二进制变量

n_siting_vars = 2;           % 储能选址变量
n_capacity_vars = 2;         % 储能容量变量
n_power_ratio_vars = 2;      % 功率容量配比变量
n_shift_time_vars = N_SL;    % 平移负荷时间变量
n_cut_binary_vars = N_CL;    % 可削减负荷二进制开关变量 (每个节点一个)

n_var = n_siting_vars + n_capacity_vars + n_power_ratio_vars + ...
        n_shift_time_vars + n_cut_binary_vars;

fprintf('=== v6.3版本决策变量构成 (可削减负荷模型简化) ===\n');
fprintf('储能选址: %d\n', n_siting_vars);
fprintf('储能容量: %d\n', n_capacity_vars);
fprintf('功率容量配比: %d\n', n_power_ratio_vars);
fprintf('平移负荷时间: %d\n', n_shift_time_vars);
fprintf('可削减负荷二进制开关: %d (每个节点一个βcut)\n', n_cut_binary_vars);
fprintf('总决策变量数: %d (从210个减少到%d个)\n', n_var, n_var);

% 变量边界
VRmin = zeros(1, n_var);
VRmax = zeros(1, n_var);

% 储能选址边界 (节点2-33)
VRmin(1:2) = 2;
VRmax(1:2) = 33;

% 储能容量边界 - 根本性解决容量不足问题
VRmin(3:4) = 1000;  % 最小容量8MWh，确保足够消纳空间
VRmax(3:4) = ESS_params.E_max;  % 最大容量

% 储能功率容量配比边界 - 缩短配比提高功率响应
VRmin(5:6) = 2.0;   % 最小2小时，提高最大功率响应
VRmax(5:6) = 4.0;   % 最大4小时，平衡功率和容量

idx = 7;

% 平移负荷时间边界
time_SL1 = shift_load(:,4);
time_SL2 = shift_load(:,5) - shift_load(:,6) + 1;
VRmin(idx:idx+N_SL-1) = time_SL1';
VRmax(idx:idx+N_SL-1) = time_SL2';
idx = idx + N_SL;

% 可削减负荷二进制开关边界 (βcut ∈ {0,1})
% 使用连续变量[0,1]，在目标函数中通过sigmoid函数转换为二进制
VRmin(idx:idx+N_CL-1) = zeros(1, N_CL);
VRmax(idx:idx+N_CL-1) = ones(1, N_CL);
idx = idx + N_CL;

% 验证边界向量长度
fprintf('边界向量长度验证: VRmin=%d, VRmax=%d, n_var=%d\n', length(VRmin), length(VRmax), n_var);
if length(VRmin) ~= n_var || length(VRmax) ~= n_var
    error('边界向量长度与决策变量数量不匹配！');
end

%% MMO_CLRPSO算法参数 - 提高收敛性
popsize = 200;  % 种群规模 - 增加以获得更好的前沿覆盖
Max_Gen = 30;  % 迭代代数 - 增加以确保充分收敛

fprintf('\n开始储能选址定容优化 (v6.3版本 - 可削减负荷模型简化)...\n');
fprintf('决策变量数: %d\n', n_var);
fprintf('目标函数数: %d\n', n_obj);
fprintf('储能最大容量: %d kWh\n', ESS_params.E_max);
fprintf('储能最大功率: %d kW\n', ESS_params.P_max);
fprintf('储能容量成本: %d 元/kWh\n', ESS_params.Ce);
fprintf('储能功率成本: %d 元/kW\n', ESS_params.Cp);
fprintf('调度逻辑: 弃电优先充电，TOU价格策略\n');

%% 调用MMO_CLRPSO算法
[ps, pf] = MMO_CLRPSO_ESS_v2('ESS_objective_v6', VRmin, VRmax, n_obj, popsize, Max_Gen);

%% 目标函数归一化处理
fprintf('\n进行目标函数归一化处理...\n');

% 计算目标函数的最小值和最大值
f1_min = min(pf(:,1));
f1_max = max(pf(:,1));
f2_min = min(pf(:,2));
f2_max = max(pf(:,2));

fprintf('目标函数1 (储能全寿命周期成本): %.2f - %.2f 万元\n', f1_min/10000, f1_max/10000);
fprintf('目标函数2 (系统运行成本): %.2f - %.2f 万元\n', f2_min/10000, f2_max/10000);

% Min-Max归一化
pf_norm = zeros(size(pf));
if f1_max > f1_min
    pf_norm(:,1) = (pf(:,1) - f1_min) / (f1_max - f1_min);
else
    pf_norm(:,1) = 0.5 * ones(size(pf,1), 1);
end

if f2_max > f2_min
    pf_norm(:,2) = (pf(:,2) - f2_min) / (f2_max - f2_min);
else
    pf_norm(:,2) = 0.5 * ones(size(pf,1), 1);
end

%% 基于归一化后的Pareto前沿选择最优解
% 计算到理想点(0,0)的距离
distances = sqrt(pf_norm(:,1).^2 + pf_norm(:,2).^2);
[~, best_idx] = min(distances);
best_solution = ps(best_idx,:);

%% 结果分析
fprintf('\n优化完成！\n');
fprintf('获得Pareto解的数量: %d\n', size(ps,1));

fprintf('\nv6.3最优解分析 (可削减负荷模型简化结果):\n');
fprintf('储能1安装节点: %d\n', round(best_solution(1)));
fprintf('储能2安装节点: %d\n', round(best_solution(2)));
fprintf('储能1额定容量: %.2f kWh\n', best_solution(3));
fprintf('储能2额定容量: %.2f kWh\n', best_solution(4));

% v6新增：通过小时比计算功率
ESS1_power = best_solution(3) / best_solution(5);
ESS2_power = best_solution(4) / best_solution(6);
fprintf('储能1额定功率: %.2f kW (配比%.2fh)\n', ESS1_power, best_solution(5));
fprintf('储能2额定功率: %.2f kW (配比%.2fh)\n', ESS2_power, best_solution(6));

% 计算最优解的目标函数值
[f1, f2, cost_details] = ESS_objective_v6(best_solution);
fprintf('储能全寿命周期成本: %.2f 万元\n', f1/10000);
fprintf('系统运行成本: %.2f 万元\n', f2/10000);
fprintf('归一化后的目标函数值: f1_norm=%.3f, f2_norm=%.3f\n', pf_norm(best_idx,1), pf_norm(best_idx,2));
fprintf('到理想点的距离: %.3f\n', distances(best_idx));

%% 详细成本分析
fprintf('\n=== v6详细成本分析 ===\n');
fprintf('目标函数1 - 储能全寿命周期成本 (%.2f万元):\n', f1/10000);
fprintf('  储能投资成本: %.2f 万元\n', cost_details.C_i/10000);
fprintf('  储能运维成本: %.2f 万元\n', cost_details.C_m/10000);
fprintf('  储能报废处置成本: %.2f 万元\n', cost_details.C_d/10000);
if isfield(cost_details, 'capacity_power_penalty')
    fprintf('  功率容量配比惩罚: %.2f 万元\n', cost_details.capacity_power_penalty/10000);
end

fprintf('\n目标函数2 - 系统运行成本 (%.2f万元):\n', f2/10000);
fprintf('  电网交互成本: %.2f 万元\n', cost_details.grid_cost/10000);
if isfield(cost_details, 'curtail_cost_rate')
    fprintf('  弃风弃光成本: %.2f 万元 (%.1f元/kWh)\n', cost_details.curtail_cost/10000, cost_details.curtail_cost_rate);
else
    fprintf('  弃风弃光成本: %.2f 万元\n', cost_details.curtail_cost/10000);
end
fprintf('  网损成本: %.2f 万元\n', cost_details.loss_cost/10000);
fprintf('  平移负荷补偿成本: %.2f 万元\n', cost_details.C_SL/10000);
fprintf('  削减负荷补偿成本: %.2f 万元\n', cost_details.C_CL/10000);
fprintf('  约束惩罚成本: %.2f 万元\n', cost_details.penalty_total/10000);
if isfield(cost_details, 'penalty_E')
    fprintf('  储能约束惩罚成本: %.2f 万元\n', cost_details.penalty_E/10000);
end
if isfield(cost_details, 'min_utilization_penalty')
    fprintf('  最低消纳率惩罚成本: %.2f 万元\n', cost_details.min_utilization_penalty/10000);
end

%% v6新增：储能利用率分析
fprintf('\n=== v6储能利用率分析 ===\n');
if isfield(cost_details, 'ess_utilization')
    fprintf('储能1利用率: %.1f%% (充放电次数: %.1f次/天)\n', ...
        cost_details.ess_utilization.ess1_rate*100, cost_details.ess_utilization.ess1_cycles);
    fprintf('储能2利用率: %.1f%% (充放电次数: %.1f次/天)\n', ...
        cost_details.ess_utilization.ess2_rate*100, cost_details.ess_utilization.ess2_cycles);
end

%% v6新增：风光利用率分析
fprintf('\n=== v6风光利用率分析 ===\n');
if isfield(cost_details, 'renewable_stats')
    fprintf('风电利用率: %.1f%% (弃风率: %.1f%%)\n', ...
        cost_details.renewable_stats.wind_utilization_rate, cost_details.renewable_stats.wind_curtailment_rate);
    fprintf('光伏利用率: %.1f%% (弃光率: %.1f%%)\n', ...
        cost_details.renewable_stats.pv_utilization_rate, cost_details.renewable_stats.pv_curtailment_rate);
    fprintf('风电预测: %.2f MWh, 实际上网: %.2f MWh\n', ...
        cost_details.renewable_stats.wind_forecast_mwh, cost_details.renewable_stats.wind_actual_mwh);
    fprintf('光伏预测: %.2f MWh, 实际上网: %.2f MWh\n', ...
        cost_details.renewable_stats.pv_forecast_mwh, cost_details.renewable_stats.pv_actual_mwh);
    fprintf('总购电量: %.2f MWh\n', cost_details.renewable_stats.total_purchase_mwh);
end

%% 保存结果
save('ESS_optimization_results_v6.mat', 'ps', 'pf', 'pf_norm', 'best_solution', 'cost_details');

%% 绘制结果
try
    % 绘制调度结果和储能充放电功率图
    plot_dispatch_results_v6(best_solution, cost_details, ESS_params);
    fprintf('\n调度结果图表已生成！\n');
    
    % 绘制Pareto解集和前沿图
    plot_pareto_results_v6(ps, pf, pf_norm, best_solution, best_idx);
    fprintf('Pareto前沿图表已生成！\n');
    
catch ME
    fprintf('绘图出错: %s\n', ME.message);
end

fprintf('\nv6.3版本优化完成！\n');
fprintf('主要改进:\n');
fprintf('1. 可削减负荷模型简化：P''cut,i = Pcut,i(1-βcut)\n');
fprintf('2. 削减负荷决策变量从192个减少到4个二进制变量\n');
fprintf('3. 每个节点一个βcut开关控制全天削减状态\n');
fprintf('4. 决策变量从210维降至%d维\n', n_var);
fprintf('5. 大幅提升算法收敛效率\n'); 