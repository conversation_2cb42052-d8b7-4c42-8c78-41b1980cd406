function plot_dispatch_results_separated(best_solution)
% 绘制分离的储能选址定容调度结果
% 图1：风光出力、购电量和负荷曲线
% 图2：储能充放电功率
global mpc_base Pwt Ppv price shift_load cut_load
global N_SL N_CL ac acq dc ESS_params

% 风光基准容量定义 (MW) - 增大装机容量
wind_base = [2.4, 2.0, 3.0]; % 各风电场额定容量 (增大2倍)
pv_base = [2.4, 3.0, 3.0];   % 各光伏电站额定容量 (增大3倍)

%% 解析最优解
ESS1_node = round(best_solution(1));
ESS2_node = round(best_solution(2));
ESS1_capacity = best_solution(3); % kWh
ESS2_capacity = best_solution(4); % kWh
ESS1_power = best_solution(5); % kW
ESS2_power = best_solution(6); % kW

% 风光实际上网功率 (注意：这里是实际上网功率，不是削减量)
idx = 7;
wind_actual = zeros(24, 3);  % 风电实际上网功率
pv_actual = zeros(24, 3);    % 光伏实际上网功率
for t = 1:24
    wind_actual(t,:) = best_solution(idx:idx+2);      % 风电实际上网功率 (MW)
    pv_actual(t,:) = best_solution(idx+3:idx+5);      % 光伏实际上网功率 (MW)
    idx = idx + 6;
end

% 储能充放电功率
ess_power = zeros(24, 2);
for t = 1:24
    ess_power(t,:) = best_solution(idx:idx+1);
    idx = idx + 2;
end

% 储能约束检查
[ess_power_corrected, ~] = ESS_constraint_check(ess_power, [ESS1_capacity, ESS2_capacity], [ESS1_power, ESS2_power]);

%% 计算各时段的电源出力和负荷
time_hours = 0:23;
total_load = zeros(24,1);
wind_output = zeros(24,1);
pv_output = zeros(24,1);
grid_power = zeros(24,1);

for t = 1:24
    % 总负荷计算
    total_load(t) = sum(ac(t,:)) + sum(dc(t,:));
    
    % 风光实际出力 - 直接使用决策变量（实际上网功率）
    wind_output(t) = sum(wind_actual(t,:));  % 直接求和实际上网功率
    pv_output(t) = sum(pv_actual(t,:));      % 直接求和实际上网功率
    
    % 电网交互功率 (正值为购电)
    ess1_net = -ess_power_corrected(t,1); % 储能净输出（放电为正）
    ess2_net = -ess_power_corrected(t,2);
    grid_power(t) = total_load(t) - wind_output(t) - pv_output(t) - ess1_net - ess2_net;
end

%% 图1：风光出力、购电量和负荷曲线
figure(2);
clf;

x_pos = 1:24;

% 准备堆叠数据 - 只包含风光和购电
supply_data = zeros(24, 3);
supply_data(:,1) = max(0, wind_output);     % 风电出力
supply_data(:,2) = max(0, pv_output);       % 光伏出力  
supply_data(:,3) = max(0, grid_power);      % 购电量

% 绘制堆叠柱状图
h_supply = bar(x_pos, supply_data, 0.8, 'stacked');

% 设置颜色 - 按用户要求
colors = [
    1.0, 0.5, 0.0;  % 风电 - 橙色
    0.6, 0.3, 0.0;  % 光伏 - 棕色
    1.0, 0.8, 0.0;  % 购电 - 橙黄色
];

for i = 1:3
    set(h_supply(i), 'FaceColor', colors(i,:));
end

hold on;

% 绘制负荷曲线
h_load = plot(x_pos, total_load, 'k-', 'LineWidth', 3, 'Marker', 'o', 'MarkerSize', 6);

% 图表设置
xlabel('时间 (h)');
ylabel('功率 (MW)');
title('风光出力、购电量与负荷曲线');
grid on;

% 设置x轴标签
xticks(1:2:24);
xticklabels(arrayfun(@(x) sprintf('%02d:00', x-1), 1:2:24, 'UniformOutput', false));

% 图例
legend([h_supply(1), h_supply(2), h_supply(3), h_load], ...
       {'风电出力', '光伏出力', '购电量', '负荷曲线'}, ...
       'Location', 'best');

% 添加统计信息
info_text = sprintf(['电源统计信息:\n' ...
    '总风电出力: %.1f MWh\n' ...
    '总光伏出力: %.1f MWh\n' ...
    '总购电量: %.1f MWh\n' ...
    '总负荷: %.1f MWh'], ...
    sum(wind_output), sum(pv_output), sum(max(0, grid_power)), sum(total_load));

text(0.02, 0.98, info_text, 'Units', 'normalized', ...
    'VerticalAlignment', 'top', 'BackgroundColor', 'white', ...
    'EdgeColor', 'black', 'FontSize', 9);

%% 图2：储能充放电功率
figure(3);
clf;

% 准备储能数据 - 正值为放电，负值为充电
ess1_power_mw = -ess_power_corrected(:,1); % 转换符号：放电为正，充电为负
ess2_power_mw = -ess_power_corrected(:,2);

% 绘制储能功率柱状图
bar_width = 0.35;
x_pos1 = x_pos - bar_width/2;
x_pos2 = x_pos + bar_width/2;

h_ess1 = bar(x_pos1, ess1_power_mw, bar_width);
hold on;
h_ess2 = bar(x_pos2, ess2_power_mw, bar_width);

% 设置颜色 - 按用户要求
set(h_ess1, 'FaceColor', [0.0, 0.0, 0.8]); % ESS1 - 深蓝色
set(h_ess2, 'FaceColor', [0.5, 0.7, 1.0]); % ESS2 - 浅蓝色

% 添加零线
yline(0, 'k--', 'LineWidth', 1);

% 图表设置
xlabel('时间 (h)');
ylabel('功率 (MW)');
title('储能充放电功率');
grid on;

% 设置x轴标签
xticks(1:2:24);
xticklabels(arrayfun(@(x) sprintf('%02d:00', x-1), 1:2:24, 'UniformOutput', false));

% 图例
legend([h_ess1, h_ess2], ...
       {sprintf('ESS1 (节点%d)', ESS1_node), sprintf('ESS2 (节点%d)', ESS2_node)}, ...
       'Location', 'best');

% 添加说明文字
text(0.02, 0.98, '正值：放电    负值：充电', 'Units', 'normalized', ...
    'VerticalAlignment', 'top', 'BackgroundColor', 'yellow', ...
    'EdgeColor', 'black', 'FontSize', 10, 'FontWeight', 'bold');

% 添加储能配置信息
ess_info_text = sprintf(['储能配置信息:\n' ...
    'ESS1: 节点%d, %.1f kWh, %.1f kW\n' ...
    'ESS2: 节点%d, %.1f kWh, %.1f kW\n' ...
    'ESS1充电量: %.2f MWh, 放电量: %.2f MWh\n' ...
    'ESS2充电量: %.2f MWh, 放电量: %.2f MWh'], ...
    ESS1_node, ESS1_capacity, ESS1_power, ...
    ESS2_node, ESS2_capacity, ESS2_power, ...
    sum(max(0, ess_power_corrected(:,1))), sum(max(0, -ess_power_corrected(:,1))), ...
    sum(max(0, ess_power_corrected(:,2))), sum(max(0, -ess_power_corrected(:,2))));

text(0.02, 0.02, ess_info_text, 'Units', 'normalized', ...
    'VerticalAlignment', 'bottom', 'BackgroundColor', 'white', ...
    'EdgeColor', 'black', 'FontSize', 9);

%% 输出调度结果统计
fprintf('\n=== 分离图表调度结果分析 ===\n');
fprintf('储能1: 节点%d, 容量%.1f kWh, 功率%.1f kW\n', ESS1_node, ESS1_capacity, ESS1_power);
fprintf('储能2: 节点%d, 容量%.1f kWh, 功率%.1f kW\n', ESS2_node, ESS2_capacity, ESS2_power);

% 计算各项统计数据
total_wind = sum(wind_output);
total_pv = sum(pv_output);
total_grid = sum(max(0, grid_power));
total_load_sum = sum(total_load);

% 储能统计
ess1_charge_total = sum(max(0, ess_power_corrected(:,1))); % 总充电量 MWh
ess1_discharge_total = sum(max(0, -ess_power_corrected(:,1))); % 总放电量 MWh
ess2_charge_total = sum(max(0, ess_power_corrected(:,2)));
ess2_discharge_total = sum(max(0, -ess_power_corrected(:,2)));

fprintf('总风电出力: %.2f MWh (%.1f%%)\n', total_wind, total_wind/total_load_sum*100);
fprintf('总光伏出力: %.2f MWh (%.1f%%)\n', total_pv, total_pv/total_load_sum*100);
fprintf('总购电量: %.2f MWh (%.1f%%)\n', total_grid, total_grid/total_load_sum*100);
fprintf('总负荷: %.2f MWh\n', total_load_sum);
fprintf('储能1总充电量: %.2f MWh\n', ess1_charge_total);
fprintf('储能1总放电量: %.2f MWh\n', ess1_discharge_total);
fprintf('储能2总充电量: %.2f MWh\n', ess2_charge_total);
fprintf('储能2总放电量: %.2f MWh\n', ess2_discharge_total);

% 能源平衡检查
energy_balance = total_wind + total_pv + total_grid + ess1_discharge_total + ess2_discharge_total - ess1_charge_total - ess2_charge_total;
fprintf('能源平衡检查 (应接近总负荷): %.2f MWh\n', energy_balance);
fprintf('平衡误差: %.3f MWh (%.2f%%)\n', abs(energy_balance - total_load_sum), abs(energy_balance - total_load_sum)/total_load_sum*100);

end

%% 辅助函数
function [ess_power_corrected, penalty_E] = ESS_constraint_check(ess_power, capacities, max_powers)
% 储能约束检查和修正
ess_power_corrected = ess_power;
penalty_E = 0;

for ess_idx = 1:2
    Ebat0 = 0.4 * capacities(ess_idx) / 1000; % 初始SOC 40%, 转换为MWh
    Emin = 0.16 * capacities(ess_idx) / 1000; % 最小SOC 16%
    Emax = 0.72 * capacities(ess_idx) / 1000; % 最大SOC 72%
    Pmax = max_powers(ess_idx) / 1000; % 转换为MW
    
    % 严格约束储能功率
    for t = 1:24
        if abs(ess_power_corrected(t, ess_idx)) > Pmax
            if ess_power_corrected(t, ess_idx) > Pmax
                ess_power_corrected(t, ess_idx) = Pmax;
            elseif ess_power_corrected(t, ess_idx) < -Pmax
                ess_power_corrected(t, ess_idx) = -Pmax;
            end
        end
    end
    
    Ebat = zeros(24,1);
    
    for t = 1:24
        % SOC约束
        if t == 1
            if ess_power_corrected(t, ess_idx) > 0
                Ebat(t) = Ebat0 + ess_power_corrected(t, ess_idx) * 0.9;
            else
                Ebat(t) = Ebat0 + ess_power_corrected(t, ess_idx) / 0.9;
            end
        else
            if ess_power_corrected(t, ess_idx) > 0
                Ebat(t) = Ebat(t-1) + ess_power_corrected(t, ess_idx) * 0.9;
            else
                Ebat(t) = Ebat(t-1) + ess_power_corrected(t, ess_idx) / 0.9;
            end
        end
        
        % SOC边界约束
        if Ebat(t) > Emax
            if t == 1
                ess_power_corrected(t, ess_idx) = (Emax - Ebat0) / 0.9;
            else
                ess_power_corrected(t, ess_idx) = (Emax - Ebat(t-1)) / 0.9;
            end
            Ebat(t) = Emax;
        elseif Ebat(t) < Emin
            if t == 1
                ess_power_corrected(t, ess_idx) = (Emin - Ebat0) * 0.9;
            else
                ess_power_corrected(t, ess_idx) = (Emin - Ebat(t-1)) * 0.9;
            end
            Ebat(t) = Emin;
        end
    end
end
end 