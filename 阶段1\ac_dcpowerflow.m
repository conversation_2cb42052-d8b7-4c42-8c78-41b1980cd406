function [ res ] = ac_dcpowerflow( mpc)
    %%初始数据
    baseMVA = mpc.baseMVA;
    %	bus_i	type	Pac	Qac	Pdc	area	Vm	baseKV	W	Vmax	Vmin
    bus1 = mpc.bus;
    bus=bus1;
    
    %	    bus	Pgac Qgac Pgdc	Qmax	Qmin	Vg
    gen1 = mpc.gen;
    gen=gen1;
    % gen_pv=gen1;
    %	fbus	tbus	r	x	D
    branch1 =  mpc.branch;
    branch=branch1;
    branch(:,3) = branch(:,3)+1j*branch(:,4);
    branch(:,4) = [];
    
    
    Tmax=30; %最大迭代次数
    limit=1.0e-6; %要求精度
    
    % 获取节点数和支路数
    n=size(bus,1);
    B=1:n;
    b=size(branch,1);
    nPQ=0;nPV=0;nBAN=0;
    %%对节点重新排序
    exch=zeros(1,n);%记录节点编号的变化
    j=1;
    for i=1:n
        if bus (i,2)==1%找出PQ节点个数
            nPQ=nPQ+1;
            bus(j,:)=bus(i,:);%i,j节点位置互换
            exch(i)=j;
            j=j+1;
        end
    end
    for i=1:n
        if bus1(i,2)==2%找出PV节点个数
            nPV=nPV+1;
            bus(j,:)=bus1(i,:);%i,j节点位置互换
            exch(i)=j;
            j=j+1;
        end
    end
    for i=1:n
        if bus1(i,2)==3%找出平衡节点
            nBAN=i;
            exch(i)=j;
            bus(n,:)=bus1(i,:);%平衡节点放到最后的位置
        end
    end
    %对应修改支路参数
    for j=1:b
        for m=1:2
            branch(j,m)=exch(branch(j,m));
        end
    end
    %发电机节点编号更新
    [~, col] = ismember(gen1(:,1)', bus(:,1));
    gen(:,1) = col;
    A=bus(:,1);%存入现有序号，后期还原用
    bus(:,1)=B;%新排列序号，按PQ，PV，平衡节点排列
    
    %%提取P,Q,U向量（初始值P、Q为0，电压为1，相角为0）
    P=zeros(1,n); %P,Q为原始数据，Pi,Qi为计算结果（所有节点都有P）
    Q=zeros(1,n);
    U=ones(1,n); %电压初始值由此确定
    cita=zeros(1,n); %此处未知节点皆设为1.0∠0 %注意：此处角度单位为度，提取后再转换成弧度，后面计算使用弧度
    
    for i=1:n%该循环将负荷i节点类型对应的数据输入P、Q、U
        if bus(i,9)==1%节点为直流节点
            [row, ~] = find(gen(:, 1) == i);
            a=~isempty(row);%该节点若有发电机则为1，反之为0
            if a==1%有发电机
                P(i)=(gen(row,4)-bus(i,5)+0.95*gen(row,2)-bus(i,3)/0.95)/baseMVA;
            else
                P(i)=(-bus(i,5)-bus(i,3)/0.95)/baseMVA;
            end
        else%节点为交流节点
            [row, ~] = find(gen(:, 1) == i);
            a=~isempty(row);%该节点若有发电机则为1，反之为0
            if a==1%有发电机
                P(i)=(gen(row,2)-bus(i,3)+0.95*gen(row,4)-bus(i,5)/0.95)/baseMVA;
                Q(i)=-bus(i,4)/(baseMVA);
            else
                P(i)=(-bus(i,3)-bus(i,5)/0.95)/baseMVA;
                Q(i)=-bus(i,4)/(baseMVA);
            end
        end
    end
    for i = 1:size(gen, 1)
        row=gen(i,1);
        U(row)=gen(i, 7);% 将U矩阵中对应元素所在的行用gen中第七列的元素替换
    end
    
    %%线路的导纳元素
    %构造节点导纳矩阵
    Y=zeros(n); %新建节点导纳矩阵
    y=zeros(n); %网络中的真实导纳
    %计算y(i,j)
    for i=1:size(branch,1) %与交流线联结的真实导纳
        ii=branch(i,1); jj=branch(i,2);
        y(ii,jj)=1/branch(i,3);
        y(jj,ii)=y(ii,jj);
    end
    %计算y(i,i),接地
    % for i=1:size(branch,1) %与交流线联结的对地导纳
    %     ii=branch(i,1); jj=branch(i,2);
    %     y(ii,ii)=y(ii,ii);
    %     y(jj,jj)=y(jj,jj);
    % end
    
    %由y计算Y
    ysum=sum(y,2); %每一行求和
    for i=1:n
        for j=1:n
            if i==j
                Y(i,j)=ysum(i);
            else
                Y(i,j)=-y(i,j);
            end
        end
    end
    
    
    G=real(Y); %电导矩阵
    B=imag(Y); %电纳矩阵
    
    
    %%NR迭代
    
    % 创建一个33x33的关联矩阵T，描述节点之间是否存在联系
    T = zeros(33);
    % 根据支路数据设置U矩阵中的连接情况
    for i = 1:size(branch, 1)
        from_node = branch(i, 1);
        to_node = branch(i, 2);
        T(from_node, to_node) = 1;
        T(to_node, from_node) = 1;
        T(i,i)=1;
    end
    
    % 创建一个33x33的接线矩阵D，描述节点之间采用DC连接还是AC连接
    D = zeros(33);
    % 根据矩阵a中的连接方式设置B矩阵中的元素
    for i = 1:size(branch, 1)
        from_node = branch(i, 1);
        to_node = branch(i, 2);
        connection_type = branch(i, 4);
        D(from_node, to_node) = connection_type;
        D(to_node, from_node) = connection_type;
    end
    
    %生成系统中VSC调制矩阵M
    M=ones(33);
    L=find(branch(:, 5) ~= 0);
    for i = 1:size(L)
        from_node = branch(L(i), 1);
        to_node = branch(L(i), 2);
        M_type = branch(L(i), 5);
        M(from_node, to_node) = M_type;
        M(to_node, from_node) = M_type;
    end
    %生成系统中VSC控制角M_cita
    M_cita=ones(33);
    L=find(branch(:, 6) ~= 0);
    for i = 1:size(L)
        from_node = branch(L(i), 1);
        to_node = branch(L(i), 2);
        M_cita1 = branch(L(i), 6);
        M_cita(from_node, to_node) = M_cita1 ;
        M_cita(to_node, from_node) = M_cita1 ;
    end
    
    % M(5,6)=0.99;M(6,5)=0.99;
    % M(4,23)=0.97;M(23,4)=0.97;
    % M(9,10)=0.97;M(10,9)=0.97;
    % M(13,14)=0.96;M(14,13)=0.96;
    % M(25,32)=0.98;M(32,25)=0.98;
    % M(16,29)=0.96;M(29,16)=0.96;
    % M(18,19)=0.99;M(19,18)=0.99;
    %生成矩阵W
    W=bus(:,9);
    
    %利用M更新交流端的U
    M_position=find(branch(:, 5) ~= 0);%存在VSC的位置
    M_variable=branch(M_position, 5);
    M_row=branch(M_position,1:2);%VSC位置对应的节点
    ac_position=bus(bus(:,9)==0,1);%ac节点编号
    ac_point = zeros(length(M_row),1);
    dc_point=zeros(length(M_row),1);
    % 遍历矩阵A的每行
    for i = 1:size(M_row, 1)
        % 遍历矩阵A当前行的元素
        for j = 1:size(M_row, 2)
            % 查找当前元素是否在矩阵B中存在
            if ismember(M_row(i, j), ac_position)
                % 将存在的元素添加到结果矩阵中
                ac_point(i) = M_row(i, j);
            else
                dc_point(i)= M_row(i, j);
            end
        end
    end
    U(ac_point')=U(dc_point').*M_variable';
    for i = 1:nPV
        row=gen(i+1,1);
        U(row)=gen(i+1, 7);% 发电机机端电压更正
    end
    % gen_pv=gen;
    %计算功率不平衡量
    [dP,dQ,Pi,Qi]=Unbalanced( n,nPQ,P,Q,U,G,B,cita,T,D,M,M_cita,W);
    
    % %校验PV节点是否需要向PQ节点转化
    % for ii=1:size(gen_pv,1)
    %     if bus(gen_pv(ii,1),2)==2
    %         gen_pv(ii,3)= Qi(gen_pv(ii,1))*baseMVA+bus(gen_pv(ii,1),4)';
    %         if gen_pv(ii,3)>gen_pv(ii,5)||gen_pv(ii,3)<gen_pv(ii,6)%PV节点Q越限，则PV节点转化为PQ节点，将Q限制为上下限
    %             bus(gen_pv(ii,1),2)=1;
    %             nPQ=sum(bus(:,2)==1);
    %             gen_pv(ii,3)=gen_pv(ii,5);
    %             %                 bus(gen_pv(ii,1),4)=gen_pv(ii,3)-Qi(gen_pv(ii,1))*baseMVA;
    %             Q(gen_pv(ii,1))=(gen_pv(ii,3)-Qi(gen_pv(ii,1))*baseMVA)/(baseMVA);
    %         end
    %     end
    % end
    % %计算功率不平衡量
    % [dP,dQ,Pi,Qi]=Unbalanced( n,nPQ,P,Q,U,G,B,cita,T,D,M,W);
    
    %主循环
    for i=1:Tmax
    
        %雅可比矩阵的计算
        J=Jacobi( n,nPQ,U,cita,B,G,Pi,Qi ,T,D,M,M_cita,W);
    
    %校验PV节点是否需要向PQ节点转化
    % if i<=Tmax
    %     if i>=2
    %         for ii=1:size(gen_pv,1)
    %             if bus(gen_pv(ii,1),2)==2
    %                 gen_pv(ii,3)= Qi(gen_pv(ii,1))*baseMVA+bus(gen_pv(ii,1),4)';
    %                 if gen_pv(ii,3)<gen_pv(ii,6)%PV节点Q越限，则PV节点转化为PQ节点，将Q限制为上下限
    %                     U(gen_pv(ii,1))=U(gen_pv(ii,1))+0.01;
    %                 elseif gen_pv(ii,3)>gen_pv(ii,6)
    %                     U(gen_pv(ii,1))=U(gen_pv(ii,1))+0.01;
    %                 else,end
    %             end
    %         end
    %     end
    % end
    
    
    
    
        %求解节点电压修正量
        [dU,dcita]=Correct( n,nPQ,U,dP,dQ,J,W );
    
        %修正节点电压
        U=U-dU;
        cita=cita-dcita;
    
        %计算功率不平衡量
    %     [dP,dQ,Pi,Qi]=Unbalanced( n,nPQ,P,Q,U,G,B,cita,T,D,M,W);
    % 
        
        %计算功率不平衡量
        [dP,dQ,Pi,Qi]=Unbalanced( n,nPQ,P,Q,U,G,B,cita,T,D,M,M_cita,W);
    
        if (max(abs(dP))<limit && max(abs(dQ))<limit )
            break;
        end%if
    end%for
    
    %将U与cita还原为原来节点顺序
    bus(:,7)=U;
    bus(:,6)=cita;
    bus(:,1)=A;
    bus=sortrows(bus, 1);
    U=bus(:,7)';
    cita=bus(:,6)';
    Pi(2,:)=A';
    Qi(2,:)=A';
    Pi=Pi';
    Qi=Qi';
    Pi=sortrows(Pi, 2);
    Qi=sortrows(Qi, 2);
    Pi=Pi';
    Pi(2,:)=[];
    Qi=Qi';
    Qi(2,:)=[];
    for i=1:size(gen1,1)
        if bus(gen1(i,1),2)==2
            gen1(i,3)= Qi(gen1(i,1))*baseMVA+bus1(gen1(i,1),4)';
        end
    end
    gen1(1,3)=Qi(1)*baseMVA;
    gen1(1,2)=Pi(1)*baseMVA;
    gen1(:,7)=U(gen1(:,1));
    
    %计算VSC的S
    % if all(W(:) == 0)
    %     S=[];
    % else
    S=S_vsc( n,U,bus,cita,branch1);
    % end
    %输出结果（网损，gen,bus）
    res = struct();
    res.loss=sum(gen1(:,2))+sum(gen1(:,4))-(sum(bus1(:,3))+sum(bus1(:,5)));
    res.bus=bus;
    res.gen=gen1;
    res.nPV=nPV;
    res.S=S;
    %迭代结束，判断收敛
    if (max(abs(dP))<limit && max(abs(dQ))<limit )
        res.loss=res.loss;
    else
        res.loss=100000;
    end
    end