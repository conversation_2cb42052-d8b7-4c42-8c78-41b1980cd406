function plot_dispatch_results_v2(solution)
%% 绘制交直流柔性配电网储能优化调度结果 (新格式)
% 按照用户要求的堆叠柱状图格式

global mpc_base Pwt Ppv price shift_load cut_load Load_ac Load_dc
global N_SL N_CL Nwt Npv ac acq dc ESS_params

% 解析决策变量
ESS1_node = round(solution(1));
ESS2_node = round(solution(2));
ESS1_capacity = solution(3); % kWh
ESS2_capacity = solution(4); % kWh
ESS1_power = solution(5); % kW
ESS2_power = solution(6); % kW

% 提取储能充放电功率 (MW)
idx = 7 + 24*6; % 跳过风光削减变量
P_ESS = zeros(24, 2);
for t = 1:24
    P_ESS(t,1) = solution(idx + (t-1)*2 + 1); % 储能1功率
    P_ESS(t,2) = solution(idx + (t-1)*2 + 2); % 储能2功率
end

% 提取风光削减量
idx = 7;
wind_curtail = zeros(24, 3);
pv_curtail = zeros(24, 3);
for t = 1:24
    wind_curtail(t,:) = solution(idx:idx+2);
    pv_curtail(t,:) = solution(idx+3:idx+5);
    idx = idx + 6;
end

% 计算实际风光出力
wind_actual = max(0, Pwt - wind_curtail); % kW
pv_actual = max(0, Ppv - pv_curtail); % kW
wind_total = sum(wind_actual, 2); % 总风电出力
pv_total = sum(pv_actual, 2); % 总光伏出力

% 计算总负荷
total_load_ac = sum(ac, 2); % AC负荷 MW
total_load_dc = sum(dc, 2); % DC负荷 MW
total_load = total_load_ac + total_load_dc; % 总负荷 MW

% 计算向上级电网购电量 (功率平衡)
% 购电量 = 总负荷 - 风电出力 - 光伏出力 - 储能放电 + 储能充电
P_grid = total_load - wind_total/1000 - pv_total/1000 - sum(P_ESS, 2);

% 储能SOC计算
SOC = zeros(24, 2);
SOC_min = 0.16; % 16%
SOC_max = 0.72; % 72%
SOC_init = 0.4; % 初始SOC 40%

for ess = 1:2
    if ess == 1
        capacity = ESS1_capacity;
    else
        capacity = ESS2_capacity;
    end
    
    if capacity > 0
        SOC(1, ess) = SOC_init;
        for t = 2:24
            % 充电为负，放电为正
            delta_E = -P_ESS(t-1, ess) * 1; % 1小时，MW转换为MWh
            SOC(t, ess) = SOC(t-1, ess) + delta_E / (capacity/1000); % 容量转换为MWh
            SOC(t, ess) = max(SOC_min, min(SOC_max, SOC(t, ess)));
        end
    end
end

%% 创建堆叠柱状图
figure(3);
set(gcf, 'Position', [200, 200, 1400, 800]);

% 时间轴
time_hours = 0:23;

% 准备数据 (转换为MW)
ESS1_data = P_ESS(:,1); % 储能1功率 (MW)
ESS2_data = P_ESS(:,2); % 储能2功率 (MW)
wind_data = wind_total / 1000; % 风电功率 (MW)
pv_data = pv_total / 1000; % 光伏功率 (MW)
grid_data = max(0, P_grid); % 购电量 (MW)
load_data = total_load; % 负荷 (MW)

% 分离充电和放电
ESS1_charge = min(0, ESS1_data); % 充电 (负值)
ESS1_discharge = max(0, ESS1_data); % 放电 (正值)
ESS2_charge = min(0, ESS2_data); % 充电 (负值)
ESS2_discharge = max(0, ESS2_data); % 放电 (正值)

% 创建堆叠数据矩阵
% 正值部分：放电、风电、光伏、购电
positive_data = [ESS1_discharge, ESS2_discharge, wind_data, pv_data, grid_data];

% 负值部分：充电
negative_data = [ESS1_charge, ESS2_charge];

% 绘制正值堆叠柱状图
h_pos = bar(time_hours, positive_data, 'stacked', 'BarWidth', 0.8);

% 设置颜色
colors = [
    0.6, 0.8, 0.4;  % ESS1放电 - 浅绿色
    0.4, 0.7, 0.6;  % ESS2放电 - 青绿色
    0.8, 0.4, 0.2;  % 风电 - 橙红色
    0.4, 0.2, 0.6;  % 光伏 - 紫色
    0.9, 0.8, 0.4;  % 购电 - 黄色
];

for i = 1:5
    h_pos(i).FaceColor = colors(i,:);
    h_pos(i).EdgeColor = 'black';
    h_pos(i).LineWidth = 0.5;
end

hold on;

% 绘制负值堆叠柱状图 (充电)
h_neg = bar(time_hours, negative_data, 'stacked', 'BarWidth', 0.8);
h_neg(1).FaceColor = [0.6, 0.8, 0.4]; % ESS1充电
h_neg(2).FaceColor = [0.4, 0.7, 0.6]; % ESS2充电
h_neg(1).EdgeColor = 'black';
h_neg(2).EdgeColor = 'black';
h_neg(1).LineWidth = 0.5;
h_neg(2).LineWidth = 0.5;

% 绘制负荷曲线
plot(time_hours, load_data, 'b-', 'LineWidth', 2, 'Marker', 's', 'MarkerSize', 4, 'MarkerFaceColor', 'blue');

% 设置坐标轴
xlabel('时间', 'FontSize', 12, 'FontWeight', 'bold');
ylabel('有功功率 (MW)', 'FontSize', 12, 'FontWeight', 'bold');
title('交直流柔性配电网储能优化调度结果', 'FontSize', 14, 'FontWeight', 'bold');

% 设置x轴刻度
xticks(0:3:23);
xticklabels({'00:00', '03:00', '06:00', '09:00', '12:00', '15:00', '18:00', '21:00', '24:00'});

% 添加图例
legend_labels = {
    sprintf('ESS1 (节点%d)', ESS1_node),
    sprintf('ESS2 (节点%d)', ESS2_node),
    '风电',
    '光伏',
    '购电量',
    '负荷'
};

legend(legend_labels, 'Location', 'northeast', 'FontSize', 10);

% 设置网格
grid on;
grid minor;

% 设置y轴范围
y_max = max([max(sum(positive_data,2)), max(load_data)]) * 1.1;
y_min = min(sum(negative_data,2)) * 1.1;
ylim([y_min, y_max]);

%% 绘制储能SOC变化曲线
figure(4);
set(gcf, 'Position', [300, 300, 1000, 600]);

plot(time_hours, SOC(:,1)*100, 'b-', 'LineWidth', 2, 'Marker', 'o', 'MarkerSize', 6);
hold on;
plot(time_hours, SOC(:,2)*100, 'r-', 'LineWidth', 2, 'Marker', 's', 'MarkerSize', 6);

% 添加SOC限制线
yline(SOC_min*100, '--k', 'LineWidth', 1.5, 'Alpha', 0.7);
yline(SOC_max*100, '--g', 'LineWidth', 1.5, 'Alpha', 0.7);

xlabel('时间 (h)', 'FontSize', 12, 'FontWeight', 'bold');
ylabel('SOC (%)', 'FontSize', 12, 'FontWeight', 'bold');
title('储能系统SOC变化曲线', 'FontSize', 14, 'FontWeight', 'bold');

legend_labels = {
    sprintf('储能1 (节点%d)', ESS1_node),
    sprintf('储能2 (节点%d)', ESS2_node),
    '最小SOC',
    '最大SOC'
};

legend(legend_labels, 'Location', 'best', 'FontSize', 10);
grid on;
xlim([0, 23]);
ylim([10, 80]);

%% 输出调度结果统计
fprintf('\n=== 调度结果分析 ===\n');
fprintf('储能1: 节点%d, 容量%.1f kWh, 功率%.1f kW\n', ESS1_node, ESS1_capacity, ESS1_power);
fprintf('储能2: 节点%d, 容量%.1f kWh, 功率%.1f kW\n', ESS2_node, ESS2_capacity, ESS2_power);

fprintf('总风电出力: %.2f MWh\n', sum(wind_total)/1000);
fprintf('总光伏出力: %.2f MWh\n', sum(pv_total)/1000);
fprintf('总购电量: %.2f MWh\n', sum(max(0, P_grid)));

% 储能充放电统计
ESS1_charge_total = sum(max(0, -P_ESS(:,1)));
ESS1_discharge_total = sum(max(0, P_ESS(:,1)));
ESS2_charge_total = sum(max(0, -P_ESS(:,2)));
ESS2_discharge_total = sum(max(0, P_ESS(:,2)));

fprintf('储能1总充电量: %.2f MWh\n', ESS1_charge_total);
fprintf('储能1总放电量: %.2f MWh\n', ESS1_discharge_total);
fprintf('储能2总充电量: %.2f MWh\n', ESS2_charge_total);
fprintf('储能2总放电量: %.2f MWh\n', ESS2_discharge_total);

% 计算储能利用率
if ESS1_capacity > 0
    ESS1_utilization = (ESS1_charge_total + ESS1_discharge_total) / (ESS1_capacity/1000) * 100;
    fprintf('储能1利用率: %.1f%%\n', ESS1_utilization);
end

if ESS2_capacity > 0
    ESS2_utilization = (ESS2_charge_total + ESS2_discharge_total) / (ESS2_capacity/1000) * 100;
    fprintf('储能2利用率: %.1f%%\n', ESS2_utilization);
end

end 