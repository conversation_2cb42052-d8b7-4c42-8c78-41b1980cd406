%% 快速测试修复效果
% 验证修复后的储能调度策略和大容量配置

clear; clc; close all;

% 全局变量声明
global mpc_base Pwt Ppv price shift_load cut_load Load_ac Load_dc
global N_SL N_CL Nwt Npv ac acq dc ESS_params

% 修复后的储能参数设置
ESS_params.Ce = 200;        % 储能单位容量成本 (元/kWh)
ESS_params.Cp = 100;        % 储能单位功率成本 (元/kW)
ESS_params.Cmaint = 50;     % 年运行维护成本 (元/kW·年)
ESS_params.ir = 0.015;
ESS_params.dr = 0.09;
ESS_params.Y = 10;
ESS_params.lambda = 0.08;
ESS_params.Cde = 60;
ESS_params.E_max = 25000;
ESS_params.P_max = 8000;

% 加载数据
load_data_v6;

fprintf('=== 快速修复效果测试 ===\n\n');

%% 测试超大容量配置
% 构造测试解：25MWh总容量，6MW总功率
n_var = 2 + 2 + 2 + N_SL + N_CL;
solution_test = zeros(1, n_var);
solution_test(1:2) = [30, 5];                    % 储能安装节点
solution_test(3:4) = [12500, 12500];             % 储能容量 12.5MWh each
solution_test(5:6) = [4.0, 4.0];                 % 功率配比 4h

% 平移负荷时间设置
idx = 7;
if N_SL > 0
    solution_test(idx:idx+N_SL-1) = shift_load(:,4)';
    idx = idx + N_SL;
end

% 削减负荷开关设置
if N_CL > 0
    solution_test(idx:idx+N_CL-1) = 0;
end

fprintf('测试储能配置:\n');
fprintf('ESS1: %.0f kWh, %.0f kW\n', solution_test(3), solution_test(3)/solution_test(5));
fprintf('ESS2: %.0f kWh, %.0f kW\n', solution_test(4), solution_test(4)/solution_test(6));
fprintf('总容量: %.1f MWh\n', sum(solution_test(3:4))/1000);
fprintf('总功率: %.1f MW\n', sum(solution_test(3:4)./solution_test(5:6))/1000);

%% 运行目标函数
[f1, f2, cost_details] = ESS_objective_v6(solution_test);

fprintf('\n运行结果:\n');
fprintf('储能全寿命周期成本: %.2f 万元\n', f1/10000);
fprintf('系统运行成本: %.2f 万元\n', f2/10000);
fprintf('弃风弃光成本: %.2f 万元\n', cost_details.curtail_cost/10000);

if isfield(cost_details, 'renewable_stats')
    fprintf('风电利用率: %.1f%% (弃风率: %.1f%%)\n', ...
        cost_details.renewable_stats.wind_utilization_rate, ...
        cost_details.renewable_stats.wind_curtailment_rate);
    fprintf('光伏利用率: %.1f%% (弃光率: %.1f%%)\n', ...
        cost_details.renewable_stats.pv_utilization_rate, ...
        cost_details.renewable_stats.pv_curtailment_rate);
end

if isfield(cost_details, 'ess_utilization')
    fprintf('储能1利用率: %.1f%%\n', cost_details.ess_utilization.ess1_rate*100);
    fprintf('储能2利用率: %.1f%%\n', cost_details.ess_utilization.ess2_rate*100);
end

%% 检查是否达到目标
curtailment_rate = 0;
if isfield(cost_details, 'renewable_stats')
    curtailment_rate = cost_details.renewable_stats.wind_curtailment_rate;
end

fprintf('\n=== 目标达成检查 ===\n');
if curtailment_rate <= 5.0
    fprintf('🎉 成功！弃电率%.1f%%已达到5%%以下目标！\n', curtailment_rate);
else
    fprintf('⚠️  弃电率%.1f%%仍高于5%%目标\n', curtailment_rate);
end

%% 调度逻辑检查
if isfield(cost_details, 'hourly_data')
    P_curtail = cost_details.hourly_data.P_curtail;
    ess_power = cost_details.hourly_data.ess_power;
    
    fprintf('\n=== 调度逻辑检查 ===\n');
    logic_issues = 0;
    for t = 1:24
        if P_curtail(t) > 0.01
            total_ess_power = sum(ess_power(t,:));
            if total_ess_power >= 0  % 储能不在充电
                logic_issues = logic_issues + 1;
                if logic_issues <= 3
                    fprintf('时刻%02d:00: 有弃电%.3f MW，但储能功率%.3f MW\n', ...
                        t-1, P_curtail(t), total_ess_power);
                end
            end
        end
    end
    
    if logic_issues == 0
        fprintf('✅ 调度逻辑修复成功！\n');
    else
        fprintf('❌ 仍有%d个调度逻辑问题\n', logic_issues);
    end
    
    curtailment_hours = find(P_curtail > 0.01);
    fprintf('弃电时段数: %d 小时\n', length(curtailment_hours));
    fprintf('总弃电量: %.2f MWh\n', sum(P_curtail));
end

%% 经济性分析
fprintf('\n=== 经济性分析 ===\n');

% 与不配置储能对比
solution_no_ess = zeros(1, n_var);
solution_no_ess(1:2) = [10, 15];
solution_no_ess(3:4) = [0, 0];
solution_no_ess(5:6) = [3, 3];

idx = 7;
if N_SL > 0
    solution_no_ess(idx:idx+N_SL-1) = shift_load(:,4)';
    idx = idx + N_SL;
end
if N_CL > 0
    solution_no_ess(idx:idx+N_CL-1) = 0;
end

[f1_no_ess, f2_no_ess, cost_details_no_ess] = ESS_objective_v6(solution_no_ess);

fprintf('不配置储能:\n');
fprintf('  弃风弃光率: %.1f%%\n', cost_details_no_ess.renewable_stats.wind_curtailment_rate);
fprintf('  弃风弃光成本: %.2f 万元\n', cost_details_no_ess.curtail_cost/10000);

fprintf('\n配置储能后:\n');
fprintf('  弃风弃光率: %.1f%%\n', curtailment_rate);
fprintf('  弃风弃光成本: %.2f 万元\n', cost_details.curtail_cost/10000);
fprintf('  储能投资成本: %.2f 万元\n', f1/10000);

% 计算效益
annual_saving = (cost_details_no_ess.curtail_cost - cost_details.curtail_cost) * 365;
annual_ess_cost = f1 / ESS_params.Y;

fprintf('\n效益分析:\n');
fprintf('  年弃电成本节省: %.2f 万元\n', annual_saving/10000);
fprintf('  年化储能成本: %.2f 万元\n', annual_ess_cost/10000);

if annual_saving > 0
    payback_period = annual_ess_cost / annual_saving;
    fprintf('  投资回收期: %.1f 年\n', payback_period);
end

curtailment_improvement = cost_details_no_ess.renewable_stats.wind_curtailment_rate - curtailment_rate;
fprintf('  弃电率改善: %.1f个百分点\n', curtailment_improvement);

%% 总结
fprintf('\n=== 修复效果总结 ===\n');

if curtailment_rate <= 5.0 && logic_issues == 0
    fprintf('🎊 完全成功！\n');
    fprintf('1. 弃电率目标达成: %.1f%% ≤ 5%%\n', curtailment_rate);
    fprintf('2. 调度逻辑修复成功\n');
    fprintf('3. 经济性良好: 投资回收期%.1f年\n', payback_period);
elseif curtailment_rate <= 5.0
    fprintf('🎉 基本成功！\n');
    fprintf('1. 弃电率目标达成: %.1f%% ≤ 5%%\n', curtailment_rate);
    fprintf('2. 调度逻辑仍需优化\n');
elseif logic_issues == 0
    fprintf('⚠️  部分成功\n');
    fprintf('1. 调度逻辑修复成功\n');
    fprintf('2. 弃电率%.1f%%仍需进一步降低\n', curtailment_rate);
else
    fprintf('❌ 需要继续优化\n');
    fprintf('1. 弃电率%.1f%%高于目标\n', curtailment_rate);
    fprintf('2. 调度逻辑仍有问题\n');
end

fprintf('\n关键改进措施:\n');
fprintf('1. 储能容量: %.1f MWh (大幅提升)\n', sum(solution_test(3:4))/1000);
fprintf('2. 储能功率: %.1f MW (大幅提升)\n', sum(solution_test(3:4)./solution_test(5:6))/1000);
fprintf('3. 成本降低: 容量成本%.0f元/kWh, 功率成本%.0f元/kW\n', ESS_params.Ce, ESS_params.Cp);
fprintf('4. 弃电成本: 5.0元/kWh (强化激励)\n');
fprintf('5. SOC优化: 初始5%%, 上限99.5%%\n');

fprintf('\n=== 测试完成 ===\n');
