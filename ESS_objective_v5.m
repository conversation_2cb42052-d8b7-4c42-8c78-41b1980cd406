function [f1, f2, cost_details] = ESS_objective_v5(X)
% 储能选址定容双目标函数 - v5版本：储能风光协调优化
% 核心改进：储能功率容量强关联 + 储能充放电与风光剩余功率协调
% 输入: X - 决策变量向量
% 输出: f1 - 储能全寿命周期成本 (元)
%       f2 - 系统运行成本 (元)
%       cost_details - 成本详细信息结构体

global mpc_base Pwt Ppv price shift_load cut_load Load_ac Load_dc
global N_SL N_CL Nwt Npv ac acq dc ESS_params

% 风光基准容量定义 (MW) - v5版本修正：与主程序保持一致
wind_base = [2.16, 1.8, 2.7]; % 风电基准容量 (1.8倍)
pv_base = [2.0, 2.5, 2.5];    % 光伏基准容量 (2.5倍)

%% v5新增：解析决策变量 - 储能功率容量强关联，支持2套储能
ESS1_node = round(X(1));
ESS2_node = round(X(2));
ESS1_capacity = X(3); % kWh
ESS2_capacity = X(4); % kWh
ESS1_hour_ratio = X(5); % 小时比 (2-6h)
ESS2_hour_ratio = X(6); % 小时比 (2-6h)

% v5核心：通过小时比计算功率，天生满足配比约束
ESS1_power = ESS1_capacity / ESS1_hour_ratio; % kW
ESS2_power = ESS2_capacity / ESS2_hour_ratio; % kW

% 功率容量配比惩罚 = 0 (因为强制满足约束)
capacity_power_penalty = 0;

% 风光实际上网功率解析
wind_actual = reshape(X(7:150), 24, 6);  % 24×6矩阵
pv_actual = wind_actual(:,4:6);           % 光伏实际上网功率 (MW)
wind_actual = wind_actual(:,1:3);         % 风电实际上网功率 (MW)

% === v5改进：删除最低消纳率约束，让市场机制自然调节 ===
% 删除强制消纳率约束，通过弃风弃光成本来引导优化
% 这样可以让算法根据经济性自主决定最优的风光消纳水平
% penalty_min_utilization = 0 (不再计算最低消纳率惩罚)
% ===============================================================

% === v5改进：更灵活的储能充放电策略 ======================================
% 价格驱动触发策略：放宽触发条件，提高储能利用率
prc_low  = 0.35;   % 降低谷价阈值，覆盖更多谷时段
prc_high = 0.75;   % 提高峰价阈值，覆盖更多峰时段

ess_coeff = reshape(X(151:198), 24, 2); % 24×2，储能充放电系数 (-1~1，2套储能)

% 计算 24h 的净负荷 (MW) = 交流负荷 + 直流负荷 - 已接纳的可再生出力
net_load = Load_ac' + Load_dc' - (sum(wind_actual, 2) + sum(pv_actual, 2));
net_load(net_load < 0) = 0;                       % 若出现反向功率，按 0 处理
peak_net_load = max(net_load);                    % 峰值净负荷

% 初始化输出变量
ess_power = zeros(24, 2);
renewable_surplus = zeros(24, 1); % 可再生能源剩余功率 (弃电潜力)

for t = 1:24
    % 可再生能源预测 & 剩余功率
    total_renewable_forecast = sum(Pwt(t,:) .* wind_base) + sum(Ppv(t,:) .* pv_base);
    total_renewable_actual   = sum(wind_actual(t,:)) + sum(pv_actual(t,:));
    renewable_surplus(t)     = max(0, total_renewable_forecast - total_renewable_actual);

    % 更灵活的触发条件判断
    allow_discharge = price(t) >= prc_high;                                  % 峰价放电
    allow_charge    = (renewable_surplus(t) > 0.01) || (price(t) <= prc_low); % 有弃电或谷价时充电
    
    % 新增：基于负荷需求的充放电策略
    high_demand = net_load(t) > 0.7 * peak_net_load; % 高负荷时段
    low_demand = net_load(t) < 0.3 * peak_net_load;  % 低负荷时段
    
    % 扩展触发条件
    allow_discharge = allow_discharge || high_demand; % 高负荷时也允许放电
    allow_charge = allow_charge || low_demand;        % 低负荷时也允许充电

    % 基于触发条件修正 ess_coeff，但不完全禁止
    for ess_idx = 1:2
        if ess_coeff(t, ess_idx) > 0 && ~allow_discharge
            ess_coeff(t, ess_idx) = ess_coeff(t, ess_idx) * 0.3; % 减弱而非完全禁止
        elseif ess_coeff(t, ess_idx) < 0 && ~allow_charge
            ess_coeff(t, ess_idx) = ess_coeff(t, ess_idx) * 0.3; % 减弱而非完全禁止
        end
    end

    % 在触发逻辑后再计算储能功率 (MW)
    for ess_idx = 1:2
        if ess_idx == 1
            max_power_kw = ESS1_power;
        else
            max_power_kw = ESS2_power;
        end
        max_power_mw = max_power_kw / 1000;                                       % 转换为 MW

        if ess_coeff(t, ess_idx) < 0                                    % 充电
            % 充电不再严格受限于弃电量，允许谷价充电
            if renewable_surplus(t) > 0.01
                charge_pwr = min(abs(ess_coeff(t, ess_idx)) * max_power_mw, ...
                                renewable_surplus(t));                   % 优先消纳弃电
            else
                charge_pwr = abs(ess_coeff(t, ess_idx)) * max_power_mw * 0.8; % 谷价充电
            end
            ess_power(t, ess_idx) = -charge_pwr;                         % 负值表示充电
        elseif ess_coeff(t, ess_idx) > 0                                 % 放电
            % 放电功率随价格和负荷需求进行加权
            price_factor = min(1, max(0, (price(t) - prc_low) / (prc_high - prc_low)));
            demand_factor = min(1, net_load(t) / peak_net_load);
            combined_factor = max(price_factor, demand_factor);
            ess_power(t, ess_idx) = ess_coeff(t, ess_idx) * max_power_mw * (0.5 + 0.5 * combined_factor);
        else                                                             % 系数为 0
            ess_power(t, ess_idx) = 0;
        end
    end
end

% v5新增：储能约束检查 - 使用改进的约束检查函数，支持2套储能
[ess_power, penalty_E] = ESS_constraint_check_v5(ess_power, [ESS1_capacity, ESS2_capacity], [ESS1_power, ESS2_power]);

% 平移负荷时间
shift_time_vars = X(199:199+N_SL-1);

% 削减负荷量和开关
cut_amount = reshape(X(199+N_SL:199+N_SL+95), 24, N_CL);
cut_switch = reshape(X(199+N_SL+96:end), 24, N_CL);
cut_switch = sigmoid(cut_switch);

%% 目标函数1：储能全寿命周期成本
% 储能投资成本 (元)
C_e = ESS_params.Ce;
C_p = ESS_params.Cp;
C_i = (ESS1_capacity + ESS2_capacity) * C_e + (ESS1_power + ESS2_power) * C_p;

% 运维成本 (元)
C_maint = ESS_params.Cmaint;
i_r = ESS_params.ir;
d_r = ESS_params.dr;
Y = ESS_params.Y;

C_m = 0;
for y = 1:Y
    C_m = C_m + C_maint * (ESS1_power + ESS2_power) * (1 + i_r)^y / (1 + d_r)^y;
end

% 报废折旧成本 (元)
lambda = ESS_params.lambda;
C_d = lambda * (ESS1_capacity + ESS2_capacity) * C_e / (1 + d_r)^Y;

f1 = C_i + C_m + C_d + capacity_power_penalty;

%% 目标函数2：系统运行成本
% 构建修改后的配电网模型
mpc_modified = cell(24,1);
for t = 1:24
    mpc_modified{t} = mpc_base;
    
    % 修改负荷
    mpc_modified{t}.bus(:,3) = ac(t,:)';
    mpc_modified{t}.bus(:,4) = acq(t,:)';
    mpc_modified{t}.bus(:,5) = dc(t,:)';
    
    % 风电注入
    mpc_modified{t}.bus([5,24,29],3) = mpc_modified{t}.bus([5,24,29],3) - wind_actual(t,:)';
    mpc_modified{t}.bus([5,24,29],4) = mpc_modified{t}.bus([5,24,29],4) - wind_actual(t,:)' .* tan(acos(0.95));
    
    % 光伏注入
    mpc_modified{t}.bus([10,17,22],5) = mpc_modified{t}.bus([10,17,22],5) - pv_actual(t,:)';
    
    % 储能注入
    if ESS1_node <= 33 && ESS1_node >= 2
        if mpc_modified{t}.bus(ESS1_node, 9) == 1 % DC节点
            mpc_modified{t}.bus(ESS1_node, 5) = mpc_modified{t}.bus(ESS1_node, 5) + ess_power(t,1);
        else % AC节点
            mpc_modified{t}.bus(ESS1_node, 3) = mpc_modified{t}.bus(ESS1_node, 3) + ess_power(t,1);
            mpc_modified{t}.bus(ESS1_node, 4) = mpc_modified{t}.bus(ESS1_node, 4) + ess_power(t,1) * tan(acos(0.95));
        end
    end
    
    if ESS2_node <= 33 && ESS2_node >= 2
        if mpc_modified{t}.bus(ESS2_node, 9) == 1 % DC节点
            mpc_modified{t}.bus(ESS2_node, 5) = mpc_modified{t}.bus(ESS2_node, 5) + ess_power(t,2);
        else % AC节点
            mpc_modified{t}.bus(ESS2_node, 3) = mpc_modified{t}.bus(ESS2_node, 3) + ess_power(t,2);
            mpc_modified{t}.bus(ESS2_node, 4) = mpc_modified{t}.bus(ESS2_node, 4) + ess_power(t,2) * tan(acos(0.95));
        end
    end
    
    % 平移负荷
    shift_time = time_SL(shift_time_vars, shift_load);
    for SL = 1:N_SL
        if t == shift_time(SL,1) || t == shift_time(SL,2)
            mpc_modified{t}.bus(shift_load(SL,1),3) = mpc_modified{t}.bus(shift_load(SL,1),3) + shift_load(SL,2);
            mpc_modified{t}.bus(shift_load(SL,1),4) = mpc_modified{t}.bus(shift_load(SL,1),4) + shift_load(SL,3);
        end
    end
    
    % 削减负荷
    for i = 1:N_CL
        switch_state = cut_switch(t,i) > 0.5;
        reduction = cut_amount(t,i) * switch_state;
        mpc_modified{t}.bus(cut_load(i,1),3) = mpc_modified{t}.bus(cut_load(i,1),3) + cut_load(i,t+1) - reduction;
    end
end

% 计算系统运行成本
total_grid_cost = 0;
total_curtail_cost = 0;        % 弃风弃光成本累计 (元)
total_loss_cost    = 0;        % 网损成本累计 (元)
penalty_total      = 0;        % 原有约束惩罚累计 (元)
penalty_over_generation = 0;   % 新增：可再生出力超额惩罚累计 (元)
convergence_flag = 0;

% v5新增：弃风弃光成本设置
curtail_cost_rate = 3.0;            % 元/kWh - 大幅提高弃电成本，使储能具有经济性
over_gen_penalty_rate = curtail_cost_rate * 10; % 超额出力惩罚费率 (元/kWh)，确保>购电/弃电成本

% 新增：统计变量
grid_power_hourly = zeros(24, 1);
wind_curtailed_hourly = zeros(24, 1);
pv_curtailed_hourly = zeros(24, 1);

for t = 1:24
    % 潮流计算
    res = ac_dcpowerflow(mpc_modified{t});
    
    if res.gen == 100000
        convergence_flag = 1;
        break;
    end
    
    % 记录每小时的电网交互功率
    grid_power_hourly(t) = res.gen;
    
    % 电网交互成本
    grid_cost = price(t) * res.gen * 1000 * 2; % 大幅提高购电成本系数，激励储能和风光消纳
    total_grid_cost = total_grid_cost + grid_cost;
    
    % === 弃风弃光量及超额出力检查 ===
    wind_curtailed = 0;
    pv_curtailed   = 0;
    for i = 1:3
        wind_forecast = Pwt(t,i) * wind_base(i);
        pv_forecast = Ppv(t,i) * pv_base(i);
        wind_curtailed = wind_curtailed + max(0, wind_forecast - wind_actual(t,i));
        pv_curtailed   = pv_curtailed   + max(0, pv_forecast   - pv_actual(t,i));

        % 若实际上网功率超出预测，施加惩罚 (防止"虚假零弃电")
        if wind_actual(t,i) > wind_forecast
            penalty_over_generation = penalty_over_generation + ...
                (wind_actual(t,i) - wind_forecast) * 1000 * over_gen_penalty_rate; % MW→kWh
        end
        if pv_actual(t,i) > pv_forecast
            penalty_over_generation = penalty_over_generation + ...
                (pv_actual(t,i) - pv_forecast) * 1000 * over_gen_penalty_rate;  % MW→kWh
        end
    end
    
    % 记录每小时的弃风弃光量
    wind_curtailed_hourly(t) = wind_curtailed;
    pv_curtailed_hourly(t) = pv_curtailed;
    
    % 弃风弃光成本：MW → kWh (24×1h 时间分辨率)
    curtail_cost = (wind_curtailed + pv_curtailed) * 1000 * curtail_cost_rate;
    total_curtail_cost = total_curtail_cost + curtail_cost;
    
    % 网损成本
    loss_cost = res.loss * 1000 * price(t) * 0.5;
    total_loss_cost = total_loss_cost + loss_cost;
    
    % 约束惩罚成本
    penalty = calculate_penalty_v5(res, mpc_modified{t});
    penalty_total = penalty_total + penalty;
end

% === v5改进：删除强制最低消纳率约束 ===
% 不再强制要求最低消纳率，让弃风弃光成本自然引导优化
% 计算统计信息用于分析，但不施加惩罚

% === 重新引入"最低消纳率"软约束 (v5.1) ===
% 不改变弃电单价, 但若弃电率超过阈值则追加惩罚, 强化储能/其它调度手段吸纳可再生。

total_wind_forecast_mwh = sum(sum(Pwt .* wind_base));   % 24h 风电预测 (MWh)
total_pv_forecast_mwh   = sum(sum(Ppv .* pv_base));     % 24h 光伏预测 (MWh)

total_wind_actual_mwh   = sum(sum(wind_actual));        % 24h 实际风电上网 (MWh)
total_pv_actual_mwh     = sum(sum(pv_actual));          % 24h 实际光伏上网 (MWh)

% 计算弃电率 (风+光) = (弃电量)/(预测总量)
total_curtail_mwh = (total_wind_forecast_mwh - total_wind_actual_mwh) + ...
                   (total_pv_forecast_mwh   - total_pv_actual_mwh);
total_forecast_mwh = total_wind_forecast_mwh + total_pv_forecast_mwh;

if total_forecast_mwh > 1e-6
    curtail_ratio = total_curtail_mwh / total_forecast_mwh;   % 0~1
else
    curtail_ratio = 0;
end

% 阈值与罚金系数 (可在主脚本或 Global 参数里调)
target_ratio   = 0.15;   % 允许的最高弃电率 15%
penalty_rate_y = 50;     % 超额部分按 50 元/kWh 处罚, 强化惩罚力度

excess_ratio = max(0, curtail_ratio - target_ratio);
penalty_min_utilization = excess_ratio * total_forecast_mwh * 1000 * penalty_rate_y; % 元
% =======================================

if convergence_flag == 1
    f1 = 1e8; f2 = 1e8;
    if nargout > 2
        cost_details = struct('C_i', 0, 'C_m', 0, 'C_d', 0, 'grid_cost', 0, ...
                             'curtail_cost', 0, 'curtail_cost_rate', curtail_cost_rate, 'loss_cost', 0, 'C_SL', 0, 'C_CL', 0, ...
                             'penalty_total', 0, 'penalty_E', 0, 'capacity_power_penalty', 0, 'min_utilization_penalty', 0, ...
                             'penalty_over_generation', 0);
    end
    return;
end

% 柔性负荷补偿成本
% 平移负荷补偿成本
Tp = round(shift_time_vars) ~= shift_load(:,4)';
C_SL = 0.2 * 1000 * sum(Tp' .* (shift_load(:,2) .* shift_load(:,6)));

% 削减负荷补偿成本
C_CL = 0;
for i = 1:N_CL
    continuous_count = 0;
    total_reductions = sum(cut_switch(:,i) > 0.5);
    is_valid = true;
    
    for t = 1:24
        if cut_switch(t,i) > 0.5
            continuous_count = continuous_count + 1;
            if continuous_count > 5
                is_valid = false;
                break;
            end
        else
            continuous_count = 0;
        end
    end
    
    if total_reductions > 15
        is_valid = false;
    end
    
    if is_valid
        switch_states = cut_switch(:,i) > 0.5;
        C_CL = C_CL + sum(cut_amount(:,i) .* switch_states) * 0.2 * 1000;
    else
        C_CL = C_CL + 10000;
    end
end

%% 目标函数2最终计算
f2 =total_grid_cost + total_curtail_cost + total_loss_cost + C_SL + C_CL + ...
     penalty_total + penalty_over_generation + penalty_E + penalty_min_utilization;

%% v5新增：储能利用率计算
ess_utilization = calculate_ess_utilization_v5(ess_power, [ESS1_capacity, ESS2_capacity]);

%% 新增：计算风光利用率和弃风弃光率
% 修正：确保利用率计算的逻辑正确性
% 利用率 = 实际上网量 / 预测发电量 * 100%
% 弃电率 = 弃电量 / 预测发电量 * 100%
% 且 利用率 + 弃电率 = 100%

% 计算总弃电量
total_wind_curtailed_mwh = sum(wind_curtailed_hourly);
total_pv_curtailed_mwh = sum(pv_curtailed_hourly);

% 风电利用率和弃风率
if total_wind_forecast_mwh > 0
    wind_utilization_rate = (total_wind_forecast_mwh - total_wind_curtailed_mwh) / total_wind_forecast_mwh * 100;
    wind_curtailment_rate = total_wind_curtailed_mwh / total_wind_forecast_mwh * 100;
else
    wind_utilization_rate = 0;
    wind_curtailment_rate = 0;
end

% 光伏利用率和弃光率
if total_pv_forecast_mwh > 0
    pv_utilization_rate = (total_pv_forecast_mwh - total_pv_curtailed_mwh) / total_pv_forecast_mwh * 100;
    pv_curtailment_rate = total_pv_curtailed_mwh / total_pv_forecast_mwh * 100;
else
    pv_utilization_rate = 0;
    pv_curtailment_rate = 0;
end

% 购电量
total_purchase_mwh = sum(max(grid_power_hourly, 0));

% 验证计算逻辑
wind_check = wind_utilization_rate + wind_curtailment_rate;
pv_check = pv_utilization_rate + pv_curtailment_rate;

% 返回成本详细信息
if nargout > 2
    cost_details.C_i = C_i;
    cost_details.C_m = C_m;
    cost_details.C_d = C_d;
    cost_details.grid_cost = total_grid_cost;
    cost_details.curtail_cost = total_curtail_cost;
    cost_details.curtail_cost_rate = curtail_cost_rate;
    cost_details.loss_cost = total_loss_cost;
    cost_details.C_SL = C_SL;
    cost_details.C_CL = C_CL;
    cost_details.penalty_total = penalty_total;
    cost_details.penalty_E = penalty_E;
    cost_details.capacity_power_penalty = capacity_power_penalty;
    cost_details.min_utilization_penalty = penalty_min_utilization;
    cost_details.penalty_over_generation = penalty_over_generation;
    cost_details.ess_utilization = ess_utilization;
    
    % 新增：详细成本分解
    cost_details.cost_breakdown.investment_cost = C_i;
    cost_details.cost_breakdown.maintenance_cost = C_m;
    cost_details.cost_breakdown.disposal_cost = C_d;
    cost_details.cost_breakdown.capacity_power_penalty = capacity_power_penalty;
    cost_details.cost_breakdown.grid_cost = total_grid_cost;
    cost_details.cost_breakdown.curtailment_cost = total_curtail_cost;
    cost_details.cost_breakdown.loss_cost = total_loss_cost;
    cost_details.cost_breakdown.shift_cost = C_SL;
    cost_details.cost_breakdown.cut_cost = C_CL;
    cost_details.cost_breakdown.constraint_penalty = penalty_total;
    cost_details.cost_breakdown.over_generation_penalty = penalty_over_generation;
    cost_details.cost_breakdown.ess_penalty = penalty_E;
    cost_details.cost_breakdown.min_utilization_penalty = penalty_min_utilization;
    
    % 新增：风光利用率和购电量信息
    cost_details.renewable_stats.wind_utilization_rate = wind_utilization_rate;
    cost_details.renewable_stats.pv_utilization_rate = pv_utilization_rate;
    cost_details.renewable_stats.wind_curtailment_rate = wind_curtailment_rate;
    cost_details.renewable_stats.pv_curtailment_rate = pv_curtailment_rate;
    cost_details.renewable_stats.total_purchase_mwh = total_purchase_mwh;
    cost_details.renewable_stats.wind_forecast_mwh = total_wind_forecast_mwh;
    cost_details.renewable_stats.pv_forecast_mwh = total_pv_forecast_mwh;
    cost_details.renewable_stats.wind_actual_mwh = total_wind_actual_mwh;
    cost_details.renewable_stats.pv_actual_mwh = total_pv_actual_mwh;
    
    % 新增：小时级数据用于可视化
    cost_details.hourly_data.grid_power = grid_power_hourly;
    cost_details.hourly_data.wind_curtailed = wind_curtailed_hourly;
    cost_details.hourly_data.pv_curtailed = pv_curtailed_hourly;
    cost_details.hourly_data.wind_actual = wind_actual;
    cost_details.hourly_data.pv_actual = pv_actual;
    cost_details.hourly_data.ess_power = ess_power;
end

end

%% v5新增：辅助函数
function penalty = calculate_penalty_v5(res, mpc)
% v5版本约束惩罚计算
P1 = 70000; P2 = 1000; P4 = 200; % 显著提高P1，使反送电惩罚 > 弃电成本
Sbase = 10;

% 平衡节点功率约束
Pgslack = res.gen;
if Pgslack > 40
    penalty_slack = 100 + P1 * ((Pgslack - 40) / Sbase)^2;
elseif Pgslack < 0
    penalty_slack = 100 + P1 * ((0 - Pgslack) / Sbase)^2;
else
    penalty_slack = 0;
end

% 电压约束
V = res.bus(:,7);
penalty_V = 0;
for i = 1:size(mpc.bus,1)
    if res.bus(i,2) == 1
        if V(i) > 1.1
            penalty_V = penalty_V + 100 + P2 * (V(i) - 1.1)^2;
        elseif V(i) < 0.9
            penalty_V = penalty_V + 100 + P2 * (0.9 - V(i))^2;
        end
    end
end

% VSC约束
penalty_M = 0;
if ~isempty(res.S)
    for i = 1:length(res.S)
        if res.S(i) > 0.6 % v5版本进一步放宽
            penalty_M = penalty_M + 100 + P4 * (res.S(i) - 0.6)^2;
        end
    end
end

penalty = penalty_slack + penalty_V + penalty_M;
end

function [ess_power_corrected, penalty_E] = ESS_constraint_check_v5(ess_power, capacities, max_powers)
% v5版本储能约束检查 - 改进的SOC管理
ess_power_corrected = ess_power;
penalty_E = 0;

eta_ch = 0.92;   % 提高充电效率
eta_dis = 0.92;  % 提高放电效率
dt = 1;

for ess_idx = 1:2
    Ebat0 = 0.5 * capacities(ess_idx) / 1000; % 初始SOC 50%
    Emin = 0.1 * capacities(ess_idx) / 1000;  % 最小SOC 10%
    Emax = 0.8 * capacities(ess_idx) / 1000;  % 最大SOC 80%
    Pmax = max_powers(ess_idx) / 1000;
    
    % 功率约束
    for t = 1:24
        if abs(ess_power_corrected(t, ess_idx)) > Pmax
            penalty_E = penalty_E + 5000 * (abs(ess_power_corrected(t, ess_idx)) - Pmax)^2;
            if ess_power_corrected(t, ess_idx) > Pmax
                ess_power_corrected(t, ess_idx) = Pmax;
            elseif ess_power_corrected(t, ess_idx) < -Pmax
                ess_power_corrected(t, ess_idx) = -Pmax;
            end
        end
    end
    
    Ebat = zeros(24,1);
    
    for t = 1:24
        % SOC更新
        if t == 1
            if ess_power_corrected(t, ess_idx) > 0 % 放电
                Ebat(t) = Ebat0 - ess_power_corrected(t, ess_idx) * dt / eta_dis;
            else % 充电
                Ebat(t) = Ebat0 - ess_power_corrected(t, ess_idx) * dt * eta_ch;
            end
        else
            if ess_power_corrected(t, ess_idx) > 0 % 放电
                Ebat(t) = Ebat(t-1) - ess_power_corrected(t, ess_idx) * dt / eta_dis;
            else % 充电
                Ebat(t) = Ebat(t-1) - ess_power_corrected(t, ess_idx) * dt * eta_ch;
            end
        end
        
        % SOC边界约束 - 软约束处理
        if Ebat(t) > Emax
            penalty_E = penalty_E + 2000 * (Ebat(t) - Emax)^2;
            Ebat(t) = Emax;
        elseif Ebat(t) < Emin
            penalty_E = penalty_E + 2000 * (Emin - Ebat(t))^2;
            Ebat(t) = Emin;
        end
    end
    
    % 末时刻回归约束 - 更宽松的软约束
    if abs(Ebat(24) - Ebat0) > 0.25
        penalty_E = penalty_E + 500 * (abs(Ebat(24) - Ebat0))^2;
    end
end
end

function ess_utilization = calculate_ess_utilization_v5(ess_power, capacities)
% v5新增：计算储能利用率
ess_utilization = struct();

for ess_idx = 1:2
    capacity_mwh = capacities(ess_idx) / 1000;
    
    % 计算充放电能量
    charge_energy = 0;
    discharge_energy = 0;
    cycle_count = 0;
    
    for t = 1:24
        if ess_power(t, ess_idx) > 0 % 放电
            discharge_energy = discharge_energy + ess_power(t, ess_idx);
        else % 充电
            charge_energy = charge_energy + abs(ess_power(t, ess_idx));
        end
    end
    
    % 计算利用率和循环次数
    total_energy = charge_energy + discharge_energy;
    utilization_rate = min(1.0, total_energy / (2 * capacity_mwh)); % 理论最大为2倍容量
    cycle_count = min(charge_energy, discharge_energy) / capacity_mwh;
    
    if ess_idx == 1
        ess_utilization.ess1_rate = utilization_rate;
        ess_utilization.ess1_cycles = cycle_count;
    else
        ess_utilization.ess2_rate = utilization_rate;
        ess_utilization.ess2_cycles = cycle_count;
    end
end
end 