%% 交直流柔性配电网储能设备选址定容主程序 (v5版本 - 储能风光协调优化)
% 基于MMO_CLRPSO算法的双目标优化
% 新增：储能充放电与风光出力强制协调约束，提高储能和风光利用率
clear all; clc; close all;

%% 系统参数设置
global mpc_base Pwt Ppv price shift_load cut_load Load_ac Load_dc
global N_SL N_CL Nwt Npv ac acq dc
global ESS_params % 储能参数

% 加载基础数据
mpc_base = case33ACDC;
flexible_load;
power_load;

% v5新增：储能参数优化 - 增大储能规模以匹配风光装机
ESS_params.Ce = 500;        % 储能单位容量成本 (元/kWh) ↓降低投资门槛
ESS_params.Cp = 300;        % 储能单位功率成本 (元/kW) ↓降低投资门槛
ESS_params.Cmaint = 80;     % 年运行维护成本 (元/kW·年)
ESS_params.ir = 0.015;      % 通胀膨胀率
ESS_params.dr = 0.09;       % 贴现率
ESS_params.Y = 10;          % 储能寿命 (年)
ESS_params.lambda = 0.08;   % 净报废成本系数
ESS_params.E_max = 5000;    % 最大安装容量增至 5 MWh (kWh) - 匹配风光规模
ESS_params.P_max = 1200;    % 最大充放电功率增至 1.2 MW (kW) - 匹配风光波动

% 风光数据处理 - 保持较高装机以测试储能消纳能力
Pwt = [Pwt1' Pwt2' Pwt3'];
Ppv = [Ppv1' Ppv2' Ppv3'];

% 风光基准容量设置
wind_base_original = [1.2, 1.0, 1.5]; % 原始基准容量
pv_base_original = [0.8, 1.0, 1.0];   % 原始基准容量

% v5调整：适度增大基准容量，风电1.8倍，光伏2.5倍
wind_base_new = wind_base_original * 1.8;  % 风电总装机：6.66MW
pv_base_new = pv_base_original * 2.5;      % 光伏总装机：7.0MW

fprintf('=== v5版本风光装机调整 ===\n');
fprintf('原风电装机: %.1f MW -> 新风电装机: %.1f MW\n', sum(wind_base_original), sum(wind_base_new));
fprintf('原光伏装机: %.1f MW -> 新光伏装机: %.1f MW\n', sum(pv_base_original), sum(pv_base_new));
fprintf('风光总装机: %.1f MW (最大负荷约11.4MW)\n', sum(wind_base_new) + sum(pv_base_new));

% 负荷数据处理
ratio = mpc_base.bus(:,3:5)./sum(mpc_base.bus(:,3:5));
ratioq = mpc_base.bus(:,4)./mpc_base.bus(:,3);
ratioq(isnan(ratioq)) = 0;

for i = 1:24
    ac(i,:) = Load_ac(i) * ratio(:,1);
    acq(i,:) = ac(i,:) .* ratioq';
    dc(i,:) = Load_dc(i) * ratio(:,3);
end

N_SL = size(shift_load,1);
N_CL = size(cut_load,1);
Nwt = 3; Npv = 3;

fprintf('系统参数:\n');
fprintf('平移负荷数量 N_SL: %d\n', N_SL);
fprintf('削减负荷数量 N_CL: %d\n', N_CL);

%% v5新增：改进决策变量编码 - 储能功率容量强关联
% 新的决策变量：[ESS1_node, ESS2_node, ESS1_capacity, ESS2_capacity, ESS1_hour_ratio, ESS2_hour_ratio, 
%               风光实际上网功率(24*6), 储能充放电系数(24*2), 平移负荷时间(N_SL), 削减负荷量(24*N_CL), 削减负荷开关(24*N_CL)]

% 详细计算决策变量数量
n_ess_vars = 6;                    % 储能选址定容变量(包含容量和小时比)
n_renewable_vars = 24 * 6;         % 风光实际上网功率变量
n_ess_power_vars = 24 * 2;         % 储能充放电系数变量(-1到1)
n_shift_time_vars = N_SL;          % 平移负荷时间变量
n_cut_amount_vars = 24 * N_CL;     % 削减负荷量变量
n_cut_switch_vars = 24 * N_CL;     % 削减负荷开关变量

n_var = n_ess_vars + n_renewable_vars + n_ess_power_vars + n_shift_time_vars + n_cut_amount_vars + n_cut_switch_vars;
n_obj = 2; % 目标函数数

fprintf('v5决策变量详细计算:\n');
fprintf('储能选址定容: %d (含功率容量配比)\n', n_ess_vars);
fprintf('风光实际上网功率: 24×6 = %d\n', n_renewable_vars);
fprintf('储能充放电系数: 24×2 = %d\n', n_ess_power_vars);
fprintf('平移负荷时间: %d\n', n_shift_time_vars);
fprintf('削减负荷量: 24×%d = %d\n', N_CL, n_cut_amount_vars);
fprintf('削减负荷开关: 24×%d = %d\n', N_CL, n_cut_switch_vars);
fprintf('总决策变量数: %d\n', n_var);

% 验证计算
expected_vars = 6 + 144 + 48 + N_SL + 96 + 96;
if n_var == expected_vars
    verification_result = '正确';
else
    verification_result = '错误';
end
fprintf('预期变量数: %d (验证: %s)\n', expected_vars, verification_result);

% 变量边界
VRmin = zeros(1, n_var);
VRmax = zeros(1, n_var);

% 储能选址边界 (节点2-33)
VRmin(1:2) = 2;
VRmax(1:2) = 33;

% v5新增：储能容量边界 - 提高下限确保有效容量
VRmin(3:4) = 800;  % 最小800 kWh，确保储能有意义
VRmax(3:4) = ESS_params.E_max;

% v5新增：储能功率容量配比边界 (小时数，2-6小时) - 提高功率
VRmin(5:6) = 2;    % 最小2小时
VRmax(5:6) = 6;    % 最大6小时，提高功率配比

% 风光实际上网功率边界
idx = 7;
for t = 1:24
    % 风电场1,2,3的实际上网功率边界
    VRmin(idx:idx+2) = [0 0 0];
    VRmax(idx:idx+2) = [Pwt(t,1)*wind_base_new(1) Pwt(t,2)*wind_base_new(2) Pwt(t,3)*wind_base_new(3)];
    % 光伏电站1,2,3的实际上网功率边界
    VRmin(idx+3:idx+5) = [0 0 0];
    VRmax(idx+3:idx+5) = [Ppv(t,1)*pv_base_new(1) Ppv(t,2)*pv_base_new(2) Ppv(t,3)*pv_base_new(3)];
    idx = idx + 6;
end

% v5新增：储能充放电系数边界 (-1到1，通过风光剩余功率计算实际功率)
for t = 1:24
    VRmin(idx:idx+1) = [-1 -1];  % -1表示最大充电
    VRmax(idx:idx+1) = [1 1];    % +1表示最大放电
    idx = idx + 2;
end

% 平移负荷时间边界
time_SL1 = shift_load(:,4);
time_SL2 = shift_load(:,5) - shift_load(:,6) + 1;
VRmin(idx:idx+N_SL-1) = time_SL1';
VRmax(idx:idx+N_SL-1) = time_SL2';
idx = idx + N_SL;

% 削减负荷量边界
for t = 1:24
    VRmin(idx:idx+N_CL-1) = zeros(1,N_CL);
    VRmax(idx:idx+N_CL-1) = cut_load(:,t+1)';
    idx = idx + N_CL;
end

% 削减负荷开关边界
for t = 1:24
    VRmin(idx:idx+N_CL-1) = -10 * ones(1,N_CL);
    VRmax(idx:idx+N_CL-1) = 10 * ones(1,N_CL);
    idx = idx + N_CL;
end

% 验证边界向量长度
fprintf('边界向量长度验证: VRmin=%d, VRmax=%d, n_var=%d\n', length(VRmin), length(VRmax), n_var);
if length(VRmin) ~= n_var || length(VRmax) ~= n_var
    error('边界向量长度与决策变量数量不匹配！');
end

%% MMO_CLRPSO算法参数
popsize = 1000;  % 增大种群
Max_Gen = 40;   % 恢复充足代数确保收敛

fprintf('\n开始储能选址定容优化 (v5版本 - 储能风光协调)...\n');
fprintf('决策变量数: %d\n', n_var);
fprintf('目标函数数: %d\n', n_obj);
fprintf('储能最大容量: %d kWh\n', ESS_params.E_max);
fprintf('储能最大功率: %d kW\n', ESS_params.P_max);
fprintf('储能容量成本: %d 元/kWh\n', ESS_params.Ce);
fprintf('储能功率成本: %d 元/kW\n', ESS_params.Cp);
fprintf('弃风弃光成本: 10.0 元/kWh\n'); % 弃风弃光成本设置
fprintf('消纳率约束: 已删除，由市场机制自然调节\n'); % 删除强制消纳率约束

%% 调用MMO_CLRPSO算法
[ps, pf] = MMO_CLRPSO_ESS_v2('ESS_objective_v5', VRmin, VRmax, n_obj, popsize, Max_Gen);

%% 目标函数归一化处理
fprintf('\n进行目标函数归一化处理...\n');

% 计算目标函数的最小值和最大值
f1_min = min(pf(:,1));
f1_max = max(pf(:,1));
f2_min = min(pf(:,2));
f2_max = max(pf(:,2));

fprintf('目标函数1 (储能全寿命周期成本): %.2f - %.2f 万元\n', f1_min/10000, f1_max/10000);
fprintf('目标函数2 (系统运行成本): %.2f - %.2f 万元\n', f2_min/10000, f2_max/10000);

% Min-Max归一化
pf_norm = zeros(size(pf));
if f1_max > f1_min
    pf_norm(:,1) = (pf(:,1) - f1_min) / (f1_max - f1_min);
else
    pf_norm(:,1) = 0.5 * ones(size(pf,1), 1);
end

if f2_max > f2_min
    pf_norm(:,2) = (pf(:,2) - f2_min) / (f2_max - f2_min);
else
    pf_norm(:,2) = 0.5 * ones(size(pf,1), 1);
end

%% 基于归一化后的Pareto前沿选择最优解
% 计算到理想点(0,0)的距离
distances = sqrt(pf_norm(:,1).^2 + pf_norm(:,2).^2);
[~, best_idx] = min(distances);
best_solution = ps(best_idx,:);

%% 结果分析
fprintf('\n优化完成！\n');
fprintf('获得Pareto解的数量: %d\n', size(ps,1));

fprintf('\nv5最优解分析 (储能风光协调优化):\n');
fprintf('储能1安装节点: %d\n', round(best_solution(1)));
fprintf('储能2安装节点: %d\n', round(best_solution(2)));
fprintf('储能1额定容量: %.2f kWh\n', best_solution(3));
fprintf('储能2额定容量: %.2f kWh\n', best_solution(4));

% v5新增：通过小时比计算功率
ESS1_power = best_solution(3) / best_solution(5);
ESS2_power = best_solution(4) / best_solution(6);
fprintf('储能1额定功率: %.2f kW (配比%.2fh)\n', ESS1_power, best_solution(5));
fprintf('储能2额定功率: %.2f kW (配比%.2fh)\n', ESS2_power, best_solution(6));

% 计算最优解的目标函数值
[f1, f2, cost_details] = ESS_objective_v5(best_solution);
fprintf('储能全寿命周期成本: %.2f 万元\n', f1/10000);
fprintf('系统运行成本: %.2f 万元\n', f2/10000);
fprintf('归一化后的目标函数值: f1_norm=%.3f, f2_norm=%.3f\n', pf_norm(best_idx,1), pf_norm(best_idx,2));
fprintf('到理想点的距离: %.3f\n', distances(best_idx));

%% 详细成本分析
fprintf('\n=== v5详细成本分析 ===\n');
fprintf('目标函数1 - 储能全寿命周期成本 (%.2f万元):\n', f1/10000);
fprintf('  储能投资成本: %.2f 万元\n', cost_details.C_i/10000);
fprintf('  储能运维成本: %.2f 万元\n', cost_details.C_m/10000);
fprintf('  储能报废成本: %.2f 万元\n', cost_details.C_d/10000);
if isfield(cost_details, 'capacity_power_penalty')
    fprintf('  功率容量配比惩罚: %.2f 万元\n', cost_details.capacity_power_penalty/10000);
end

fprintf('\n目标函数2 - 系统运行成本 (%.2f万元):\n', f2/10000);
fprintf('  电网交互成本: %.2f 万元\n', cost_details.grid_cost/10000);
if isfield(cost_details, 'curtail_cost_rate')
    fprintf('  弃风弃光成本: %.2f 万元 (%.1f元/kWh)\n', cost_details.curtail_cost/10000, cost_details.curtail_cost_rate);
else
    fprintf('  弃风弃光成本: %.2f 万元\n', cost_details.curtail_cost/10000);
end
fprintf('  网损成本: %.2f 万元\n', cost_details.loss_cost/10000);
fprintf('  平移负荷补偿成本: %.2f 万元\n', cost_details.C_SL/10000);
fprintf('  削减负荷补偿成本: %.2f 万元\n', cost_details.C_CL/10000);
fprintf('  约束惩罚成本: %.2f 万元\n', cost_details.penalty_total/10000);
if isfield(cost_details, 'penalty_E')
    fprintf('  储能约束惩罚成本: %.2f 万元\n', cost_details.penalty_E/10000);
end
if isfield(cost_details, 'min_utilization_penalty')
    fprintf('  最低消纳率惩罚成本: %.2f 万元\n', cost_details.min_utilization_penalty/10000);
end

%% v5新增：储能利用率分析
fprintf('\n=== v5储能利用率分析 ===\n');
if isfield(cost_details, 'ess_utilization')
    fprintf('储能1利用率: %.1f%% (充放电次数: %.1f次/天)\n', ...
        cost_details.ess_utilization.ess1_rate*100, cost_details.ess_utilization.ess1_cycles);
    fprintf('储能2利用率: %.1f%% (充放电次数: %.1f次/天)\n', ...
        cost_details.ess_utilization.ess2_rate*100, cost_details.ess_utilization.ess2_cycles);
end

%% v5新增：风光利用率分析
fprintf('\n=== v5风光利用率分析 ===\n');
if isfield(cost_details, 'renewable_stats')
    fprintf('风电利用率: %.1f%% (弃风率: %.1f%%)\n', ...
        cost_details.renewable_stats.wind_utilization_rate, cost_details.renewable_stats.wind_curtailment_rate);
    fprintf('光伏利用率: %.1f%% (弃光率: %.1f%%)\n', ...
        cost_details.renewable_stats.pv_utilization_rate, cost_details.renewable_stats.pv_curtailment_rate);
    fprintf('风电预测: %.2f MWh, 实际上网: %.2f MWh\n', ...
        cost_details.renewable_stats.wind_forecast_mwh, cost_details.renewable_stats.wind_actual_mwh);
    fprintf('光伏预测: %.2f MWh, 实际上网: %.2f MWh\n', ...
        cost_details.renewable_stats.pv_forecast_mwh, cost_details.renewable_stats.pv_actual_mwh);
    fprintf('总购电量: %.2f MWh\n', cost_details.renewable_stats.total_purchase_mwh);
end

%% 保存结果
save('ESS_optimization_results_v5.mat', 'ps', 'pf', 'pf_norm', 'best_solution', 'cost_details');

%% 绘制结果
try
    % 绘制调度结果和储能充放电功率图
    plot_dispatch_results_v5(best_solution, cost_details, ESS_params);
    fprintf('\n调度结果图表已生成！\n');
    
    % 绘制Pareto解集和前沿图
    plot_pareto_results_v5(ps, pf, pf_norm, best_solution, best_idx);
    fprintf('Pareto前沿图表已生成！\n');
    
catch ME
    fprintf('绘图出错: %s\n', ME.message);
end

fprintf('\nv5版本优化完成！\n');
fprintf('主要改进:\n');
fprintf('1. 储能功率容量强关联编码，消除配比惩罚\n');
fprintf('2. 储能充放电与风光剩余功率协调\n');
fprintf('3. 提高储能规模上限至5MWh/1.2MW\n');
fprintf('4. 增强储能利用率约束和分析\n');
fprintf('5. 提高弃电成本激励储能参与消纳\n'); 