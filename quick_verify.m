%% 快速验证修正效果
clear; clc; close all;

% 全局变量声明
global mpc_base Pwt Ppv price shift_load cut_load Load_ac Load_dc
global N_SL N_CL Nwt Npv ac acq dc ESS_params

% 储能参数设置
ESS_params.Ce = 700; ESS_params.Cp = 400; ESS_params.Cmaint = 60;
ESS_params.ir = 0.015; ESS_params.dr = 0.09; ESS_params.Y = 10;
ESS_params.lambda = 0.08; ESS_params.Cde = 60;
ESS_params.E_max = 8000; ESS_params.P_max = 2000;

% 加载数据
load_data_v6;

%% 创建测试解
n_var = 6 + N_SL + 24*N_CL + 24*N_CL;
X_test = zeros(1, n_var);

% 储能参数
X_test(1:6) = [21, 4, 1102, 1120, 2.68, 1.89];

% 平移负荷时间
idx = 7;
for i = 1:N_SL
    X_test(idx) = 3;  % 转移到凌晨3点
    idx = idx + 1;
end

% 削减负荷量和开关
for t = 1:24
    is_peak = (t >= 10 && t <= 12) || (t >= 18 && t <= 21);
    for i = 1:N_CL
        X_test(idx) = cut_load(i,t+1) * (is_peak ? 0.8 : 0.1);
        idx = idx + 1;
    end
end
for t = 1:24
    is_peak = (t >= 10 && t <= 12) || (t >= 18 && t <= 21);
    for i = 1:N_CL
        X_test(idx) = is_peak ? 5 : -5;
        idx = idx + 1;
    end
end

%% 测试目标函数
[f1, f2, cost_details] = ESS_objective_v6(X_test);

fprintf('=== 快速验证结果 ===\n');
fprintf('储能成本: %.1f万元, 运行成本: %.1f万元\n', f1/10000, f2/10000);
fprintf('弃风弃光率: %.1f%%\n', cost_details.renewable_stats.wind_curtailment_rate);

%% 验证储能调度
ess_power = cost_details.hourly_data.ess_power;
evening_discharge = sum(max(ess_power(18:21,:), 0), 'all');
pv_charge = sum(max(-ess_power(8:16,:), 0), 'all');

fprintf('晚高峰放电: %.2f MWh\n', evening_discharge);
fprintf('光伏期充电: %.2f MWh\n', pv_charge);

if evening_discharge > 0.5
    fprintf('✓ 晚高峰期放电正常\n');
else
    fprintf('✗ 晚高峰期放电不足\n');
end

if pv_charge > 1.0
    fprintf('✓ 光伏期充电正常\n');
else
    fprintf('✗ 光伏期充电不足\n');
end

fprintf('验证完成\n'); 