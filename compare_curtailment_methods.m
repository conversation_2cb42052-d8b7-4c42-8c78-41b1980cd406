%% 对比弃风弃光计算方法
% 展示修改前后的差异
clear all; clc;

fprintf('=== 弃风弃光计算方法对比 ===\n\n');

% 模拟数据
wind_forecast = [0.5, 0.4, 0.6];  % 风电预测出力 (MW)
pv_forecast = [0.3, 0.4, 0.4];    % 光伏预测出力 (MW)
wind_base = [1.2, 1.0, 1.5];      % 风电基准容量 (MW)
pv_base = [0.8, 1.0, 1.0];        % 光伏基准容量 (MW)

fprintf('示例数据:\n');
fprintf('风电预测出力比例: [%.1f, %.1f, %.1f] p.u.\n', wind_forecast);
fprintf('光伏预测出力比例: [%.1f, %.1f, %.1f] p.u.\n', pv_forecast);
fprintf('风电基准容量: [%.1f, %.1f, %.1f] MW\n', wind_base);
fprintf('光伏基准容量: [%.1f, %.1f, %.1f] MW\n', pv_base);

% 计算实际预测出力
wind_available = wind_forecast .* wind_base;  % MW
pv_available = pv_forecast .* pv_base;        % MW

fprintf('\n实际预测出力:\n');
fprintf('风电: [%.2f, %.2f, %.2f] MW, 总计 %.2f MW\n', wind_available, sum(wind_available));
fprintf('光伏: [%.2f, %.2f, %.2f] MW, 总计 %.2f MW\n', pv_available, sum(pv_available));

%% 方法1：原来的削减比例方法
fprintf('\n--- 方法1：削减比例方法 (修改前) ---\n');

% 假设决策变量为削减比例
wind_cut_ratio = [0.1, 0.2, 0.15];  % 削减比例 (0-1)
pv_cut_ratio = [0.25, 0.1, 0.2];    % 削减比例 (0-1)

% 计算弃风弃光量
wind_curtailed_1 = wind_available .* wind_cut_ratio;
pv_curtailed_1 = pv_available .* pv_cut_ratio;

% 计算实际上网功率
wind_actual_1 = wind_available .* (1 - wind_cut_ratio);
pv_actual_1 = pv_available .* (1 - pv_cut_ratio);

% 计算成本 (1.58元/kWh)
curtail_cost_1 = 1.58 * (sum(wind_curtailed_1) + sum(pv_curtailed_1)) * 1000;

fprintf('削减比例: 风电[%.1f%%, %.1f%%, %.1f%%], 光伏[%.1f%%, %.1f%%, %.1f%%]\n', ...
    wind_cut_ratio*100, pv_cut_ratio*100);
fprintf('弃风弃光量: 风电[%.3f, %.3f, %.3f] MW, 光伏[%.3f, %.3f, %.3f] MW\n', ...
    wind_curtailed_1, pv_curtailed_1);
fprintf('实际上网功率: 风电[%.3f, %.3f, %.3f] MW, 光伏[%.3f, %.3f, %.3f] MW\n', ...
    wind_actual_1, pv_actual_1);
fprintf('总弃风弃光量: %.3f MW\n', sum(wind_curtailed_1) + sum(pv_curtailed_1));
fprintf('弃风弃光成本 (1.58元/kWh): %.2f 元\n', curtail_cost_1);

%% 方法2：新的实际上网功率方法
fprintf('\n--- 方法2：实际上网功率方法 (修改后) ---\n');

% 假设决策变量为实际上网功率
wind_actual_2 = [0.54, 0.32, 0.51];  % 实际上网功率 (MW)
pv_actual_2 = [0.18, 0.36, 0.32];    % 实际上网功率 (MW)

% 计算弃风弃光量 = 预测出力 - 实际上网功率
wind_curtailed_2 = max(0, wind_available - wind_actual_2);
pv_curtailed_2 = max(0, pv_available - pv_actual_2);

% 计算成本 (1.6元/kWh)
curtail_cost_2 = 1.6 * (sum(wind_curtailed_2) + sum(pv_curtailed_2)) * 1000;

fprintf('实际上网功率: 风电[%.3f, %.3f, %.3f] MW, 光伏[%.3f, %.3f, %.3f] MW\n', ...
    wind_actual_2, pv_actual_2);
fprintf('弃风弃光量: 风电[%.3f, %.3f, %.3f] MW, 光伏[%.3f, %.3f, %.3f] MW\n', ...
    wind_curtailed_2, pv_curtailed_2);
fprintf('总弃风弃光量: %.3f MW\n', sum(wind_curtailed_2) + sum(pv_curtailed_2));
fprintf('弃风弃光成本 (1.6元/kWh): %.2f 元\n', curtail_cost_2);

%% 对比分析
fprintf('\n=== 对比分析 ===\n');
fprintf('方法1 vs 方法2:\n');
fprintf('弃风弃光量: %.3f MW vs %.3f MW\n', ...
    sum(wind_curtailed_1) + sum(pv_curtailed_1), ...
    sum(wind_curtailed_2) + sum(pv_curtailed_2));
fprintf('弃风弃光成本: %.2f 元 vs %.2f 元\n', curtail_cost_1, curtail_cost_2);
fprintf('成本差异: %.2f 元 (%.1f%%)\n', ...
    curtail_cost_2 - curtail_cost_1, ...
    (curtail_cost_2 - curtail_cost_1)/curtail_cost_1*100);

fprintf('\n主要差异:\n');
fprintf('1. 决策变量含义: 削减比例(0-1) -> 实际上网功率(MW)\n');
fprintf('2. 弃风弃光计算: 预测出力×削减比例 -> 预测出力-实际上网功率\n');
fprintf('3. 单位成本: 1.58元/kWh -> 1.6元/kWh\n');
fprintf('4. 物理意义: 更直观，算法直接控制上网功率\n');

fprintf('\n修改完成！\n'); 