%节点编号    有功(MW)   无功(Mvar)     连续时间（初/末）     使用时长（h）
shift_load=[
% 6	0.065	0.0325	10	15	2
% 9	0.04	0.02	11	16	2
% 14	0.134	0.067	20	24	1
% 18	0.014	0.007	12	16	2
% 22	0.025	0.0125	16	21	2
% 24	0.033	0.0165	19	23	2
% 29	0.029	0.0145	16	21	2
6	0.08	0.04	9	15	2
9	0.07	0.035	10	16	2
4	0.09	0.045	11	15	1
31	0.08	0.04	19	24	1
14	0.07	0.035	21	29	2
15	0.08	0.04	21	29	2
18	0.09	0.045	9	14	2
19	0.05	0.025	18	24	2
23	0.09	0.045	19	24	2
28	0.07	0.035	9	15	2
30	0.08	0.04	10	16	1
33	0.09	0.045	12	17	2
];

%节点编号   削减负荷上限(MW,24小时)
cut_load=[
25 0.021929287	0.021927409	0.022709458	0.021925264	0.023611822	0.023550549	0.035086859	0.034715895	0.055461187	0.104147685	0.098671976	0.085653942	0.080999384	0.094514695	0.105420237	0.096675845	0.09744959	0.093969247	0.079651126	0.068836045	0.058466997	0.065776414	0.032520423	0.02466505
33 0.017543429	0.017541927	0.018167567	0.017540211	0.018889458	0.018840439	0.028069487	0.027772716	0.04436895	0.083318148	0.078937581	0.068523154	0.064799507	0.075611756	0.084336189	0.077340676	0.077959672	0.075175398	0.063720901	0.055068836	0.046773597	0.052621131	0.026016339	0.01973204
4 0.021777273	0.02	0.022402597	0.020454545	0.023275175	0.024	0.030458182	0.028436364	0.041819239	0.067543636	0.061738017	0.050912642	0.049508141	0.063479624	0.06359528	0.059670248	0.058080808	0.059381494	0.055397727	0.045454545	0.036363636	0.042228739	0.02231405	0.021306818
27 0.014518182	0.013333333	0.014935065	0.013636364	0.015516783	0.016	0.020305455	0.018957576	0.027879493	0.045029091	0.041158678	0.033941761	0.033005427	0.042319749	0.042396853	0.039780165	0.038720539	0.039587662	0.036931818	0.03030303	0.024242424	0.028152493	0.014876033	0.014204545
];

% 放大可平移与可削减负荷的有功/无功功率（乘以 15）
scale_factor = 5;
shift_load(:,2:3) = shift_load(:,2:3) * scale_factor;   % 有功、无功
cut_load(:,2:end) = cut_load(:,2:end) * scale_factor;   % 24 小时有功上限