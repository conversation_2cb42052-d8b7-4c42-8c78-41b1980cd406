function mpc = case33ACDC

%% system MVA base
mpc.baseMVA = 10;

%% bus data
%	bus_i	type	Pac	Qac	Pdc	 Va	 Vm	baseKV	W(1DC  0AC)	Vmax	Vmin
mpc.bus = [  %% (Pd and Qd are specified in kW & kVAr here, converted to MW & MVAr below)
1	3	0	0	0	0	1	12.66	0	1	1;
2	1	100	60	0	0	1	12.66	0	1.1	0.9;
3	1	90	40	0	0	1	12.66	0	1.1	0.9;
4	1	120	80	0	0	1	12.66	0	1.1	0.9;
5	1	60	30	0	0	1	12.66	0	1.1	0.9;
6	1	60	20	0	0	1	12.66	0	1.1	0.9;
7	1	200	100	0	0	1	12.66	0	1.1	0.9;
8	1	200	100	0	0	1	12.66	0	1.1	0.9;
9	1	60	20	0	0	1	12.66	0	1.1	0.9;
10	1	60	20	0	0	1	12.66	0	1.1	0.9;
11	1	45	30	0	0	1	12.66	0	1.1	0.9;
12	1	45	35	15	0	1	20.67	1	1.1	0.9;
13	1	0	0	60	0	1	20.67	1	1.1	0.9;
14	1	80	80	40	0	1	20.67	1	1.1	0.9;
15	1	40	10	20	0	1	20.67	1	1.1	0.9;
16	1	0	0	60	0	1	20.67	1	1.1	0.9;
17	1	0	0	60	0	1	20.67	1	1.1	0.9;
18	1	60	40	30	0	1	20.67	1	1.1	0.9;
19	1	90	40	0	0	1	12.66	0	1.1	0.9;
20	1	90	40	0	0	1	12.66	0	1.1	0.9;
21	1	0	0	200	0	1	20.67	1	1.1	0.9;
22	1	0	0	90	0	1	20.67	1	1.1	0.9;
23	1	90	50	0	0	1	12.66	0	1.1	0.9;
24	1	420	200	0	0	1	12.66	0	1.1	0.9;
25	1	220	200	200	0	1	12.66	0	1.1	0.9;
26	1	60	25	60	0	1	12.66	0	1.1	0.9;
27	1	60	25	0	0	1	12.66	0	1.1	0.9;
28	1	60	20	0	0	1	12.66	0	1.1	0.9;
29	1	120	70	0	0	1	12.66	0	1.1	0.9;
30	1	200	600	0	0	1	12.66	0	1.1	0.9;
31	1	110	70	40	0	1	20.67	1	1.1	0.9;
32	1	0	0	210	0	1	20.67	1	1.1	0.9;
33	1	40	40	20	0	1	20.67	1	1.1	0.9;
    ];

%% generator data
% bus	Pgac Qgac Pgdc	Qmax	Qmin	Vg	Pmax	Pmin
mpc.gen = [1	0	0	0	10	-10	1.05  80  0;
    5	0.5	0	0	0.4	-0.4	1.03  0.5  0;
    24	0.5	0	0	0.4	-0.4	1.03  0.5  0;
    29	0.5	0	0	0.4	-0.4	1.02 0.5  0;
    32	0	0	0.25	0	0	1  0.25  0;
    17	0	0	0.25	0	0	1  0.25  0;
    22	0	0	0.25	0	0	1  0.25  0;
    ];

%% branch data
%	fbus	tbus	r	x	D  M M_cita
mpc.branch = [  %% (r and x specified in ohms here, converted to p.u. below)
1	2	0.0922	0.047	0	0	0
2	3	0.493	0.2511	0	0	0
3	4	0.366	0.1864	0	0	0
4	5	0.3811	0.1941	0	0	0
5	6	0.819	0.707	0	0	0
6	7	0.1872	0.6188	0	0	0
7	8	0.7114	0.2351	0	0	0
8	9	1.0300	0.7400	0	0	0
9	10	1.0440	0.7400	0	0	0
10	11	0.1966	0.0650	0	0	0
11	12	1.4228	0	1	0.97	0.3176
12	13	2.936	0	1	0	0
13	14	1.0832	0	1	0	0
14	15	1.182	0	1	0	0
15	16	1.4926	0	1	0	0
16	17	2.578	0	1	0	0
17	18	1.464	0	1	0	0
2	19	0.164	0.1565	0	0	0
19	20	1.5042	1.3554	0	0	0
20	21	0.819	0	1	0.96	0.3176
21	22	1.4178	0	1	0	0
3	23	0.4512	0.3083	0	0	0
23	24	0.898	0.7091	0	0	0
24	25	0.896	0.7011	0	0	0
6	26	0.203	0.1034	0	0	0
26	27	0.2842	0.1447	0	0	0
27	28	1.059	0.9337	0	0	0
28	29	0.8042	0.7006	0	0	0
29	30	0.5075	0.2585	0	0	0
30	31	1.9488	0	1	0.98	0.3176
31	32	0.621	0	1	0	0
32	33	0.682	0	1	0	0
13	22	4	0	1	0	0
18	33	1	0	1	0	0
25	29	0.5	0.5	0	0	0
    ];

%%-----  OPF Data  -----%%
%% generator cost data
%	1	startup	shutdown	n	x1	y1	...	xn	yn
%	2	startup	shutdown	n	c(n-1)	...	c0
mpc.gencost = [
	2	0	0	3	0	20	0;
    2	0	0	3	0	128	0;
    2	0	0	3	0	128	0;
    2	0	0	3	0	128	0;
    2	0	0	3	0	209	0;
    2	0	0	3	0	209	0;
    2	0	0	3	0	209	0;
];

%%数据进行标幺值化
busacp=mpc.bus(:,3)/ 1e3;%33节点全部转化为MW
busdc=mpc.bus(:,5)/ 1e3;
busacq=mpc.bus(:,4)/ 1e3;
mpc.bus(:,3)=busacp;
mpc.bus(:,5)=busdc;
mpc.bus(:,4)=busacq;

Zac=12.66^2/mpc.baseMVA;%交流基准值
Zbc=20.67^2/mpc.baseMVA;%直流基准值

for i=1:size(mpc.branch,1)
    if mpc.branch(i,5)==1
        mpc.branch(i,3)=mpc.branch(i,3)/Zbc;
        mpc.branch(i,4)=mpc.branch(i,4)/Zbc;
    else
         mpc.branch(i,3)=mpc.branch(i,3)/Zac;
         mpc.branch(i,4)=mpc.branch(i,4)/Zac;
    end
end