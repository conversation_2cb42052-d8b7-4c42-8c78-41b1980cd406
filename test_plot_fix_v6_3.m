%% 测试修复后的v6.3绘图功能
% 验证新的可削减负荷模型绘图是否正常工作

clear; clc; close all;

% 声明全局变量
global mpc_base Pwt Ppv price shift_load cut_load Load_ac Load_dc
global N_SL N_CL Nwt Npv ac acq dc ESS_params

% 储能参数设置
ESS_params.Ce = 600;        % 储能单位容量成本 (元/kWh)
ESS_params.Cp = 350;        % 储能单位功率成本 (元/kW)
ESS_params.Cmaint = 50;     % 年运行维护成本 (元/kW·年)
ESS_params.ir = 0.015;      % 通胀膨胀率
ESS_params.dr = 0.09;       % 贴现率
ESS_params.Y = 10;          % 储能寿命 (年)
ESS_params.lambda = 0.08;   % 净报废成本系数
ESS_params.Cde = 60;        % 单位容量报废处置成本 (元/kWh)
ESS_params.E_max = 12000;   % 最大安装容量 12 MWh
ESS_params.P_max = 4000;    % 最大充放电功率 4 MW

% 加载数据
load_data_v6;

fprintf('=== 测试修复后的v6.3绘图功能 ===\n');

% 构造v6.3版本的测试决策变量
n_var = 2 + 2 + 2 + N_SL + N_CL;  % v6.3版本变量数量
X_test = zeros(1, n_var);

% 基于实际运行结果构造测试解
X_test(1:2) = [25, 9];              % 储能安装节点
X_test(3:4) = [2220.01, 2037.18];   % 储能容量
X_test(5:6) = [3.51, 3.89];         % 功率容量配比

% 平移负荷时间（设为原始时间）
X_test(7:6+N_SL) = shift_load(:,4)';

% 可削减负荷βcut值（模拟不同的削减策略）
X_test(7+N_SL:end) = [0.2, 0.5, 0.8, 0.3];  % 4个节点的βcut值

fprintf('测试解构造完成：\n');
fprintf('  储能1: 节点%d, 容量%.0f kWh, 功率%.0f kW\n', ...
    X_test(1), X_test(3), X_test(3)/X_test(5));
fprintf('  储能2: 节点%d, 容量%.0f kWh, 功率%.0f kW\n', ...
    X_test(2), X_test(4), X_test(4)/X_test(6));
fprintf('  决策变量总数: %d\n', length(X_test));

% 显示βcut值
fprintf('  可削减负荷βcut值:\n');
for i = 1:N_CL
    fprintf('    节点%d: βcut = %.3f\n', cut_load(i,1), X_test(7+N_SL+i-1));
end

% 测试目标函数计算
try
    fprintf('\n正在计算目标函数...\n');
    [f1, f2, cost_details] = ESS_objective_v6(X_test);
    
    fprintf('目标函数计算成功：\n');
    fprintf('  f1 (储能全寿命周期成本): %.2f 万元\n', f1/10000);
    fprintf('  f2 (系统运行成本): %.2f 万元\n', f2/10000);
    fprintf('  削减负荷补偿成本: %.2f 万元\n', cost_details.C_CL/10000);
    
    % 测试绘图功能
    fprintf('\n正在测试绘图功能...\n');
    
    % 调用绘图函数
    plot_dispatch_results_v6(X_test, cost_details, ESS_params);
    
    fprintf('绘图功能测试成功！\n');
    
    % 验证负荷调整效果
    fprintf('\n=== 负荷调整验证 ===\n');
    
    % 计算原始负荷
    original_load = Load_ac + Load_dc;
    fprintf('原始负荷峰值: %.3f MW (时段%d)\n', max(original_load), find(original_load == max(original_load)));
    
    % 计算调整后负荷
    adjusted_load = calculate_adjusted_load_test(X_test, original_load);
    fprintf('调整后负荷峰值: %.3f MW (时段%d)\n', max(adjusted_load), find(adjusted_load == max(adjusted_load)));
    
    % 验证平移负荷效果
    shift_difference = sum(original_load) - sum(adjusted_load);
    fprintf('平移负荷总量差异: %.6f MW (应该接近0)\n', shift_difference);
    
    % 验证削减负荷效果
    total_curtailment = sum(original_load) - sum(adjusted_load);
    if abs(total_curtailment) > 1e-6
        fprintf('削减负荷总量: %.3f MW\n', abs(total_curtailment));
    end
    
    % 显示几个关键时段的负荷变化
    fprintf('\n关键时段负荷变化:\n');
    for t = [1, 10, 12, 18, 20, 24]
        fprintf('  时段%02d: %.3f MW → %.3f MW (变化%.3f MW)\n', ...
            t, original_load(t), adjusted_load(t), adjusted_load(t) - original_load(t));
    end
    
catch ME
    fprintf('测试失败！\n');
    fprintf('错误信息: %s\n', ME.message);
    if ~isempty(ME.stack)
        fprintf('错误位置: %s (行 %d)\n', ME.stack(1).name, ME.stack(1).line);
    end
end

fprintf('\n=== 测试完成 ===\n');

%% 辅助函数：计算调整后负荷（用于验证）
function adjusted_load = calculate_adjusted_load_test(X, original_load)
    global shift_load cut_load N_SL N_CL
    
    adjusted_load = original_load;
    
    % 平移负荷调整
    if ~isempty(shift_load) && N_SL > 0
        shift_time_vars = X(7:6+N_SL);
        for SL = 1:N_SL
            power = shift_load(SL,2);
            dur = shift_load(SL,6);
            orig_start = shift_load(SL,4);
            new_start = round(shift_time_vars(SL));
            
            % 简化处理：如果新旧时间不同，则进行平移
            if new_start ~= orig_start
                % 移出原时段
                for h = 0:dur-1
                    orig_hour = mod(orig_start + h - 1, 24) + 1;
                    adjusted_load(orig_hour) = adjusted_load(orig_hour) - power;
                end
                
                % 移入新时段
                for h = 0:dur-1
                    new_hour = mod(new_start + h - 1, 24) + 1;
                    adjusted_load(new_hour) = adjusted_load(new_hour) + power;
                end
            end
        end
    end
    
    % 削减负荷调整
    if ~isempty(cut_load) && N_CL > 0
        idx_cut_start = 7 + N_SL;
        beta_cut = X(idx_cut_start:idx_cut_start+N_CL-1);
        beta_cut = 1./(1+exp(-beta_cut));  % sigmoid转换
        
        for t = 1:24
            total_reduction = 0;
            for i = 1:N_CL
                beta_cut_value = beta_cut(i);
                max_curtail_power = cut_load(i, t+1);
                actual_reduction = max_curtail_power * beta_cut_value;
                total_reduction = total_reduction + actual_reduction;
            end
            adjusted_load(t) = adjusted_load(t) - total_reduction;
        end
    end
end 