function [ps,pf]=MMO_CLRPSO_ESS_v2(func_name,VRmin,VRmax,n_obj,Particle_Number,Max_Gen)
%% 储能选址定容的MMO_CLRPSO算法 (修正版)
% 包含目标函数归一化处理

%% Initialize parameters
n_var=size(VRmin,2);
Max_FES=Max_Gen*Particle_Number;
n_PBA=5;
cc=[2.05 2.05];
iwt=0.7298;

%% Initialize particles' positions and velocities
mv=0.5*(VRmax-VRmin);
VRmin=repmat(VRmin,Particle_Number,1);
VRmax=repmat(VRmax,Particle_Number,1);
Vmin=repmat(-mv,Particle_Number,1);
Vmax=-Vmin;
pos=VRmin+(VRmax-VRmin).*rand(Particle_Number,n_var);

%%Cluster Population
subsize=10;
num=Particle_Number/subsize;
n_GBA=10*subsize;
n_NBA=3*n_GBA;
pos=sortrows(pos,1);
particle_position=Cluster(pos,subsize,n_var);

% 初始化Pareto前沿图
figure(2);
set(gcf, 'Position', [100, 100, 1200, 500]);

% 存储所有目标函数值用于归一化
all_f1 = [];
all_f2 = [];

%calculate the objective value of particle
fprintf('初始化种群...\n');
for k=1:length(particle_position)
    for j=1:subsize
        [f1, f2] = feval(func_name, particle_position{k,1}(j,1:n_var));
        particle_position{k,1}(j,n_var+1) = f1;
        particle_position{k,1}(j,n_var+2) = f2;
        particle_position{k,j+1} = particle_position{k,1}(j,:);
        
        % 收集目标函数值
        all_f1 = [all_f1; f1];
        all_f2 = [all_f2; f2];
    end
    velocity{k,1}=Vmin(1:subsize,:)+2.*Vmax(1:subsize,:).*rand(subsize,n_var);
    temp_particle=non_domination_scd_sort(particle_position{k,1}, n_obj, n_var);
    tempindex=find(temp_particle(:,n_var+n_obj+1)==1);
    GBA{k,1}=temp_particle(tempindex,1:n_var+n_obj);
    subgbest{k,1}=GBA{k,1}(1,1:n_var+n_obj);
    for j=1:subsize
        if isequal(subgbest{k,1}, particle_position{k,1}(j,:))
            subgbest{k,2}=velocity{k,1}(j,:);
            break
        end
    end
    NBA{k,1}=GBA{k,1};
end
fitcount=Particle_Number;

%main loop
for i=1:Max_Gen
    fprintf('第 %d/%d 代...\n', i, Max_Gen);
    
    %subpopulation evolution
    for k=1:num
        for m=1:subsize
            pbest_m=particle_position{k,m+1};
            pbest=pbest_m(1,:);
            
            %update velocity
            velocity{k,1}(m,:)=iwt.*velocity{k,1}(m,:)+cc(1).*rand(1,n_var).*(pbest(1,1:n_var)-particle_position{k,1}(m,1:n_var))...
                +cc(2).*rand(1,n_var).*(subgbest{k,1}(1,1:n_var)-particle_position{k,1}(m,1:n_var));
            velocity{k,1}(m,:)=(velocity{k,1}(m,:)>mv).*mv+(velocity{k,1}(m,:)<=mv).*velocity{k,1}(m,:);
            velocity{k,1}(m,:)=(velocity{k,1}(m,:)<(-mv)).*(-mv)+(velocity{k,1}(m,:)>=(-mv)).*velocity{k,1}(m,:);
            
            %update position
            particle_position{k,1}(m,1:n_var)=particle_position{k,1}(m,1:n_var)+velocity{k,1}(m,:);
            particle_position{k,1}(m,1:n_var)=((particle_position{k,1}(m,1:n_var)>=VRmin(1,:))&(particle_position{k,1}(m,1:n_var)<=VRmax(1,:))).*particle_position{k,1}(m,1:n_var)...
                +(particle_position{k,1}(m,1:n_var)<VRmin(1,:)).*(VRmin(1,:)+0.25.*(VRmax(1,:)-VRmin(1,:)).*rand(1,n_var))+(particle_position{k,1}(m,1:n_var)>VRmax(1,:)).*(VRmax(1,:)-0.25.*(VRmax(1,:)-VRmin(1,:)).*rand(1,n_var));
            
            %calculate objective space
            [f1, f2] = feval(func_name, particle_position{k,1}(m,1:n_var));
            particle_position{k,1}(m,n_var+1) = f1;
            particle_position{k,1}(m,n_var+2) = f2;
            fitcount=fitcount+1;
            
            % 收集目标函数值
            all_f1 = [all_f1; f1];
            all_f2 = [all_f2; f2];
            
            %estimate pbest
            pbest_m=[particle_position{k,m+1};particle_position{k,1}(m,1:n_var+n_obj)];
            pbest_m=non_domination_scd_sort(pbest_m, n_obj, n_var);
            tempindex=find(pbest_m(:,n_var+n_obj+1)==1);
            pbest_m=pbest_m(tempindex,1:n_var+n_obj);
            if ismember(particle_position{k,m+1}(1,1:n_var+n_obj),pbest_m,'rows')==1
                particle_position{k,m+1}=[particle_position{k,m+1}(1,1:n_var+n_obj);pbest_m];
                particle_position{k,m+1}=unique(particle_position{k,m+1},'rows');
            else
                particle_position{k,m+1}=pbest_m;
            end
            
            [row_pbest_m,~]=size(pbest_m);
            if row_pbest_m>n_PBA
                pbest_m=pbest_m(1:n_PBA,1:n_var+n_obj);
                particle_position{k,m+1}=pbest_m;
            else
                particle_position{k,m+1}=pbest_m(:,1:n_var+n_obj);
            end
            
            %update gbest
            gbest_m=[particle_position{k,m+1};subgbest{k,1}];
            gbest_m=non_domination_scd_sort(gbest_m, n_obj, n_var);
            tempindex=find(gbest_m(:,n_var+n_obj+1)==1);
            gbest_m=gbest_m(tempindex,1:n_var+n_obj);
            if ismember(subgbest{k,1},gbest_m,'rows')==0
                subgbest{k,1}=gbest_m(1,:);
                subgbest{k,2}=velocity{k,1}(m,:);
            end
            
            gbest_m=[particle_position{k,1}(m,:);subgbest{k,1}];
            gbest_m=non_domination_scd_sort(gbest_m, n_obj, n_var);
            tempindex=find(gbest_m(:,n_var+n_obj+1)==1);
            gbest_m=gbest_m(tempindex,1:n_var+n_obj);
            if ismember(particle_position{k,1}(m,1:n_var+n_obj),gbest_m,'rows')==1
                subgbest{k,1}=particle_position{k,1}(m,:);
                subgbest{k,2}=velocity{k,1}(m,:);
            end
            
            %update GBA Archives
            temp_GBA_k=[GBA{k,1};particle_position{k,1}(m,1:n_var+n_obj);particle_position{k,m+1}];
            temp_GBA_k=unique(temp_GBA_k,'rows');
            temp_GBA_k=non_domination_scd_sort(temp_GBA_k,n_obj,n_var);
            tempindex=find(temp_GBA_k(:,n_var+n_obj+1)==1);
            temp_GBA_k=temp_GBA_k(tempindex,1:n_var+n_obj);
            if length(tempindex)>n_GBA
                GBA{k,1}=temp_GBA_k(tempindex(1:n_GBA),1:n_var+n_obj);
            else
                GBA{k,1}=temp_GBA_k;
            end
        end
    end
    
    %update NBA
    for k=1:num
        if k==1
            tempNBA=GBA{k,1};
            if num > 1
                tempNBA=[tempNBA;GBA{2,1}];
                tempNBA=[tempNBA;GBA{num,1}];
            end
        elseif k==num
            tempNBA=GBA{k,1};
            tempNBA=[tempNBA;GBA{num-1,1}];
            tempNBA=[tempNBA;GBA{1,1}];
        else
            tempNBA=GBA{k,1};
            tempNBA=[tempNBA;GBA{k-1,1}];
            tempNBA=[tempNBA;GBA{k+1,1}];
        end
        tempNBA=unique(tempNBA,'rows');
        NBA_k=tempNBA;
        NBA_k=non_domination_scd_sort(NBA_k, n_obj, n_var);
        index_NBA_k=find(NBA_k(:,n_var+n_obj+1)==1);
        NBA_k=NBA_k(index_NBA_k,1:n_var+n_obj);
        [row_NBA_k,~]=size(NBA_k);
        if row_NBA_k>n_NBA
            NBA{k,1}=NBA_k(1:n_NBA,1:n_var+n_obj);   
        else
            NBA{k,1}=NBA_k(:,1:n_var+n_obj);
        end
    end
    
    %local search
    for k=1:num
        if ~isempty(GBA{k,1})
            pbest=GBA{k,1}(1,1:n_var+n_obj);
            
            %update velocity
            temp_vel_subgbest=iwt.*subgbest{k,2}+cc(1).*rand(1,n_var).*(pbest(1,1:n_var)-subgbest{k,1}(1,1:n_var))...
                +cc(2).*rand(1,n_var).*(NBA{k,1}(1,1:n_var)-subgbest{k,1}(1,1:n_var));
            temp_vel_subgbest=(temp_vel_subgbest>mv).*mv+(temp_vel_subgbest<=mv).*temp_vel_subgbest;
            temp_vel_subgbest=(temp_vel_subgbest<(-mv)).*(-mv)+(temp_vel_subgbest>=(-mv)).*temp_vel_subgbest;
            
            %update position
            temp_position=subgbest{k,1}(1,1:n_var)+subgbest{k,2};
            temp_position=((temp_position(1,:)>=VRmin(1,:))&(temp_position(1,:)<=VRmax(1,:))).*temp_position(1,:)...
                +(temp_position(1,:)<VRmin(1,:)).*(VRmin(1,:)+0.25.*(VRmax(1,:)-VRmin(1,:)).*rand(1,n_var))+(temp_position(1,:)>VRmax(1,:)).*(VRmax(1,:)-0.25.*(VRmax(1,:)-VRmin(1,:)).*rand(1,n_var));
            
            [f1, f2] = feval(func_name, temp_position(1,1:n_var));
            temp_position(:,n_var+1) = f1;
            temp_position(:,n_var+2) = f2;
            
            % 收集目标函数值
            all_f1 = [all_f1; f1];
            all_f2 = [all_f2; f2];
            
            %update the gbest of each subpopulation
            temp_subgbest=[temp_position;subgbest{k,1};GBA{k,1}];
            temp_subgbest=non_domination_scd_sort(temp_subgbest,n_obj,n_var);
            indextemp=find(temp_subgbest(:,n_var+n_obj+1)==1);
            temp_subgbest=temp_subgbest(indextemp,1:n_var+n_obj);
            if ismember(temp_position,temp_subgbest,'rows')==1
                GBA{k,1}=temp_subgbest;
                subgbest{k,1}=GBA{k,1}(1,:);
                subgbest{k,2}=temp_vel_subgbest;
            end
            
            fitcount=fitcount+1;
        end
    end
    
    % 实时绘制Pareto前沿 (包含归一化)
    if mod(i, 5) == 0 || i == Max_Gen
        plot_pareto_front_v2(GBA, n_var, n_obj, i, all_f1, all_f2);
    end
   
    if fitcount>Max_FES
        break;
    end
end

%output non-dominated solution
tempEXA=cell2mat(GBA);
if ~isempty(tempEXA)
    tempEXA=unique(tempEXA,'rows');
    tempEXA=non_domination_scd_sort(tempEXA(:,1:n_var+n_obj), n_obj, n_var);
    tempindex=find(tempEXA(:,n_var+n_obj+1)==1);
    len2=length(tempindex);
    if len2>Particle_Number
        ps=tempEXA(1:Particle_Number,1:n_var);
        pf=tempEXA(1:Particle_Number,n_var+1:n_var+n_obj);
    else
        ps=tempEXA(tempindex,1:n_var);
        pf=tempEXA(tempindex,n_var+1:n_var+n_obj);
    end
else
    ps = [];
    pf = [];
end

end

function plot_pareto_front_v2(GBA, n_var, n_obj, generation, all_f1, all_f2)
% 实时绘制Pareto前沿 (包含归一化)
tempEXA = cell2mat(GBA);
if isempty(tempEXA)
    return;
end

tempEXA = unique(tempEXA, 'rows');
tempEXA = non_domination_scd_sort(tempEXA(:,1:n_var+n_obj), n_obj, n_var);
tempindex = find(tempEXA(:,n_var+n_obj+1)==1);

if ~isempty(tempindex)
    pf_current = tempEXA(tempindex, n_var+1:n_var+n_obj);
    
    % 计算归一化参数
    f1_min = min(all_f1);
    f1_max = max(all_f1);
    f2_min = min(all_f2);
    f2_max = max(all_f2);
    
    % 归一化
    if f1_max > f1_min
        pf_norm_1 = (pf_current(:,1) - f1_min) / (f1_max - f1_min);
    else
        pf_norm_1 = 0.5 * ones(size(pf_current,1), 1);
    end
    
    if f2_max > f2_min
        pf_norm_2 = (pf_current(:,2) - f2_min) / (f2_max - f2_min);
    else
        pf_norm_2 = 0.5 * ones(size(pf_current,1), 1);
    end
    
    figure(2);
    
    % 原始值
    subplot(1,2,1);
    clf(subplot(1,2,1));
    scatter(pf_current(:,1)/10000, pf_current(:,2)/10000, 50, 'filled', 'MarkerFaceColor', 'blue');
    xlabel('储能全寿命周期成本 (万元)');
    ylabel('系统运行成本 (万元)');
    title(sprintf('Pareto前沿 (原始值) - 第%d代', generation));
    grid on;
    
    % 归一化值
    subplot(1,2,2);
    clf(subplot(1,2,2));
    scatter(pf_norm_1, pf_norm_2, 50, 'filled', 'MarkerFaceColor', 'blue');
    hold on;
    scatter(0, 0, 100, 'filled', 'MarkerFaceColor', 'green', 'MarkerEdgeColor', 'black'); % 理想点
    xlabel('归一化储能全寿命周期成本');
    ylabel('归一化系统运行成本');
    title(sprintf('Pareto前沿 (归一化) - 第%d代', generation));
    legend('Pareto解', '理想点', 'Location', 'best');
    grid on;
    xlim([-0.1, 1.1]);
    ylim([-0.1, 1.1]);
    
    drawnow;
end
end

function f = non_domination_scd_sort(x, n_obj, n_var)
% 非支配排序
[N_particle, ~] = size(x);
if N_particle == 0
    f = [];
    return;
end

front = 1;
F(front).f = [];
individual = [];

for i = 1 : N_particle
    individual(i).n = 0; 
    individual(i).p = [];
    for j = 1 : N_particle
        dom_less = 0;
        dom_equal = 0;
        dom_more = 0;
        for k = 1 : n_obj
            if (x(i,n_var + k) < x(j,n_var + k))
                dom_less = dom_less + 1;
            elseif (x(i,n_var + k) == x(j,n_var + k))  
                dom_equal = dom_equal + 1;
            else
                dom_more = dom_more + 1;
            end
        end
        if dom_less == 0 && dom_equal ~= n_obj
            individual(i).n = individual(i).n + 1;
        elseif dom_more == 0 && dom_equal ~= n_obj
            individual(i).p = [individual(i).p j];
        end
    end   
    if individual(i).n == 0
        x(i,n_obj + n_var + 1) = 1;
        F(front).f = [F(front).f i];
    end
end

while ~isempty(F(front).f)
   Q = [];
   for i = 1 : length(F(front).f)
       if ~isempty(individual(F(front).f(i)).p)
            for j = 1 : length(individual(F(front).f(i)).p)
                individual(individual(F(front).f(i)).p(j)).n = ...
                    individual(individual(F(front).f(i)).p(j)).n - 1;
                if individual(individual(F(front).f(i)).p(j)).n == 0
                    x(individual(F(front).f(i)).p(j),n_obj + n_var + 1) = ...
                        front + 1;
                    Q = [Q individual(F(front).f(i)).p(j)];
                end
           end
       end
   end
   front =  front + 1;
   F(front).f = Q;
end

[~,index_of_fronts] = sort(x(:,n_obj + n_var + 1));
for i = 1 : length(index_of_fronts)
    sorted_based_on_front(i,:) = x(index_of_fronts(i),:);
end
current_index = 0;

for front = 1 : (length(F) - 1)
    if isempty(F(front).f)
        continue;
    end
    
    y = [];
    previous_index = current_index + 1;
    for i = 1 : length(F(front).f)
        y(i,:) = sorted_based_on_front(current_index + i,:);
    end
    current_index = current_index + i;
    
    % 简化的拥挤距离计算
    crowd_dist = zeros(length(F(front).f),1);
    for i = 1 : n_obj
        [~, index_of_objectives] = sort(y(:,n_var+i));
        if length(index_of_objectives) > 2
            y(index_of_objectives(1),n_obj + n_var + 2) = inf;
            y(index_of_objectives(end),n_obj + n_var + 2) = inf;
            for j = 2 : length(index_of_objectives) - 1
                distance = y(index_of_objectives(j+1),n_var+i) - y(index_of_objectives(j-1),n_var+i);
                y(index_of_objectives(j),n_obj + n_var + 2) = y(index_of_objectives(j),n_obj + n_var + 2) + distance;
            end
        else
            y(:,n_obj + n_var + 2) = inf;
        end
    end
    
    [~,index_sorted_based_crowddist]=sort(y(:,n_obj + n_var + 2),'descend');
    y=y(index_sorted_based_crowddist,:);
    z(previous_index:current_index,:) = y;
end

f = z();
end 