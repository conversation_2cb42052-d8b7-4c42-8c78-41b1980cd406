%% 测试改进后的储能调度策略 - v6版本
% 验证放宽储能边界、改进调度策略、减少负荷侧补偿的效果

clear; clc; close all;

% 全局变量声明
global mpc_base Pwt Ppv price shift_load cut_load Load_ac Load_dc
global N_SL N_CL Nwt Npv ac acq dc ESS_params

% 加载数据
load_data_v6;

% 使用改进后的储能参数
ESS_params.Ce = 600;        % 降低容量成本
ESS_params.Cp = 350;        % 降低功率成本
ESS_params.Cmaint = 50;     % 降低运维成本
ESS_params.ir = 0.015;      
ESS_params.dr = 0.09;       
ESS_params.Y = 10;          
ESS_params.lambda = 0.08;   
ESS_params.Cde = 60;        
ESS_params.E_max = 12000;   % 大幅提高最大容量
ESS_params.P_max = 4000;    % 大幅提高最大功率

fprintf('=== 改进后储能调度策略测试 ===\n\n');

%% 测试1：大容量储能配置
fprintf('【测试1】大容量储能配置效果\n');

% 构造大容量储能解
n_var = 6 + N_SL + 24*N_CL + 24*N_CL;
X_large = zeros(1, n_var);

% 储能配置：大容量、高功率
X_large(1:2) = [15, 25];        % 储能位置
X_large(3:4) = [6000, 4000];    % 储能容量6MWh和4MWh
X_large(5:6) = [3, 2.5];        % 功率容量配比3h和2.5h (高功率响应)

% 其他变量设置为合理值
idx = 7;
% 平移负荷时间设置为谷期
if N_SL > 0
    X_large(idx:idx+N_SL-1) = [2, 3, 4, 5, 6]; % 平移到深谷期
    idx = idx + N_SL;
end

% 削减负荷量和开关设置为最小
X_large(idx:end) = 0.1; % 最小削减负荷依赖

[f1_large, f2_large, cost_details_large] = ESS_objective_v6(X_large);

fprintf('大容量储能配置结果:\n');
fprintf('储能全寿命周期成本: %.2f 万元\n', f1_large/10000);
fprintf('系统运行成本: %.2f 万元\n', f2_large/10000);
fprintf('弃风弃光成本: %.2f 万元\n', cost_details_large.curtail_cost/10000);

if isfield(cost_details_large, 'renewable_stats')
    fprintf('风电利用率: %.1f%% (弃风率: %.1f%%)\n', ...
        cost_details_large.renewable_stats.wind_utilization_rate, ...
        cost_details_large.renewable_stats.wind_curtailment_rate);
    fprintf('光伏利用率: %.1f%% (弃光率: %.1f%%)\n', ...
        cost_details_large.renewable_stats.pv_utilization_rate, ...
        cost_details_large.renewable_stats.pv_curtailment_rate);
end

if isfield(cost_details_large, 'ess_utilization')
    fprintf('储能1利用率: %.1f%% (充放电次数: %.1f次/天)\n', ...
        cost_details_large.ess_utilization.ess1_rate*100, ...
        cost_details_large.ess_utilization.ess1_cycles);
    fprintf('储能2利用率: %.1f%% (充放电次数: %.1f次/天)\n', ...
        cost_details_large.ess_utilization.ess2_rate*100, ...
        cost_details_large.ess_utilization.ess2_cycles);
end

fprintf('负荷侧补偿成本: %.3f 万元 (平移%.3f + 削减%.3f)\n', ...
    (cost_details_large.C_SL + cost_details_large.C_CL)/10000, ...
    cost_details_large.C_SL/10000, cost_details_large.C_CL/10000);

%% 测试2：小容量储能配置对比
fprintf('\n【测试2】小容量储能配置对比\n');

X_small = X_large;
X_small(3:4) = [1000, 800];     % 小容量1MWh和0.8MWh
X_small(5:6) = [4, 5];          % 功率容量配比4h和5h (低功率响应)

[f1_small, f2_small, cost_details_small] = ESS_objective_v6(X_small);

fprintf('小容量储能配置结果:\n');
fprintf('储能全寿命周期成本: %.2f 万元\n', f1_small/10000);
fprintf('系统运行成本: %.2f 万元\n', f2_small/10000);
fprintf('弃风弃光成本: %.2f 万元\n', cost_details_small.curtail_cost/10000);

if isfield(cost_details_small, 'renewable_stats')
    fprintf('风电利用率: %.1f%% (弃风率: %.1f%%)\n', ...
        cost_details_small.renewable_stats.wind_utilization_rate, ...
        cost_details_small.renewable_stats.wind_curtailment_rate);
    fprintf('光伏利用率: %.1f%% (弃光率: %.1f%%)\n', ...
        cost_details_small.renewable_stats.pv_utilization_rate, ...
        cost_details_small.renewable_stats.pv_curtailment_rate);
end

%% 测试3：调度策略效果分析
fprintf('\n【测试3】调度策略效果分析\n');

% 分析储能充放电时段分布
if isfield(cost_details_large, 'hourly_data') && isfield(cost_details_large.hourly_data, 'ess_power')
    ess_power = cost_details_large.hourly_data.ess_power;
    
    % 统计各时段充放电情况
    morning_peak_discharge = sum(max(0, ess_power(10:12, :)), 'all');  % 上午高峰放电
    evening_peak_discharge = sum(max(0, ess_power(18:21, :)), 'all');  % 晚高峰放电
    pv_period_charge = sum(max(0, -ess_power(8:16, :)), 'all');        % 光伏期充电
    valley_charge = sum(max(0, -ess_power([1:7, 23:24], :)), 'all');   % 谷期充电
    
    fprintf('储能调度时段分析:\n');
    fprintf('上午高峰放电: %.2f MWh\n', morning_peak_discharge);
    fprintf('晚高峰放电: %.2f MWh\n', evening_peak_discharge);
    fprintf('光伏期充电: %.2f MWh\n', pv_period_charge);
    fprintf('谷期充电: %.2f MWh\n', valley_charge);
    
    % 验证调度策略合理性
    total_discharge = morning_peak_discharge + evening_peak_discharge;
    total_charge = pv_period_charge + valley_charge;
    
    fprintf('总放电量: %.2f MWh, 总充电量: %.2f MWh\n', total_discharge, total_charge);
    fprintf('充放电效率: %.1f%%\n', total_discharge/total_charge*100);
    
    % 检查高峰期是否有充电（不合理行为）
    peak_charge = sum(max(0, -ess_power([10:12, 18:21], :)), 'all');
    if peak_charge > 0.01
        fprintf('⚠ 警告：高峰期仍有%.3f MWh充电，调度策略需要优化\n', peak_charge);
    else
        fprintf('✓ 高峰期无充电，调度策略合理\n');
    end
    
    % 检查光伏期是否有放电（可能不合理）
    pv_discharge = sum(max(0, ess_power(8:16, :)), 'all');
    if pv_discharge > total_discharge * 0.3
        fprintf('⚠ 警告：光伏期放电过多(%.2f MWh)，可能影响弃电消纳\n', pv_discharge);
    else
        fprintf('✓ 光伏期放电适度(%.2f MWh)，有利于弃电消纳\n', pv_discharge);
    end
end

%% 测试4：经济性分析
fprintf('\n【测试4】经济性分析\n');

% 计算储能投资回收期
curtail_reduction = cost_details_small.curtail_cost - cost_details_large.curtail_cost;
annual_ess_cost_diff = (f1_large - f1_small) / ESS_params.Y;

fprintf('大容量vs小容量储能经济性对比:\n');
fprintf('储能投资增量: %.2f 万元\n', (f1_large - f1_small)/10000);
fprintf('年化投资增量: %.2f 万元/年\n', annual_ess_cost_diff/10000);
fprintf('弃电成本减少: %.2f 万元/年\n', curtail_reduction*365/10000);
fprintf('系统运行成本减少: %.2f 万元/年\n', (f2_small - f2_large)*365/10000);

total_annual_benefit = curtail_reduction*365 + (f2_small - f2_large)*365;
if total_annual_benefit > 0
    payback_period = annual_ess_cost_diff / total_annual_benefit;
    fprintf('投资回收期: %.1f 年\n', payback_period);
    
    if payback_period < ESS_params.Y
        fprintf('✓ 大容量储能经济性良好，回收期小于寿命期\n');
    else
        fprintf('⚠ 大容量储能经济性一般，回收期较长\n');
    end
else
    fprintf('✗ 大容量储能经济性差，无法回收投资\n');
end

%% 测试5：VSC协同效果验证
fprintf('\n【测试5】VSC协同效果验证\n');

% 通过对比不同时段的VSC传输功率变化，验证协同效果
% 这里主要通过弃电率变化来间接验证

if isfield(cost_details_large, 'renewable_stats') && isfield(cost_details_small, 'renewable_stats')
    large_curtail_rate = cost_details_large.renewable_stats.wind_curtailment_rate + ...
                        cost_details_large.renewable_stats.pv_curtailment_rate;
    small_curtail_rate = cost_details_small.renewable_stats.wind_curtailment_rate + ...
                        cost_details_small.renewable_stats.pv_curtailment_rate;
    
    fprintf('VSC-储能协同效果分析:\n');
    fprintf('大容量储能总弃电率: %.1f%%\n', large_curtail_rate/2);
    fprintf('小容量储能总弃电率: %.1f%%\n', small_curtail_rate/2);
    fprintf('弃电率降低: %.1f%%\n', (small_curtail_rate - large_curtail_rate)/2);
    
    if large_curtail_rate < small_curtail_rate
        fprintf('✓ 大容量储能有效降低弃电率，VSC协同效果良好\n');
    else
        fprintf('⚠ 储能容量增加未能有效降低弃电率，需要优化协同策略\n');
    end
end

%% 总结
fprintf('\n=== 改进效果总结 ===\n');
fprintf('1. 储能容量边界：从0.5-8MWh扩展到2-12MWh\n');
fprintf('2. 储能功率配比：从1.5-6h优化到2-4h，提高功率响应\n');
fprintf('3. 调度策略：高峰强制放电、光伏期积极充电、谷期价格驱动\n');
fprintf('4. 负荷侧补偿：提高补偿成本，激励优先使用储能\n');
fprintf('5. VSC协同：储能-VSC联合调度，提高系统整体效率\n');
fprintf('6. 算法参数：种群300、迭代100次，提高收敛性\n');

fprintf('\n建议运行完整优化：ESS_siting_sizing_main_v6\n'); 