%% 验证修正后的调度逻辑
% 详细测试柔性负荷调度和储能调度的修正效果

clear; clc; close all;

% 全局变量声明
global mpc_base Pwt Ppv price shift_load cut_load Load_ac Load_dc
global N_SL N_CL Nwt Npv ac acq dc ESS_params

% 储能参数设置
ESS_params.Ce = 700;        % 储能单位容量成本 (元/kWh)
ESS_params.Cp = 400;        % 储能单位功率成本 (元/kW)
ESS_params.Cmaint = 60;     % 年运行维护成本 (元/kW·年)
ESS_params.ir = 0.015;      % 通胀膨胀率
ESS_params.dr = 0.09;       % 贴现率
ESS_params.Y = 10;          % 储能寿命 (年)
ESS_params.lambda = 0.08;   % 净报废成本系数
ESS_params.Cde = 60;        % 单位容量报废处置成本 (元/kWh)
ESS_params.E_max = 8000;    % 最大安装容量 (kWh)
ESS_params.P_max = 2000;    % 最大充放电功率 (kW)

% 加载数据
load_data_v6;

fprintf('=== 验证修正后的调度逻辑 ===\n');

%% 创建测试解
n_var = 6 + N_SL + 24*N_CL + 24*N_CL;
X_test = zeros(1, n_var);

% 储能参数（基于实际优化结果）
X_test(1) = 21;        % ESS1节点
X_test(2) = 4;         % ESS2节点
X_test(3) = 1102;      % ESS1容量 (kWh)
X_test(4) = 1120;      % ESS2容量 (kWh)
X_test(5) = 2.68;      % ESS1小时比
X_test(6) = 1.89;      % ESS2小时比

% 平移负荷时间 - 验证只从高峰期转移到低谷期
fprintf('\n=== 平移负荷验证 ===\n');
idx = 7;
for i = 1:N_SL
    orig_start = shift_load(i,4);
    orig_end = shift_load(i,5);
    fprintf('平移负荷%d: 原始时段 %d-%d', i, orig_start, orig_end);
    
    % 检查是否在高峰期
    is_peak = (orig_start >= 10 && orig_start <= 12) || (orig_start >= 18 && orig_start <= 21);
    
    if is_peak
        X_test(idx) = 3;  % 转移到凌晨3点
        fprintf(' -> 高峰期，转移到低谷期(3点)\n');
    else
        X_test(idx) = orig_start;  % 保持原时间
        fprintf(' -> 非高峰期，保持不变\n');
    end
    idx = idx + 1;
end

% 削减负荷量 - 验证只在高峰期削减
fprintf('\n=== 削减负荷验证 ===\n');
peak_reduction_total = 0;
non_peak_reduction_total = 0;

for t = 1:24
    is_peak_hour = (t >= 10 && t <= 12) || (t >= 18 && t <= 21);
    
    for i = 1:N_CL
        if is_peak_hour
            X_test(idx) = cut_load(i,t+1) * 0.8;  % 高峰时段削减80%
            peak_reduction_total = peak_reduction_total + X_test(idx);
        else
            X_test(idx) = cut_load(i,t+1) * 0.1;  % 非高峰时段只削减10%
            non_peak_reduction_total = non_peak_reduction_total + X_test(idx);
        end
        idx = idx + 1;
    end
end

fprintf('高峰期削减总量: %.3f MW\n', peak_reduction_total);
fprintf('非高峰期削减总量: %.3f MW\n', non_peak_reduction_total);
fprintf('高峰期/非高峰期削减比例: %.1f:1\n', peak_reduction_total/non_peak_reduction_total);

% 削减负荷开关 - 主要在高峰期开启
for t = 1:24
    is_peak_hour = (t >= 10 && t <= 12) || (t >= 18 && t <= 21);
    for i = 1:N_CL
        if is_peak_hour
            X_test(idx) = 5;   % 高概率开启
        else
            X_test(idx) = -5;  % 低概率开启
        end
        idx = idx + 1;
    end
end

%% 测试目标函数
fprintf('\n=== 目标函数测试 ===\n');
[f1, f2, cost_details] = ESS_objective_v6(X_test);

fprintf('储能全寿命周期成本: %.2f 万元\n', f1/10000);
fprintf('系统运行成本: %.2f 万元\n', f2/10000);
fprintf('弃风弃光成本: %.2f 万元 (%.1f%%)\n', cost_details.curtail_cost/10000, ...
    cost_details.renewable_stats.wind_curtailment_rate);

%% 分析储能调度结果
fprintf('\n=== 储能调度验证 ===\n');
hourly_data = cost_details.hourly_data;
ess_power = hourly_data.ess_power;

% 检查各时段的储能调度
fprintf('时段分析:\n');
for t = [3, 8, 11, 14, 19, 23]  % 选择代表性时段
    if t <= 7
        period_name = '低谷期';
    elseif t >= 8 && t <= 16
        period_name = '光伏期';
    elseif (t >= 10 && t <= 12) || (t >= 18 && t <= 21)
        period_name = '高峰期';
    else
        period_name = '其他';
    end
    
    ess1_power = ess_power(t,1);
    ess2_power = ess_power(t,2);
    total_ess = ess1_power + ess2_power;
    
    if total_ess > 0.01
        action = '放电';
    elseif total_ess < -0.01
        action = '充电';
    else
        action = '待机';
    end
    
    fprintf('%02d:00 (%s): ESS1=%.3f MW, ESS2=%.3f MW, 总计=%.3f MW (%s)\n', ...
        t, period_name, ess1_power, ess2_power, total_ess, action);
end

%% 验证高峰期放电
evening_peak_discharge = 0;
morning_peak_discharge = 0;
for t = 18:21
    evening_peak_discharge = evening_peak_discharge + sum(max(ess_power(t,:), 0));
end
for t = 10:12
    morning_peak_discharge = morning_peak_discharge + sum(max(ess_power(t,:), 0));
end

fprintf('\n晚高峰期(18-21)总放电量: %.3f MWh\n', evening_peak_discharge);
fprintf('上午高峰期(10-12)总放电量: %.3f MWh\n', morning_peak_discharge);

if evening_peak_discharge > 0.5
    fprintf('✓ 晚高峰期放电正常\n');
else
    fprintf('✗ 晚高峰期放电不足\n');
end

%% 验证光伏期充电
pv_period_charge = 0;
for t = 8:16
    pv_period_charge = pv_period_charge + sum(max(-ess_power(t,:), 0));
end
fprintf('光伏期(8-16)总充电量: %.3f MWh\n', pv_period_charge);

if pv_period_charge > 1.0
    fprintf('✓ 光伏期充电正常\n');
else
    fprintf('✗ 光伏期充电不足\n');
end

%% 绘制结果
fprintf('\n=== 绘制验证结果 ===\n');
try
    plot_dispatch_results_v6(X_test, ac, dc, hourly_data.wind_actual, hourly_data.pv_actual, ...
        hourly_data.ess_power, hourly_data.P_curtail, hourly_data.P_import, ...
        X_test(1), X_test(2), X_test(3), X_test(4));
    fprintf('✓ 绘图完成\n');
catch ME
    fprintf('✗ 绘图失败: %s\n', ME.message);
end

fprintf('\n=== 验证完成 ===\n'); 