% function:修正节点电压
function [ dU,dcita ] = Correct( n,nPQ,U,dP,dQ,J,W )
    dcita=zeros(1,n);
    dU=zeros(1,n);
    %求解节点电压修正量
    Ud2=zeros(nPQ);
    for i=1:nPQ
        Ud2(i,i)=U(i);
    end
    dPQ=[dP dQ]';
    % 将所有NaN替换为0
    
    J(isnan(J)) = 0;
    
    % 将所有Inf替换为0
    J(isinf(J)) = 0;
    
    dUcita=(pinv(J)*dPQ)';
    dcita(1:n-1)=dUcita(1:n-1);
    dU(1:nPQ)=(Ud2*dUcita(n:n+nPQ-1)')';
    for i=1:n-1%直流节点不存在Q与Q的变化
        if W(i)==1
            dcita(i)=0;
        end
    end 
    end