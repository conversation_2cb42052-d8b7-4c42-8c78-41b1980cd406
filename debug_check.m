%% 调试脚本：检查风光和负荷数据
clear all; clc;

% 加载数据
power_load;

% 风光基准容量定义 (MW)
wind_base = [1.2, 1.0, 1.5]; % 各风电场额定容量
pv_base = [0.8, 1.0, 1.0];   % 各光伏电站额定容量

% 构建风光数据
Pwt = [Pwt1' Pwt2' Pwt3'];
Ppv = [Ppv1' Ppv2' Ppv3'];

fprintf('=== 数据检查 ===\n');
fprintf('风电数据范围: [%.3f, %.3f]\n', min(Pwt(:)), max(Pwt(:)));
fprintf('光伏数据范围: [%.3f, %.3f]\n', min(Ppv(:)), max(Ppv(:)));
fprintf('交流负荷范围: [%.3f, %.3f] MW\n', min(Load_ac), max(Load_ac));
fprintf('直流负荷范围: [%.3f, %.3f] MW\n', min(Load_dc), max(Load_dc));

% 计算预测出力
total_wind_forecast = 0;
total_pv_forecast = 0;
total_load = 0;

for t = 1:24
    wind_forecast = sum(Pwt(t,:) .* wind_base);
    pv_forecast = sum(Ppv(t,:) .* pv_base);
    load_t = Load_ac(t) + Load_dc(t);
    
    total_wind_forecast = total_wind_forecast + wind_forecast;
    total_pv_forecast = total_pv_forecast + pv_forecast;
    total_load = total_load + load_t;
    
    if t <= 5 || t >= 12 && t <= 16
        fprintf('时刻%02d: 风电%.3fMW, 光伏%.3fMW, 负荷%.3fMW\n', ...
            t, wind_forecast, pv_forecast, load_t);
    end
end

fprintf('\n=== 24小时统计 ===\n');
fprintf('总风电预测出力: %.2f MWh\n', total_wind_forecast);
fprintf('总光伏预测出力: %.2f MWh\n', total_pv_forecast);
fprintf('总负荷: %.2f MWh\n', total_load);
fprintf('风光总预测: %.2f MWh (占负荷%.1f%%)\n', ...
    total_wind_forecast + total_pv_forecast, ...
    (total_wind_forecast + total_pv_forecast)/total_load*100);

% 检查一个典型的决策变量边界
fprintf('\n=== 决策变量边界检查 ===\n');
for t = 12:16  % 检查中午时段
    fprintf('时刻%02d边界: 风电[0,%.3f], 光伏[0,%.3f]\n', ...
        t, sum(Pwt(t,:) .* wind_base), sum(Ppv(t,:) .* pv_base));
end 