function Obj = MMF6(Var)
% 1<=x1<=3    -1<=x2<=2
   
   Obj = zeros(2,1);
    if (Var(2)>-1&&Var(2)<=0)&&(((Var(1)>7/6&&Var(1)<=8/6))||(Var(1)>9/6&&Var(1)<=10/6)||(Var(1)>11/6&&Var(1)<=2))
      Var(2)=Var(2);
        elseif (Var(2)>-1&&Var(2)<=0)&&((Var(1)>2&&Var(1)<=13/6)||(Var(1)>14/6&&Var(1)<=15/6)||(Var(1)>16/6&&Var(1)<=17/6))
          Var(2)=Var(2);

        elseif (Var(2)>1&&Var(2)<=2)&&((Var(1)>1&&Var(1)<=7/6)||(Var(1)>4/3&&Var(1)<=3/2)||(Var(1)>5/3&&Var(1)<=11/6))
       Var(2)=Var(2)-1;
         

        elseif (Var(2)>1&&Var(2)<=2)&&((Var(1)>13/6&&Var(1)<=14/6)||(Var(1)>15/6&&Var(1)<=16/6)||(Var(1)>17/6&&Var(1)<=3))
        Var(2)=Var(2)-1;
        
        elseif (Var(2)>0&&Var(2)<=1)&&((Var(1)>1&&Var(1)<=7/6)||(Var(1)>4/3&&Var(1)<=3/2)||(Var(1)>5/3&&Var(1)<=11/6)||(Var(1)>13/6&&Var(1)<=14/6)||(Var(1)>15/6&&Var(1)<=16/6)||(Var(1)>17/6&&Var(1)<=3))
          Var(2)=Var(2);
 
        elseif (Var(2)>0&&Var(2)<=1)&&((Var(1)>7/6&&Var(1)<=8/6)||(Var(1)>9/6&&Var(1)<=10/6)||(Var(1)>11/6&&Var(1)<=2)||(Var(1)>2&&Var(1)<=13/6)||(Var(1)>14/6&&Var(1)<=15/6)||(Var(1)>16/6&&Var(1)<=17/6))
          Var(2)=Var(2)-1;
     end
         Obj(1)      = abs(Var(1)-2);             
         Obj(2)      = 1.0 - sqrt( abs(Var(1)-2)) + 2.0*( Var(2)-sin(6*pi* abs(Var(1)-2)+pi)).^2; 
end