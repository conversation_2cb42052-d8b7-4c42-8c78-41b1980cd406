% function: 计算雅可比矩阵
function [ J ] = Jacob<PERSON>( n,nPQ,U,cita,B,G,~,~ ,T,D,M,M_cita,W)
    %雅可比矩阵的计算
    %分块 H N K L
    %i!=j时          （需要去除平衡节点，默认节点1为平衡节点）
    for i=1:n-1%所有节点都有P
        for j=1:n-1
            if j~=i
                H(i,j) = T(i, j) * (1-W(i)) *(1- W(j)) * (1-D(i, j)) * ( - U(i) * U(j) * (G(i, j) * sin(cita(i)-cita(j)) - B(i, j) * cos(cita(i)-cita(j))));
            else
                H1=zeros(1,n);
                for m=1:n
                    if m~=i
                        H1(m)=T(i, m) * (1-W(i)) *(1- W(m)) * (1-D(i, m)) * (-U(i) * U(m) * (-G(i, m) * sin(cita(i)-cita(m)) + B(i, m) * cos(cita(i)-cita(m))));
                    end
                end
                H(i,i)=sum(H1);
            end
        end
    end
    
    
    for i=1:n-1
        for j=1:nPQ
            if j~=i
                a1 = 0.5 * (1 + sign(M(i, j)^-1 * U(i) - M(j, i)^-1 * U(j)));
                b1 = 0.5 * (1 - sign(M(i, j)^-1 * U(i) - M(j, i)^-1 * U(j)));
                a2 = 0.5 * (1 + sign(M(i, j)^-1 * U(i) -U(j)));
                b2 = 0.5 * (1 - sign(M(i, j)^-1 * U(i) -U(j)));
                N(i,j)=T(i, j) * ((1-W(i)) *(1- W(j)) * (1-D(i, j)) * ( - U(i) * U(j) * (G(i, j) * cos(cita(i)-cita(j)) + B(i, j) * sin(cita(i)-cita(j)))) ...
                    + (1-W(i)) *(1- W(j))* D(i, j) * (G(i, j) * ( - M(i, j)^-1 * U(i) * M(j, i)^-1 * U(j))) * (a1 * 0.95^-1 + b1 * 0.95) ...
                    + (1-W(i)) * W(j) * D(i, j) * (G(i, j) * ( - M(i, j)^-1 * U(i) * U(j))) * (a2 * 0.95^-1 + b2 * 0.95) ...
                    + W(i) * (1- W(j)) * D(i, j) * (G(i, j) * ( - U(i) * M(j, i)^-1 * U(j))) ...
                    + W(i) * W(j) * D(i, j) * (G(i, j) * (- U(i) * U(j))));
            else
                N1=zeros(1,n);
                for m=1:n
                    if m~=i
    
                        a1 = 0.5 * (1 + sign(M(i, m)^-1 * U(i) - M(m, i)^-1 * U(m)));
                        b1 = 0.5 * (1 - sign(M(i, m)^-1 * U(i) - M(m, i)^-1 * U(m)));
                        a2 = 0.5 * (1 + sign(M(i, m)^-1 * U(i) -U(m)));
                        b2 = 0.5 * (1 - sign(M(i, m)^-1 * U(i) -U(m)));
                        N1(m)=T(i, m) * ((1-W(i)) *(1- W(m)) * (1-D(i, m)) * (2*U(i)^2*G(i,m) - U(i) * U(m) * (G(i, m) * cos(cita(i)-cita(m)) + B(i, m) * sin(cita(i)-cita(m)))) ...
                            + (1-W(i)) *(1- W(m))* D(i, m) * (G(i, m) * (2*M(i,m)^-2*U(i)^2 - M(i, m)^-1 * U(i) * M(m, i)^-1 * U(m))) * (a1 * 0.95^-1 + b1 * 0.95) ...
                            + (1-W(i)) * W(m) * D(i, m) * (G(i, m) * (2*M(i,m)^-2*U(i)^2 - M(i, m)^-1 * U(i) * U(m))) * (a2 * 0.95^-1 + b2 * 0.95) ...
                            + W(i) * (1- W(m)) * D(i, m) * (G(i, m) * (2*U(i)^2- U(i) * M(m, i)^-1 * U(m))) ...
                            + W(i) * W(m) * D(i, m) * (G(i, m) * (2*U(i)^2- U(i) * U(m))));
                    end
                end
                N(i,i)=sum(N1);
            end
        end
    end
    
    
    for i=1:nPQ
        for j=1:n-1
            if j~=i
                K(i,j)=T(i, j) * (1-W(i)) *(1- W(j)) * (1-D(i, j)) * U(i) * U(j) * (G(i, j) * cos(cita(i)-cita(j)) + B(i, j) * sin(cita(i)-cita(j)));
            else
                K1=zeros(1,n);
                for m=1:n
                    if m~=i
    
                        K1(m)=T(i, m) * (1-W(i)) *(1- W(m)) * (1-D(i, m)) * (- U(i) * U(m) * (G(i, m) * cos(cita(i)-cita(m)) + B(i, m) * sin(cita(i)-cita(m))));
                    end
                end
                K(i,i)=sum(K1);
            end
        end
    end
    
    
    for i=1:nPQ
        for j=1:nPQ
            if j~=i
                a1 = 0.5 * (1 + sign(M(i, j)^-1 * U(i) - M(j, i)^-1 * U(j)));
                b1 = 0.5 * (1 - sign(M(i, j)^-1 * U(i) - M(j, i)^-1 * U(j)));
                a2 = 0.5 * (1 + sign(M(i, j)^-1 * U(i) -U(j)));
                b2 = 0.5 * (1 - sign(M(i, j)^-1 * U(i) -U(j)));
                L(i,j)=T(i, j) * ((1-W(i)) *(1- W(j)) * (1-D(i, j)) * (- U(i) * U(j) * (G(i, j) * sin(cita(i)-cita(j)) - B(i, j) * cos(cita(i)-cita(j)))) ...
                    + (1-W(i)) *(1- W(j))* D(i, j) *(G(i, j) * ( - M(i, j)^-1 * U(i) * M(j, i)^-1 * U(j))) * (a1 * 0.95^-1 + b1 * 0.95)*tan(M_cita(i,j)) ...
                    + (1-W(i)) * W(j) * D(i, j) *(G(i, j) * ( - M(i, j)^-1 * U(i) * U(j))) * (a2 * 0.95^-1 + b2 * 0.95)*tan(M_cita(i,j)));
            else
                L1=zeros(1,n);
                for m=1:n
                    if m~=i
    
                        a1 = 0.5 * (1 + sign(M(i, m)^-1 * U(i) - M(m, i)^-1 * U(m)));
                        b1 = 0.5 * (1 - sign(M(i, m)^-1 * U(i) - M(m, i)^-1 * U(m)));
                        a2 = 0.5 * (1 + sign(M(i, m)^-1 * U(i) -U(m)));
                        b2 = 0.5 * (1 - sign(M(i, m)^-1 * U(i) -U(m)));
                        L1(m)=T(i, m) * ((1-W(i)) *(1- W(m)) * (1-D(i, m)) * (-2* U(i)^2 *B(i, m) - U(i) * U(m) * (G(i, m) * sin(cita(i)-cita(m)) - B(i, m) * cos(cita(i)-cita(m))))...
                            + (1-W(i)) *(1- W(m))* D(i, m) *(G(i, m) * (2*M(i,m)^-2*U(i)^2 - M(i, m)^-1 * U(i) * M(m, i)^-1 * U(m))) * (a1 * 0.95^-1 + b1 * 0.95)*tan(M_cita(i,m)) ...
                            + (1-W(i)) * W(m) * D(i, m) *(G(i, m) * (2*M(i,m)^-2*U(i)^2 - M(i, m)^-1 * U(i) * U(m))) * (a2 * 0.95^-1 + b2 * 0.95)*tan(M_cita(i,m)));
                    end
                end
                L(i,j)=sum(L1);
            end
        end
    end
    
    
    
    %合成雅可比矩阵
    J=[H N;K L];
    
    end