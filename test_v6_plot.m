%% v6绘图功能测试脚本
% 测试修复后的绘图函数

clear; clc; close all;

% 全局变量声明
global mpc_base Pwt Ppv price shift_load cut_load Load_ac Load_dc
global N_SL N_CL Nwt Npv ac acq dc ESS_params

% 储能参数设置
ESS_params.Ce = 400;        
ESS_params.Cp = 250;        
ESS_params.Cmaint = 60;     
ESS_params.ir = 0.015;      
ESS_params.dr = 0.09;       
ESS_params.Y = 10;          
ESS_params.lambda = 0.08;   
ESS_params.E_max = 6000;    
ESS_params.P_max = 1500;    

% 加载数据
load_data_v6;

fprintf('=== v6绘图功能测试 ===\n');

%% 创建模拟的v6最优解
% 基于实际运行结果创建测试解
n_var = 6 + 24 + 24 + 48 + N_SL + 24*N_CL + 24*N_CL;
X_test = zeros(1, n_var);

% 储能参数 (基于实际结果)
X_test(1:2) = [7, 27];              % 储能安装节点
X_test(3:4) = [2167.28, 1039.27];   % 储能容量
X_test(5:6) = [5.19, 5.46];         % 功率容量配比

% 弃电量 (7-30) - 设置较小值
X_test(7:30) = 0.8 * ones(1, 24);

% 购电量 (31-54) - 基于负荷设置
load_profile = Load_ac + Load_dc;
X_test(31:54) = 0.4 * load_profile';  % 购电为负荷的40%

% 储能充放电系数 (55-102)
ess_coeff = zeros(24, 2);
% 设置简单的充放电模式
ess_coeff(9:15, :) = -0.6;   % 白天充电
ess_coeff(18:22, :) = 0.8;   % 晚上放电
X_test(55:102) = reshape(ess_coeff, 1, 48);

% 其余变量设为默认值
idx = 103;
X_test(idx:idx+N_SL-1) = shift_load(:,4)';
X_test(idx+N_SL:end) = 0;

fprintf('测试解构造完成，变量数: %d\n', length(X_test));

%% 创建模拟的cost_details
cost_details = struct();

% 小时级数据
cost_details.hourly_data.wind_actual = rand(24, 3) * 2;  % 模拟风电出力
cost_details.hourly_data.pv_actual = rand(24, 3) * 1.5; % 模拟光伏出力
cost_details.hourly_data.ess_power = (rand(24, 2) - 0.5) * 1; % 模拟储能功率

% 基本成本信息
cost_details.C_i = 1434600;
cost_details.C_m = 251600;
cost_details.C_d = 43300;
cost_details.grid_cost = 129400;
cost_details.curtail_cost = 213200;
cost_details.loss_cost = 4100;

%% 测试绘图功能
try
    fprintf('\n开始测试v6绘图功能...\n');
    plot_dispatch_results_v6(X_test, cost_details, ESS_params);
    fprintf('绘图测试成功！\n');
    
    fprintf('\n生成的图表:\n');
    fprintf('图1: v6储能风光协调调度结果\n');
    fprintf('图2: v6储能充放电功率\n');
    fprintf('图3: v6弃电量与购电量\n');
    
catch ME
    fprintf('绘图测试失败！\n');
    fprintf('错误信息: %s\n', ME.message);
    fprintf('错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
end

fprintf('\n=== v6绘图功能测试完成 ===\n'); 