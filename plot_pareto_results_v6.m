function plot_pareto_results_v6(ps, pf, pf_norm, best_solution, best_idx)
%% v6版本Pareto前沿结果绘图与分析
% 输入：
%   ps - Pareto解集
%   pf - Pareto前沿 (目标函数值)
%   pf_norm - 归一化后的Pareto前沿
%   best_solution - 最优解
%   best_idx - 最优解索引

% 创建图形窗口
figure('Position', [100, 100, 1200, 900]);

%% 子图1：储能选址分布 (决策变量空间)
subplot(2,3,1);
scatter(ps(:,1), ps(:,2), 50, 'o', 'filled', 'MarkerFaceAlpha', 0.7);
hold on;
scatter(best_solution(1), best_solution(2), 100, 'r', 'pentagram', 'filled');
xlabel('储能1安装节点');
ylabel('储能2安装节点');
title('Pareto 解集：储能选址分布 (决策变量空间)');
grid on;
xlim([2, 33]);
ylim([2, 33]);
legend('Pareto解', '最优解', 'Location', 'best');

%% 子图2：储能容量分布 (决策变量空间)
subplot(2,3,2);
scatter(ps(:,3), ps(:,4), 50, 'o', 'filled', 'MarkerFaceAlpha', 0.7);
hold on;
scatter(best_solution(3), best_solution(4), 100, 'r', 'pentagram', 'filled');
xlabel('储能1容量 / MWh');
ylabel('储能2容量 / MWh');
title('Pareto 解集：储能容量分布 (决策变量空间)');
grid on;
% 转换为MWh显示
set(gca, 'XTickLabel', get(gca, 'XTick')/1000);
set(gca, 'YTickLabel', get(gca, 'YTick')/1000);
legend('Pareto解', '最优解', 'Location', 'best');

%% 子图3：原始目标函数值 (目标函数空间)
subplot(2,3,3);
scatter(pf(:,1)/10000, pf(:,2)/10000, 50, 'o', 'filled', 'MarkerFaceAlpha', 0.7);
hold on;
scatter(pf(best_idx,1)/10000, pf(best_idx,2)/10000, 100, 'r', 'pentagram', 'filled');
xlabel('储能全寿命周期成本 / 万元');
ylabel('系统运行成本 / 万元');
title('Pareto 前沿：原始目标函数值 (目标函数空间)');
grid on;
legend('Pareto解', '最优解', 'Location', 'best');

%% 子图4：归一化目标函数值 (目标函数空间)
subplot(2,3,4);
scatter(pf_norm(:,1), pf_norm(:,2), 50, 'o', 'filled', 'MarkerFaceAlpha', 0.7);
hold on;
scatter(pf_norm(best_idx,1), pf_norm(best_idx,2), 100, 'r', 'pentagram', 'filled');
xlabel('归一化储能成本');
ylabel('归一化运行成本');
title('Pareto 前沿：归一化目标函数值 (目标函数空间)');
grid on;
xlim([0, 1]);
ylim([0, 1]);
legend('Pareto解', '最优解', 'Location', 'best');

%% 子图5：收敛性分析
subplot(2,3,5);
% 计算到理想点的距离分布
distances = sqrt(pf_norm(:,1).^2 + pf_norm(:,2).^2);
histogram(distances, 20, 'FaceAlpha', 0.7);
hold on;
xline(distances(best_idx), 'r--', 'LineWidth', 2, 'Label', '最优解距离');
xlabel('到理想点(0,0)的距离');
ylabel('解的数量');
title('收敛性分析：距离分布');
grid on;

%% 子图6：前沿质量评估
subplot(2,3,6);
% 分析Pareto前沿的分布质量
% 计算相邻解之间的距离
if size(pf_norm,1) > 1
    % 按第一个目标函数排序
    [~, sort_idx] = sort(pf_norm(:,1));
    pf_sorted = pf_norm(sort_idx, :);
    
    % 计算相邻点间距离
    distances_between_points = zeros(size(pf_sorted,1)-1, 1);
    for i = 1:size(pf_sorted,1)-1
        distances_between_points(i) = norm(pf_sorted(i+1,:) - pf_sorted(i,:));
    end
    
    plot(1:length(distances_between_points), distances_between_points, 'b-o', 'LineWidth', 1.5);
    xlabel('相邻解对编号');
    ylabel('归一化距离');
    title('前沿均匀性分析：相邻解间距');
    grid on;
    
    % 添加统计信息
    mean_dist = mean(distances_between_points);
    std_dist = std(distances_between_points);
    yline(mean_dist, 'r--', sprintf('平均距离: %.3f', mean_dist));
    yline(mean_dist + std_dist, 'g--', sprintf('+1σ: %.3f', mean_dist + std_dist));
    yline(mean_dist - std_dist, 'g--', sprintf('-1σ: %.3f', mean_dist - std_dist));
end

%% 整体标题
sgtitle('v6 Pareto 解集与前沿对比分析', 'FontSize', 14, 'FontWeight', 'bold');

%% 保存图片
saveas(gcf, 'v6_Pareto前沿分析.png');

%% 收敛性评估报告
fprintf('\n=== Pareto前沿收敛性评估 ===\n');
fprintf('Pareto解数量: %d\n', size(ps,1));

if size(pf_norm,1) > 1
    % 评估前沿的分布性
    spread_f1 = max(pf_norm(:,1)) - min(pf_norm(:,1));
    spread_f2 = max(pf_norm(:,2)) - min(pf_norm(:,2));
    fprintf('目标函数1分布范围: %.3f (归一化)\n', spread_f1);
    fprintf('目标函数2分布范围: %.3f (归一化)\n', spread_f2);
    
    % 评估前沿的均匀性
    if exist('distances_between_points', 'var')
        uniformity = std_dist / mean_dist;
        fprintf('前沿均匀性指标: %.3f (越小越均匀)\n', uniformity);
        
        if uniformity < 0.5
            fprintf('✓ 前沿分布较为均匀\n');
        elseif uniformity < 1.0
            fprintf('△ 前沿分布中等均匀\n');
        else
            fprintf('✗ 前沿分布不均匀，建议增加种群规模或迭代次数\n');
        end
    end
    
    % 评估收敛性
    min_distance = min(distances);
    fprintf('最优解到理想点距离: %.3f\n', distances(best_idx));
    fprintf('所有解到理想点最小距离: %.3f\n', min_distance);
    
    if min_distance < 0.3
        fprintf('✓ 收敛性良好\n');
    elseif min_distance < 0.5
        fprintf('△ 收敛性中等\n');
    else
        fprintf('✗ 收敛性较差，建议增加迭代次数\n');
    end
else
    fprintf('⚠ 只有一个Pareto解，无法评估前沿质量\n');
end

end 