%计算VSC的功率
function [S]=S_vsc( n,U,bus,~,branch)
    branch(:,3) = branch(:,3)+1j*branch(:,4);
    branch(:,4) = [];
    Y=zeros(n); %新建节点导纳矩阵
    y=zeros(n); %网络中的真实导纳
    %计算y(i,j)
    for i=1:size(branch,1) %与交流线联结的真实导纳
        ii=branch(i,1); jj=branch(i,2);
        y(ii,jj)=1/branch(i,3);
        y(jj,ii)=y(ii,jj);
    end
    %计算y(i,i),接地
    % for i=1:size(branch,1) %与交流线联结的对地导纳
    %     ii=branch(i,1); jj=branch(i,2);
    %     y(ii,ii)=y(ii,ii);
    %     y(jj,jj)=y(jj,jj);
    % end
    
    %由y计算Y
    ysum=sum(y,2); %每一行求和
    for i=1:n
        for j=1:n
            if i==j
                Y(i,j)=ysum(i);
            else
                Y(i,j)=-y(i,j);
            end
        end
    end
    
    
    G=real(Y); %电导矩阵
    B=imag(Y); %电纳矩阵
    
    %生成系统中VSC调制矩阵M
    M=ones(33);
    L=find(branch(:, 5) ~= 0);
    % if L==
    for i = 1:size(L)
        from_node = branch(L(i), 1);
        to_node = branch(L(i), 2);
        M_type = branch(L(i), 5);
        M(from_node, to_node) = M_type;
        M(to_node, from_node) = M_type;
    end
    %生成系统中VSC控制角M_cita
    M_cita=ones(33);
    L=find(branch(:, 6) ~= 0);
    for i = 1:size(L)
        from_node = branch(L(i), 1);
        to_node = branch(L(i), 2);
        M_cita1 = branch(L(i), 6);
        M_cita(from_node, to_node) = M_cita1 ;
        M_cita(to_node, from_node) = M_cita1 ;
    end
    
    M_position=find(branch(:, 5) ~= 0);%存在VSC的位置
    M_variable=branch(M_position, 5);
    M_row=branch(M_position,1:2);%VSC位置对应的节点
    ac_position=bus(bus(:,9)==0,1);%ac节点编号
    ac_point = [];
    dc_point=[];
    % 遍历矩阵A的每行
    for i = 1:size(M_row, 1)
        % 遍历矩阵A当前行的元素
        for j = 1:size(M_row, 2)
            % 查找当前元素是否在矩阵B中存在
            if ismember(M_row(i, j), ac_position)
                % 将存在的元素添加到结果矩阵中
                ac_point = [ac_point; M_row(i, j)];
            else
                dc_point= [dc_point; M_row(i, j)];
            end
        end
        P_VSC(i)=(G(ac_point(i), dc_point(i)) * ( M(ac_point(i), dc_point(i)).^-2 * U(ac_point(i)).^2- M(ac_point(i), dc_point(i)).^-1 * U(ac_point(i))  * U(dc_point(i))))/0.95;
        Q_VSC(i)=P_VSC(i)*tan(M_cita(ac_point(i), dc_point(i)));
        S(i)=sqrt(P_VSC(i)^2+Q_VSC(i)^2);
    end
    end
    